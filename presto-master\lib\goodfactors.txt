4    [[2, 2]]
6    [[2, 1], [3, 1]]
8    [[2, 3]]
10   [[2, 1], [5, 1]]
12   [[2, 2], [3, 1]]
14   [[2, 1], [7, 1]]
16   [[2, 4]]
18   [[2, 1], [3, 2]]
20   [[2, 2], [5, 1]]
22   [[2, 1], [11, 1]]
24   [[2, 3], [3, 1]]
28   [[2, 2], [7, 1]]
30   [[2, 1], [3, 1], [5, 1]]
32   [[2, 5]]
36   [[2, 2], [3, 2]]
40   [[2, 3], [5, 1]]
42   [[2, 1], [3, 1], [7, 1]]
44   [[2, 2], [11, 1]]
48   [[2, 4], [3, 1]]
50   [[2, 1], [5, 2]]
54   [[2, 1], [3, 3]]
56   [[2, 3], [7, 1]]
60   [[2, 2], [3, 1], [5, 1]]
64   [[2, 6]]
66   [[2, 1], [3, 1], [11, 1]]
70   [[2, 1], [5, 1], [7, 1]]
72   [[2, 3], [3, 2]]
80   [[2, 4], [5, 1]]
84   [[2, 2], [3, 1], [7, 1]]
88   [[2, 3], [11, 1]]
90   [[2, 1], [3, 2], [5, 1]]
96   [[2, 5], [3, 1]]
98   [[2, 1], [7, 2]]
100  [[2, 2], [5, 2]]
108  [[2, 2], [3, 3]]
110  [[2, 1], [5, 1], [11, 1]]
112  [[2, 4], [7, 1]]
120  [[2, 3], [3, 1], [5, 1]]
126  [[2, 1], [3, 2], [7, 1]]
128  [[2, 7]]
132  [[2, 2], [3, 1], [11, 1]]
140  [[2, 2], [5, 1], [7, 1]]
144  [[2, 4], [3, 2]]
150  [[2, 1], [3, 1], [5, 2]]
154  [[2, 1], [7, 1], [11, 1]]
160  [[2, 5], [5, 1]]
162  [[2, 1], [3, 4]]
168  [[2, 3], [3, 1], [7, 1]]
176  [[2, 4], [11, 1]]
180  [[2, 2], [3, 2], [5, 1]]
192  [[2, 6], [3, 1]]
196  [[2, 2], [7, 2]]
198  [[2, 1], [3, 2], [11, 1]]
200  [[2, 3], [5, 2]]
210  [[2, 1], [3, 1], [5, 1], [7, 1]]
216  [[2, 3], [3, 3]]
220  [[2, 2], [5, 1], [11, 1]]
224  [[2, 5], [7, 1]]
240  [[2, 4], [3, 1], [5, 1]]
242  [[2, 1], [11, 2]]
250  [[2, 1], [5, 3]]
252  [[2, 2], [3, 2], [7, 1]]
256  [[2, 8]]
264  [[2, 3], [3, 1], [11, 1]]
270  [[2, 1], [3, 3], [5, 1]]
280  [[2, 3], [5, 1], [7, 1]]
288  [[2, 5], [3, 2]]
294  [[2, 1], [3, 1], [7, 2]]
300  [[2, 2], [3, 1], [5, 2]]
308  [[2, 2], [7, 1], [11, 1]]
320  [[2, 6], [5, 1]]
324  [[2, 2], [3, 4]]
330  [[2, 1], [3, 1], [5, 1], [11, 1]]
336  [[2, 4], [3, 1], [7, 1]]
350  [[2, 1], [5, 2], [7, 1]]
352  [[2, 5], [11, 1]]
360  [[2, 3], [3, 2], [5, 1]]
378  [[2, 1], [3, 3], [7, 1]]
384  [[2, 7], [3, 1]]
392  [[2, 3], [7, 2]]
396  [[2, 2], [3, 2], [11, 1]]
400  [[2, 4], [5, 2]]
420  [[2, 2], [3, 1], [5, 1], [7, 1]]
432  [[2, 4], [3, 3]]
440  [[2, 3], [5, 1], [11, 1]]
448  [[2, 6], [7, 1]]
450  [[2, 1], [3, 2], [5, 2]]
462  [[2, 1], [3, 1], [7, 1], [11, 1]]
480  [[2, 5], [3, 1], [5, 1]]
484  [[2, 2], [11, 2]]
486  [[2, 1], [3, 5]]
490  [[2, 1], [5, 1], [7, 2]]
500  [[2, 2], [5, 3]]
504  [[2, 3], [3, 2], [7, 1]]
512  [[2, 9]]
528  [[2, 4], [3, 1], [11, 1]]
540  [[2, 2], [3, 3], [5, 1]]
550  [[2, 1], [5, 2], [11, 1]]
560  [[2, 4], [5, 1], [7, 1]]
576  [[2, 6], [3, 2]]
588  [[2, 2], [3, 1], [7, 2]]
594  [[2, 1], [3, 3], [11, 1]]
600  [[2, 3], [3, 1], [5, 2]]
616  [[2, 3], [7, 1], [11, 1]]
630  [[2, 1], [3, 2], [5, 1], [7, 1]]
640  [[2, 7], [5, 1]]
648  [[2, 3], [3, 4]]
660  [[2, 2], [3, 1], [5, 1], [11, 1]]
672  [[2, 5], [3, 1], [7, 1]]
686  [[2, 1], [7, 3]]
700  [[2, 2], [5, 2], [7, 1]]
704  [[2, 6], [11, 1]]
720  [[2, 4], [3, 2], [5, 1]]
726  [[2, 1], [3, 1], [11, 2]]
750  [[2, 1], [3, 1], [5, 3]]
756  [[2, 2], [3, 3], [7, 1]]
768  [[2, 8], [3, 1]]
770  [[2, 1], [5, 1], [7, 1], [11, 1]]
784  [[2, 4], [7, 2]]
792  [[2, 3], [3, 2], [11, 1]]
800  [[2, 5], [5, 2]]
810  [[2, 1], [3, 4], [5, 1]]
840  [[2, 3], [3, 1], [5, 1], [7, 1]]
864  [[2, 5], [3, 3]]
880  [[2, 4], [5, 1], [11, 1]]
882  [[2, 1], [3, 2], [7, 2]]
896  [[2, 7], [7, 1]]
900  [[2, 2], [3, 2], [5, 2]]
924  [[2, 2], [3, 1], [7, 1], [11, 1]]
960  [[2, 6], [3, 1], [5, 1]]
968  [[2, 3], [11, 2]]
972  [[2, 2], [3, 5]]
980  [[2, 2], [5, 1], [7, 2]]
990  [[2, 1], [3, 2], [5, 1], [11, 1]]
1000 [[2, 3], [5, 3]]
1008 [[2, 4], [3, 2], [7, 1]]
1024 [[2, 10]]
1050 [[2, 1], [3, 1], [5, 2], [7, 1]]
1056 [[2, 5], [3, 1], [11, 1]]
1078 [[2, 1], [7, 2], [11, 1]]
1080 [[2, 3], [3, 3], [5, 1]]
1100 [[2, 2], [5, 2], [11, 1]]
1120 [[2, 5], [5, 1], [7, 1]]
1134 [[2, 1], [3, 4], [7, 1]]
1152 [[2, 7], [3, 2]]
1176 [[2, 3], [3, 1], [7, 2]]
1188 [[2, 2], [3, 3], [11, 1]]
1200 [[2, 4], [3, 1], [5, 2]]
1210 [[2, 1], [5, 1], [11, 2]]
1232 [[2, 4], [7, 1], [11, 1]]
1250 [[2, 1], [5, 4]]
1260 [[2, 2], [3, 2], [5, 1], [7, 1]]
1280 [[2, 8], [5, 1]]
1296 [[2, 4], [3, 4]]
1320 [[2, 3], [3, 1], [5, 1], [11, 1]]
1344 [[2, 6], [3, 1], [7, 1]]
1350 [[2, 1], [3, 3], [5, 2]]
1372 [[2, 2], [7, 3]]
1386 [[2, 1], [3, 2], [7, 1], [11, 1]]
1400 [[2, 3], [5, 2], [7, 1]]
1408 [[2, 7], [11, 1]]
1440 [[2, 5], [3, 2], [5, 1]]
1452 [[2, 2], [3, 1], [11, 2]]
1458 [[2, 1], [3, 6]]
1470 [[2, 1], [3, 1], [5, 1], [7, 2]]
1500 [[2, 2], [3, 1], [5, 3]]
1512 [[2, 3], [3, 3], [7, 1]]
1536 [[2, 9], [3, 1]]
1540 [[2, 2], [5, 1], [7, 1], [11, 1]]
1568 [[2, 5], [7, 2]]
1584 [[2, 4], [3, 2], [11, 1]]
1600 [[2, 6], [5, 2]]
1620 [[2, 2], [3, 4], [5, 1]]
1650 [[2, 1], [3, 1], [5, 2], [11, 1]]
1680 [[2, 4], [3, 1], [5, 1], [7, 1]]
1694 [[2, 1], [7, 1], [11, 2]]
1728 [[2, 6], [3, 3]]
1750 [[2, 1], [5, 3], [7, 1]]
1760 [[2, 5], [5, 1], [11, 1]]
1764 [[2, 2], [3, 2], [7, 2]]
1782 [[2, 1], [3, 4], [11, 1]]
1792 [[2, 8], [7, 1]]
1800 [[2, 3], [3, 2], [5, 2]]
1848 [[2, 3], [3, 1], [7, 1], [11, 1]]
1890 [[2, 1], [3, 3], [5, 1], [7, 1]]
1920 [[2, 7], [3, 1], [5, 1]]
1936 [[2, 4], [11, 2]]
1944 [[2, 3], [3, 5]]
1960 [[2, 3], [5, 1], [7, 2]]
1980 [[2, 2], [3, 2], [5, 1], [11, 1]]
2000 [[2, 4], [5, 3]]
2016 [[2, 5], [3, 2], [7, 1]]
2048 [[2, 11]]
2058 [[2, 1], [3, 1], [7, 3]]
2100 [[2, 2], [3, 1], [5, 2], [7, 1]]
2112 [[2, 6], [3, 1], [11, 1]]
2156 [[2, 2], [7, 2], [11, 1]]
2160 [[2, 4], [3, 3], [5, 1]]
2178 [[2, 1], [3, 2], [11, 2]]
2200 [[2, 3], [5, 2], [11, 1]]
2240 [[2, 6], [5, 1], [7, 1]]
2250 [[2, 1], [3, 2], [5, 3]]
2268 [[2, 2], [3, 4], [7, 1]]
2304 [[2, 8], [3, 2]]
2310 [[2, 1], [3, 1], [5, 1], [7, 1], [11, 1]]
2352 [[2, 4], [3, 1], [7, 2]]
2376 [[2, 3], [3, 3], [11, 1]]
2400 [[2, 5], [3, 1], [5, 2]]
2420 [[2, 2], [5, 1], [11, 2]]
2430 [[2, 1], [3, 5], [5, 1]]
2450 [[2, 1], [5, 2], [7, 2]]
2464 [[2, 5], [7, 1], [11, 1]]
2500 [[2, 2], [5, 4]]
2520 [[2, 3], [3, 2], [5, 1], [7, 1]]
2560 [[2, 9], [5, 1]]
2592 [[2, 5], [3, 4]]
2640 [[2, 4], [3, 1], [5, 1], [11, 1]]
2646 [[2, 1], [3, 3], [7, 2]]
2662 [[2, 1], [11, 3]]
2688 [[2, 7], [3, 1], [7, 1]]
2700 [[2, 2], [3, 3], [5, 2]]
2744 [[2, 3], [7, 3]]
2750 [[2, 1], [5, 3], [11, 1]]
2772 [[2, 2], [3, 2], [7, 1], [11, 1]]
2800 [[2, 4], [5, 2], [7, 1]]
2816 [[2, 8], [11, 1]]
2880 [[2, 6], [3, 2], [5, 1]]
2904 [[2, 3], [3, 1], [11, 2]]
2916 [[2, 2], [3, 6]]
2940 [[2, 2], [3, 1], [5, 1], [7, 2]]
2970 [[2, 1], [3, 3], [5, 1], [11, 1]]
3000 [[2, 3], [3, 1], [5, 3]]
3024 [[2, 4], [3, 3], [7, 1]]
3072 [[2, 10], [3, 1]]
3080 [[2, 3], [5, 1], [7, 1], [11, 1]]
3136 [[2, 6], [7, 2]]
3150 [[2, 1], [3, 2], [5, 2], [7, 1]]
3168 [[2, 5], [3, 2], [11, 1]]
3200 [[2, 7], [5, 2]]
3234 [[2, 1], [3, 1], [7, 2], [11, 1]]
3240 [[2, 3], [3, 4], [5, 1]]
3300 [[2, 2], [3, 1], [5, 2], [11, 1]]
3360 [[2, 5], [3, 1], [5, 1], [7, 1]]
3388 [[2, 2], [7, 1], [11, 2]]
3402 [[2, 1], [3, 5], [7, 1]]
3430 [[2, 1], [5, 1], [7, 3]]
3456 [[2, 7], [3, 3]]
3500 [[2, 2], [5, 3], [7, 1]]
3520 [[2, 6], [5, 1], [11, 1]]
3528 [[2, 3], [3, 2], [7, 2]]
3564 [[2, 2], [3, 4], [11, 1]]
3584 [[2, 9], [7, 1]]
3600 [[2, 4], [3, 2], [5, 2]]
3630 [[2, 1], [3, 1], [5, 1], [11, 2]]
3696 [[2, 4], [3, 1], [7, 1], [11, 1]]
3750 [[2, 1], [3, 1], [5, 4]]
3780 [[2, 2], [3, 3], [5, 1], [7, 1]]
3840 [[2, 8], [3, 1], [5, 1]]
3850 [[2, 1], [5, 2], [7, 1], [11, 1]]
3872 [[2, 5], [11, 2]]
3888 [[2, 4], [3, 5]]
3920 [[2, 4], [5, 1], [7, 2]]
3960 [[2, 3], [3, 2], [5, 1], [11, 1]]
4000 [[2, 5], [5, 3]]
4032 [[2, 6], [3, 2], [7, 1]]
4050 [[2, 1], [3, 4], [5, 2]]
4096 [[2, 12]]
4116 [[2, 2], [3, 1], [7, 3]]
4158 [[2, 1], [3, 3], [7, 1], [11, 1]]
4200 [[2, 3], [3, 1], [5, 2], [7, 1]]
4224 [[2, 7], [3, 1], [11, 1]]
4312 [[2, 3], [7, 2], [11, 1]]
4320 [[2, 5], [3, 3], [5, 1]]
4356 [[2, 2], [3, 2], [11, 2]]
4374 [[2, 1], [3, 7]]
4400 [[2, 4], [5, 2], [11, 1]]
4410 [[2, 1], [3, 2], [5, 1], [7, 2]]
4480 [[2, 7], [5, 1], [7, 1]]
4500 [[2, 2], [3, 2], [5, 3]]
4536 [[2, 3], [3, 4], [7, 1]]
4608 [[2, 9], [3, 2]]
4620 [[2, 2], [3, 1], [5, 1], [7, 1], [11, 1]]
4704 [[2, 5], [3, 1], [7, 2]]
4752 [[2, 4], [3, 3], [11, 1]]
4800 [[2, 6], [3, 1], [5, 2]]
4802 [[2, 1], [7, 4]]
4840 [[2, 3], [5, 1], [11, 2]]
4860 [[2, 2], [3, 5], [5, 1]]
4900 [[2, 2], [5, 2], [7, 2]]
4928 [[2, 6], [7, 1], [11, 1]]
4950 [[2, 1], [3, 2], [5, 2], [11, 1]]
5000 [[2, 3], [5, 4]]
5040 [[2, 4], [3, 2], [5, 1], [7, 1]]
5082 [[2, 1], [3, 1], [7, 1], [11, 2]]
5120 [[2, 10], [5, 1]]
5184 [[2, 6], [3, 4]]
5250 [[2, 1], [3, 1], [5, 3], [7, 1]]
5280 [[2, 5], [3, 1], [5, 1], [11, 1]]
5292 [[2, 2], [3, 3], [7, 2]]
5324 [[2, 2], [11, 3]]
5346 [[2, 1], [3, 5], [11, 1]]
5376 [[2, 8], [3, 1], [7, 1]]
5390 [[2, 1], [5, 1], [7, 2], [11, 1]]
5400 [[2, 3], [3, 3], [5, 2]]
5488 [[2, 4], [7, 3]]
5500 [[2, 2], [5, 3], [11, 1]]
5544 [[2, 3], [3, 2], [7, 1], [11, 1]]
5600 [[2, 5], [5, 2], [7, 1]]
5632 [[2, 9], [11, 1]]
5670 [[2, 1], [3, 4], [5, 1], [7, 1]]
5760 [[2, 7], [3, 2], [5, 1]]
5808 [[2, 4], [3, 1], [11, 2]]
5832 [[2, 3], [3, 6]]
5880 [[2, 3], [3, 1], [5, 1], [7, 2]]
5940 [[2, 2], [3, 3], [5, 1], [11, 1]]
6000 [[2, 4], [3, 1], [5, 3]]
6048 [[2, 5], [3, 3], [7, 1]]
6050 [[2, 1], [5, 2], [11, 2]]
6144 [[2, 11], [3, 1]]
6160 [[2, 4], [5, 1], [7, 1], [11, 1]]
6174 [[2, 1], [3, 2], [7, 3]]
6250 [[2, 1], [5, 5]]
6272 [[2, 7], [7, 2]]
6300 [[2, 2], [3, 2], [5, 2], [7, 1]]
6336 [[2, 6], [3, 2], [11, 1]]
6400 [[2, 8], [5, 2]]
6468 [[2, 2], [3, 1], [7, 2], [11, 1]]
6480 [[2, 4], [3, 4], [5, 1]]
6534 [[2, 1], [3, 3], [11, 2]]
6600 [[2, 3], [3, 1], [5, 2], [11, 1]]
6720 [[2, 6], [3, 1], [5, 1], [7, 1]]
6750 [[2, 1], [3, 3], [5, 3]]
6776 [[2, 3], [7, 1], [11, 2]]
6804 [[2, 2], [3, 5], [7, 1]]
6860 [[2, 2], [5, 1], [7, 3]]
6912 [[2, 8], [3, 3]]
6930 [[2, 1], [3, 2], [5, 1], [7, 1], [11, 1]]
7000 [[2, 3], [5, 3], [7, 1]]
7040 [[2, 7], [5, 1], [11, 1]]
7056 [[2, 4], [3, 2], [7, 2]]
7128 [[2, 3], [3, 4], [11, 1]]
7168 [[2, 10], [7, 1]]
7200 [[2, 5], [3, 2], [5, 2]]
7260 [[2, 2], [3, 1], [5, 1], [11, 2]]
7290 [[2, 1], [3, 6], [5, 1]]
7350 [[2, 1], [3, 1], [5, 2], [7, 2]]
7392 [[2, 5], [3, 1], [7, 1], [11, 1]]
7500 [[2, 2], [3, 1], [5, 4]]
7546 [[2, 1], [7, 3], [11, 1]]
7560 [[2, 3], [3, 3], [5, 1], [7, 1]]
7680 [[2, 9], [3, 1], [5, 1]]
7700 [[2, 2], [5, 2], [7, 1], [11, 1]]
7744 [[2, 6], [11, 2]]
7776 [[2, 5], [3, 5]]
7840 [[2, 5], [5, 1], [7, 2]]
7920 [[2, 4], [3, 2], [5, 1], [11, 1]]
7938 [[2, 1], [3, 4], [7, 2]]
7986 [[2, 1], [3, 1], [11, 3]]
8000 [[2, 6], [5, 3]]
8064 [[2, 7], [3, 2], [7, 1]]
8100 [[2, 2], [3, 4], [5, 2]]
8192 [[2, 13]]
8232 [[2, 3], [3, 1], [7, 3]]
8250 [[2, 1], [3, 1], [5, 3], [11, 1]]
8316 [[2, 2], [3, 3], [7, 1], [11, 1]]
8400 [[2, 4], [3, 1], [5, 2], [7, 1]]
8448 [[2, 8], [3, 1], [11, 1]]
8470 [[2, 1], [5, 1], [7, 1], [11, 2]]
8624 [[2, 4], [7, 2], [11, 1]]
8640 [[2, 6], [3, 3], [5, 1]]
8712 [[2, 3], [3, 2], [11, 2]]
8748 [[2, 2], [3, 7]]
8750 [[2, 1], [5, 4], [7, 1]]
8800 [[2, 5], [5, 2], [11, 1]]
8820 [[2, 2], [3, 2], [5, 1], [7, 2]]
8910 [[2, 1], [3, 4], [5, 1], [11, 1]]
8960 [[2, 8], [5, 1], [7, 1]]
9000 [[2, 3], [3, 2], [5, 3]]
9072 [[2, 4], [3, 4], [7, 1]]
9216 [[2, 10], [3, 2]]
9240 [[2, 3], [3, 1], [5, 1], [7, 1], [11, 1]]
9408 [[2, 6], [3, 1], [7, 2]]
9450 [[2, 1], [3, 3], [5, 2], [7, 1]]
9504 [[2, 5], [3, 3], [11, 1]]
9600 [[2, 7], [3, 1], [5, 2]]
9604 [[2, 2], [7, 4]]
9680 [[2, 4], [5, 1], [11, 2]]
9702 [[2, 1], [3, 2], [7, 2], [11, 1]]
9720 [[2, 3], [3, 5], [5, 1]]
9800 [[2, 3], [5, 2], [7, 2]]
9856 [[2, 7], [7, 1], [11, 1]]
9900 [[2, 2], [3, 2], [5, 2], [11, 1]]
