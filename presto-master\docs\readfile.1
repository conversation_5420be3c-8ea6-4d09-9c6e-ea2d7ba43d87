.\" clig manual page template
.\" (C) 1995 <PERSON> (<EMAIL>)
.\"
.\" This file was generated by
.\" clig -- command line interface generator
.\"
.\"
.\" Clig will always edit the lines between pairs of `cligPart ...',
.\" but will not complain, if a pair is missing. So, if you want to
.\" make up a certain part of the manual page by hand rather than have
.\" it edited by clig, remove the respective pair of cligPart-lines.
.\"
.\" cligPart TITLE
.TH "readfile" 1 "12Mar10" "Clig-manuals" "Programmer's Manual"
.\" cligPart TITLE end

.\" cligPart NAME
.SH NAME
readfile \- Reads raw data from a binary file and displays it on stdout.
.\" cligPart NAME end

.\" cligPart SYNOPSIS
.SH SYNOPSIS
.B readfile
[-page]
[-byte]
[-b]
[-float]
[-f]
[-double]
[-d]
[-fcomplex]
[-fc]
[-dcomplex]
[-dc]
[-short]
[-s]
[-int]
[-i]
[-long]
[-l]
[-rzwcand]
[-rzw]
[-bincand]
[-bin]
[-position]
[-pos]
[-pkmb]
[-bcpm]
[-wapp]
[-spigot]
[-filterbank]
[-psrfits]
[-fortran]
[-index [index]]
[-nph nph]
file
.\" cligPart SYNOPSIS end

.\" cligPart OPTIONS
.SH OPTIONS
.IP -page
Paginate the output like 'more'.
.IP -byte
Raw data in byte format.
.IP -b
Raw data in byte format.
.IP -float
Raw data in floating point format.
.IP -f
Raw data in floating point format.
.IP -double
Raw data in double precision format.
.IP -d
Raw data in double precision format.
.IP -fcomplex
Raw data in float-complex format.
.IP -fc
Raw data in float-complex format.
.IP -dcomplex
Raw data in double-complex format.
.IP -dc
Raw data in double-complex format.
.IP -short
Raw data in short format.
.IP -s
Raw data in short format.
.IP -int
Raw data in integer format.
.IP -i
Raw data in integer format.
.IP -long
Raw data in long format.
.IP -l
Raw data in long format.
.IP -rzwcand
Raw data in rzw search candidate format.
.IP -rzw
Raw data in rzw search candidate format.
.IP -bincand
Raw data in bin search candidate format.
.IP -bin
Raw data in bin search candidate format.
.IP -position
Raw data in position struct format.
.IP -pos
Raw data in position struct format.
.IP -pkmb
Raw data in Parkes Multibeam format.
.IP -bcpm
Raw data in BCPM format.
.IP -wapp
Raw data in WAPP format.
.IP -spigot
Raw data in Spigot Card format.
.IP -filterbank
Raw data in SIGPROC filterbank format.
.IP -psrfits
Raw data in PSRFITS format.
.IP -fortran
Raw data was written by a fortran program.
.IP -index
The range of objects to display,
.br
0...2 Int values between -1 and oo.
.br
Default: `0' ` -1'
.IP -nph
0th FFT bin amplitude (for 'RZW' data),
.br
1 Double value.
.br
Default: `1.0'
.IP file
Input data file name..
.\" cligPart OPTIONS end

.\" cligPart DESCRIPTION
.SH DESCRIPTION
This manual page was generated automagically by clig, the
Command Line Interface Generator. Actually the programmer
using clig was supposed to edit this part of the manual
page after
generating it with clig, but obviously (s)he didn't.

Sadly enough clig does not yet have the power to pick a good
program description out of blue air ;-(
.\" cligPart DESCRIPTION end
