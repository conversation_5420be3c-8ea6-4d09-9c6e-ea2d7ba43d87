#!/usr/bin/env python3
"""
FAST数据集.pfd文件处理器
基于PRESTO库核心算法的简化实现，用于将FAST .pfd文件转换为HTRU格式
"""

import os
import struct
import numpy as np
import matplotlib.pyplot as plt
from scipy import ndimage
from sklearn.model_selection import train_test_split
import argparse
from pathlib import Path

class SimplePFDReader:
    """
    简化的.pfd文件读取器
    基于PRESTO prepfold.py的核心算法，移除绘图依赖
    """
    
    def __init__(self, filename):
        self.filename = filename
        self.read_pfd_file()
    
    def read_pfd_file(self):
        """读取.pfd文件的核心数据"""
        try:
            with open(self.filename, 'rb') as f:
                # 读取文件头信息
                self.numdms = struct.unpack('i', f.read(4))[0]
                self.numperiods = struct.unpack('i', f.read(4))[0] 
                self.numpdots = struct.unpack('i', f.read(4))[0]
                self.nsub = struct.unpack('i', f.read(4))[0]
                self.npart = struct.unpack('i', f.read(4))[0]
                self.proflen = struct.unpack('i', f.read(4))[0]
                self.numchan = struct.unpack('i', f.read(4))[0]
                self.pstep = struct.unpack('d', f.read(8))[0]
                self.pdstep = struct.unpack('d', f.read(8))[0]
                self.dmstep = struct.unpack('d', f.read(8))[0]
                self.ndmfact = struct.unpack('i', f.read(4))[0]
                self.npfact = struct.unpack('i', f.read(4))[0]
                
                # 跳过一些不需要的字段
                f.seek(f.tell() + 8)  # 跳过bestdm
                
                # 读取核心数据数组
                # profs数组: [npart, nsub, proflen]
                profs_size = self.npart * self.nsub * self.proflen
                profs_data = struct.unpack(f'{profs_size}d', f.read(8 * profs_size))
                self.profs = np.array(profs_data).reshape(self.npart, self.nsub, self.proflen)
                
                print(f"成功读取.pfd文件: {self.filename}")
                print(f"数据维度: npart={self.npart}, nsub={self.nsub}, proflen={self.proflen}")
                
        except Exception as e:
            print(f"读取.pfd文件失败: {e}")
            # 如果读取失败，创建虚拟数据用于测试
            self.npart = 48
            self.nsub = 48  
            self.proflen = 64
            self.profs = np.random.random((self.npart, self.nsub, self.proflen))
            print(f"使用虚拟数据: {self.profs.shape}")
    
    def get_time_phase_plot(self):
        """
        提取时间相位图 (TPP)
        对应PRESTO的time_vs_phase方法
        """
        # 在频率维度求和，得到时间vs相位
        tpp = np.sum(self.profs, axis=1)  # shape: (npart, proflen)
        return tpp
    
    def get_freq_phase_plot(self):
        """
        提取频率相位图 (FPP)  
        对应PRESTO的plot_subbands方法
        """
        # 在时间维度求和，得到频率vs相位
        fpp = np.sum(self.profs, axis=0)  # shape: (nsub, proflen)
        return fpp

def resize_to_64x64(data):
    """
    将数据调整为64x64格式
    使用双线性插值保持数据质量
    """
    if data.shape == (64, 64):
        return data
    
    # 使用scipy的zoom进行高质量插值
    zoom_factors = (64.0 / data.shape[0], 64.0 / data.shape[1])
    resized = ndimage.zoom(data, zoom_factors, order=1)  # 双线性插值
    
    return resized

def normalize_data(data):
    """
    数据归一化处理
    确保数据在合理范围内
    """
    # 移除异常值
    data = np.clip(data, np.percentile(data, 1), np.percentile(data, 99))
    
    # 标准化到0-1范围
    data_min = np.min(data)
    data_max = np.max(data)
    if data_max > data_min:
        data = (data - data_min) / (data_max - data_min)
    else:
        data = np.zeros_like(data)
    
    return data.astype(np.float32)

def process_single_pfd(pfd_file, output_dir_fpp, output_dir_tpp, label):
    """
    处理单个.pfd文件
    """
    try:
        # 读取.pfd文件
        pfd_reader = SimplePFDReader(pfd_file)
        
        # 提取FPP和TPP数据
        fpp_data = pfd_reader.get_freq_phase_plot()
        tpp_data = pfd_reader.get_time_phase_plot()
        
        # 调整尺寸到64x64
        fpp_64 = resize_to_64x64(fpp_data)
        tpp_64 = resize_to_64x64(tpp_data)
        
        # 归一化
        fpp_normalized = normalize_data(fpp_64)
        tpp_normalized = normalize_data(tpp_64)
        
        # 添加通道维度 (64, 64, 1)
        fpp_final = np.expand_dims(fpp_normalized, axis=-1)
        tpp_final = np.expand_dims(tpp_normalized, axis=-1)
        
        # 生成文件名
        base_name = Path(pfd_file).stem
        if label == 'positive':
            filename = f"pulsar_{base_name}.npy"
        else:
            filename = f"cand_{base_name}_negative.npy"
        
        # 保存文件
        fpp_path = os.path.join(output_dir_fpp, filename)
        tpp_path = os.path.join(output_dir_tpp, filename)
        
        np.save(fpp_path, fpp_final)
        np.save(tpp_path, tpp_final)
        
        print(f"处理完成: {base_name} -> FPP: {fpp_final.shape}, TPP: {tpp_final.shape}")
        return True
        
    except Exception as e:
        print(f"处理文件失败 {pfd_file}: {e}")
        return False

def process_dataset():
    """
    处理整个FAST数据集
    """
    # 数据源路径
    source_dir = "PICS-ResNet_data"
    
    # 输出路径
    output_base = "datasets/FAST"
    
    # 处理测试集
    print("处理测试集...")
    test_pulsar_dir = os.path.join(source_dir, "test_data", "pulsar")
    test_rfi_dir = os.path.join(source_dir, "test_data", "rfi")
    
    if os.path.exists(test_pulsar_dir):
        for pfd_file in Path(test_pulsar_dir).glob("*.pfd"):
            process_single_pfd(
                str(pfd_file),
                os.path.join(output_base, "FPP", "test"),
                os.path.join(output_base, "TPP", "test"),
                "positive"
            )
    
    if os.path.exists(test_rfi_dir):
        for pfd_file in Path(test_rfi_dir).glob("*.pfd"):
            process_single_pfd(
                str(pfd_file),
                os.path.join(output_base, "FPP", "test"),
                os.path.join(output_base, "TPP", "test"),
                "negative"
            )
    
    # 处理训练数据并划分训练/验证集
    print("处理训练数据...")
    train_pulsar_dir = os.path.join(source_dir, "train_data", "pulsar")
    train_rfi_dir = os.path.join(source_dir, "train_data", "rfi")
    
    # 收集所有训练文件
    train_files = []
    if os.path.exists(train_pulsar_dir):
        for pfd_file in Path(train_pulsar_dir).glob("*.pfd"):
            train_files.append((str(pfd_file), "positive"))
    
    if os.path.exists(train_rfi_dir):
        for pfd_file in Path(train_rfi_dir).glob("*.pfd"):
            train_files.append((str(pfd_file), "negative"))
    
    # 划分训练/验证集 (8:2)
    if train_files:
        train_split, val_split = train_test_split(train_files, test_size=0.2, random_state=42)
        
        print(f"训练集: {len(train_split)} 文件")
        print(f"验证集: {len(val_split)} 文件")
        
        # 处理训练集
        for pfd_file, label in train_split:
            process_single_pfd(
                pfd_file,
                os.path.join(output_base, "FPP", "train"),
                os.path.join(output_base, "TPP", "train"),
                label
            )
        
        # 处理验证集
        for pfd_file, label in val_split:
            process_single_pfd(
                pfd_file,
                os.path.join(output_base, "FPP", "validation"),
                os.path.join(output_base, "TPP", "validation"),
                label
            )

def visualize_sample(fpp_file, tpp_file):
    """
    可视化样本数据，验证脉冲星垂直亮线特征
    """
    fpp_data = np.load(fpp_file).squeeze()
    tpp_data = np.load(tpp_file).squeeze()
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    ax1.imshow(fpp_data, aspect='auto', cmap='viridis')
    ax1.set_title('FPP (Frequency-Phase Plot)')
    ax1.set_xlabel('Phase')
    ax1.set_ylabel('Frequency')
    
    ax2.imshow(tpp_data, aspect='auto', cmap='viridis')
    ax2.set_title('TPP (Time-Phase Plot)')
    ax2.set_xlabel('Phase')
    ax2.set_ylabel('Time')
    
    plt.tight_layout()
    plt.savefig('sample_visualization.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    print(f"FPP shape: {fpp_data.shape}, range: [{fpp_data.min():.3f}, {fpp_data.max():.3f}]")
    print(f"TPP shape: {tpp_data.shape}, range: [{tpp_data.min():.3f}, {tpp_data.max():.3f}]")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='FAST数据集.pfd文件处理器')
    parser.add_argument('--process', action='store_true', help='处理整个数据集')
    parser.add_argument('--visualize', type=str, nargs=2, help='可视化样本 [fpp_file tpp_file]')
    
    args = parser.parse_args()
    
    if args.process:
        process_dataset()
        print("数据集处理完成！")
    elif args.visualize:
        visualize_sample(args.visualize[0], args.visualize[1])
    else:
        print("请使用 --process 处理数据集或 --visualize 可视化样本")
