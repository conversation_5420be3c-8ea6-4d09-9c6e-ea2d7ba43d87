.\" clig manual page template
.\" (C) 1995-2001 <PERSON> (<EMAIL>)
.\"
.\" This file was generated by
.\" clig -- command line interface generator
.\"
.\"
.\" Clig will always edit the lines between pairs of `cligPart ...',
.\" but will not complain, if a pair is missing. So, if you want to
.\" make up a certain part of the manual page by hand rather than have
.\" it edited by clig, remove the respective pair of cligPart-lines.
.\"
.\" cligPart TITLE
.TH "rfifind" 1 "28Jun17" "Clig-manuals" "Programmer's Manual"
.\" cligPart TITLE end

.\" cligPart NAME
.SH NAME
rfifind \- Examines radio data for narrow and wide band interference as well as problems with channels
.\" cligPart NAME end

.\" cligPart SYNOPSIS
.SH SYNOPSIS
.B rfifind
[-ncpus ncpus]
-o outfile
[-filterbank]
[-psrfits]
[-noweights]
[-noscales]
[-nooffsets]
[-wapp]
[-window]
[-numwapps numwapps]
[-if ifs]
[-clip clip]
[-noclip]
[-invert]
[-zerodm]
[-xwin]
[-nocompute]
[-rfixwin]
[-rfips]
[-time time]
[-blocks blocks]
[-timesig timesigma]
[-freqsig freqsigma]
[-chanfrac chantrigfrac]
[-intfrac inttrigfrac]
[-zapchan zapchanstr]
[-zapints zapintsstr]
[-mask maskfile]
[-ignorechan ignorechanstr]
infile ...
.\" cligPart SYNOPSIS end

.\" cligPart OPTIONS
.SH OPTIONS
.IP -ncpus
Number of processors to use with OpenMP,
.br
1 Int value between 1 and oo.
.br
Default: `1'
.IP -o
Root of the output file names,
.br
1 String value
.IP -filterbank
Raw data in SIGPROC filterbank format.
.IP -psrfits
Raw data in PSRFITS format.
.IP -noweights
Do not apply PSRFITS weights.
.IP -noscales
Do not apply PSRFITS scales.
.IP -nooffsets
Do not apply PSRFITS offsets.
.IP -wapp
Raw data in Wideband Arecibo Pulsar Processor (WAPP) format.
.IP -window
Window correlator lags with a Hamming window before FFTing.
.IP -numwapps
Number of WAPPs used with contiguous frequencies,
.br
1 Int value between 1 and 8.
.br
Default: `1'
.IP -if
A specific IF to use if available (summed IFs is the default),
.br
1 Int value between 0 and 1.
.IP -clip
Time-domain sigma to use for clipping (0.0 = no clipping, 6.0 = default,
.br
1 Float value between 0 and 1000.0.
.br
Default: `6.0'
.IP -noclip
Do not clip the data.  (The default is to _always_ clip!).
.IP -invert
For rawdata, flip (or invert) the band.
.IP -zerodm
Subtract the mean of all channels from each sample (i.e. remove zero DM).
.IP -xwin
Draw plots to the screen as well as a PS file.
.IP -nocompute
Just plot and remake the mask.
.IP -rfixwin
Show the RFI instances on screen.
.IP -rfips
Plot the RFI instances in a PS file.
.IP -time
Seconds to integrate for stats and FFT calcs (use this or -blocks),
.br
1 Double value between 0 and oo.
.br
Default: `30.0'
.IP -blocks
Number of blocks (usually 16-1024 samples) to integrate for stats and FFT calcs,
.br
1 Int value between 1 and oo.
.IP -timesig
The +/-sigma cutoff to reject time-domain chunks,
.br
1 Float value between 0 and oo.
.br
Default: `10'
.IP -freqsig
The +/-sigma cutoff to reject freq-domain chunks,
.br
1 Float value between 0 and oo.
.br
Default: `4'
.IP -chanfrac
The fraction of bad channels that will mask a full interval,
.br
1 Float value between 0.0 and 1.0.
.br
Default: `0.7'
.IP -intfrac
The fraction of bad intervals that will mask a full channel,
.br
1 Float value between 0.0 and 1.0.
.br
Default: `0.3'
.IP -zapchan
Comma separated string (no spaces!) of channels to explicitly mask.  Ranges are specified by min:max[:step],
.br
1 String value
.IP -zapints
Comma separated string (no spaces!) of intervals to explicitly mask.  Ranges are specified by min:max[:step],
.br
1 String value
.IP -mask
File containing masking information to use,
.br
1 String value
.IP -ignorechan
Comma separated string (no spaces!) of channels to ignore (or file containing such string).  Ranges are specified by min:max[:step],
.br
1 String value
.IP infile
Input data file name(s)..
.\" cligPart OPTIONS end

.\" cligPart DESCRIPTION
.SH DESCRIPTION
This manual page was generated automagically by clig, the
Command Line Interface Generator. Actually the programmer
using clig was supposed to edit this part of the manual
page after
generating it with clig, but obviously (s)he didn't.

Sadly enough clig does not yet have the power to pick a good
program description out of blue air ;-(
.\" cligPart DESCRIPTION end
