.\" clig manual page template
.\" (C) 1995 <PERSON> (<EMAIL>)
.\"
.\" This file was generated by
.\" clig -- command line interface generator
.\"
.\"
.\" Clig will always edit the lines between pairs of `cligPart ...',
.\" but will not complain, if a pair is missing. So, if you want to
.\" make up a certain part of the manual page by hand rather than have
.\" it edited by clig, remove the respective pair of cligPart-lines.
.\"
.\" cligPart TITLE
.TH "bincand" 1 "12Mar10" "Clig-manuals" "Programmer's Manual"
.\" cligPart TITLE end

.\" cligPart NAME
.SH NAME
bincand \- Generates a set of fake orbits and their Fourier modulation spectra for a binary candidate (usually generated by search_bin).  It tries to return the optimum fit for the binary candidate.
.\" cligPart NAME end

.\" cligPart SYNOPSIS
.SH SYNOPSIS
.B bincand
[-plo plo]
[-phi phi]
[-rlo rlo]
[-rhi rhi]
[-psr psrname]
[-candnum candnum]
[-candfile candfile]
[-usr]
[-pb pb]
[-x asinic]
[-e e]
[-To To]
[-w w]
[-wdot wdot]
[-mak]
infile
.\" cligPart SYNOPSIS end

.\" cligPart OPTIONS
.SH OPTIONS
.IP -plo
The low pulsar period to check (s),
.br
1 Double value between 0 and oo.
.IP -phi
The high pulsar period to check (s),
.br
1 Double value between 0 and oo.
.IP -rlo
The low Fourier frequency bin to check,
.br
1 Double value between 0 and oo.
.IP -rhi
The high Fourier frequency bin to check,
.br
1 Double value between 0 and oo.
.IP -psr
Name of pulsar to check for (do not include J or B),
.br
1 String value
.IP -candnum
Number of the candidate to optimize from candfile.,
.br
1 Int value between 1 and oo.
.IP -candfile
Name of the bincary candidate file.,
.br
1 String value
.IP -usr
Describe your own binary candidate.  Must include all of the following (assumed) parameters.
.IP -pb
The orbital period (s),
.br
1 Double value between 0 and oo.
.IP -x
The projected orbital semi-major axis (lt-sec),
.br
1 Double value between 0 and oo.
.IP -e
The orbital eccentricity,
.br
1 Double value between 0 and 0.9999999.
.br
Default: `0'
.IP -To
The time of periastron passage (MJD),
.br
1 Double value between 0 and oo.
.IP -w
Longitude of periastron (deg),
.br
1 Double value between 0 and 360.
.IP -wdot
Rate of advance of periastron (deg/yr),
.br
1 Double value.
.br
Default: `0'
.IP -mak
Determine optimization parameters from 'infile.mak'.
.IP infile
Input fft file name (without a suffix) of floating point fft data.  A '.inf' file of the same name must also exist.
.\" cligPart OPTIONS end

.\" cligPart DESCRIPTION
.SH DESCRIPTION
This manual page was generated automagically by clig, the
Command Line Interface Generator. Actually the programmer
using clig was supposed to edit this part of the manual
page after
generating it with clig, but obviously (s)he didn't.

Sadly enough clig does not yet have the power to pick a good
program description out of blue air ;-(
.\" cligPart DESCRIPTION end
