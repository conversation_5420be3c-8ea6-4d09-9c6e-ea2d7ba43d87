.\" clig manual page template
.\" (C) 1995-2001 <PERSON> (<EMAIL>)
.\"
.\" This file was generated by
.\" clig -- command line interface generator
.\"
.\"
.\" Clig will always edit the lines between pairs of `cligPart ...',
.\" but will not complain, if a pair is missing. So, if you want to
.\" make up a certain part of the manual page by hand rather than have
.\" it edited by clig, remove the respective pair of cligPart-lines.
.\"
.\" cligPart TITLE
.TH "rednoise" 1 "12Mar10" "Clig-manuals" "Programmer's Manual"
.\" cligPart TITLE end

.\" cligPart NAME
.SH NAME
rednoise \- Rednoise extraction routine.
.\" cligPart NAME end

.\" cligPart SYNOPSIS
.SH SYNOPSIS
.B rednoise
[-startwidth startwidth]
[-endwidth endwidth]
[-endfreq endfreq]
file
.\" cligPart SYNOPSIS end

.\" cligPart OPTIONS
.SH OPTIONS
.IP -startwidth
The initial windowing size.,
.br
1 Int value between 2 and 50.
.br
Default: `6'
.IP -endwidth
The final windowing size.,
.br
1 Int value between 50 and 500.
.br
Default: `100'
.IP -endfreq
The highest frequency where the windowing increases.,
.br
1 Double value between 0.1 and 10.
.br
Default: `6'
.IP file
Input '.fft' file..
.\" cligPart OPTIONS end

.\" cligPart DESCRIPTION
.SH DESCRIPTION
This manual page was generated automagically by clig, the
Command Line Interface Generator. Actually the programmer
using clig was supposed to edit this part of the manual
page after
generating it with clig, but obviously (s)he didn't.

Sadly enough clig does not yet have the power to pick a good
program description out of blue air ;-(
.\" cligPart DESCRIPTION end
