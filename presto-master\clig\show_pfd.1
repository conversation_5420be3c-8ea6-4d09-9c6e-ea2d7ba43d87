.\" clig manual page template
.\" (C) 1995-2001 <PERSON> (<EMAIL>)
.\"
.\" This file was generated by
.\" clig -- command line interface generator
.\"
.\"
.\" Clig will always edit the lines between pairs of `cligPart ...',
.\" but will not complain, if a pair is missing. So, if you want to
.\" make up a certain part of the manual page by hand rather than have
.\" it edited by clig, remove the respective pair of cligPart-lines.
.\"
.\" cligPart TITLE
.TH "show_pfd" 1 "04Feb23" "Clig-manuals" "Programmer's Manual"
.\" cligPart TITLE end

.\" cligPart NAME
.SH NAME
show_pfd \- Displays or regenerates the Postscript for a 'pfd' file created by prepfold.
.\" cligPart NAME end

.\" cligPart SYNOPSIS
.SH SYNOPSIS
.B show_pfd
[-noxwin]
[-showfold]
[-scaleparts]
[-allgrey]
[-justprofs]
[-portrait]
[-events]
[-infoonly]
[-fixchi]
[-samples]
[-normalize]
[-killsubs killsubsstr]
[-killparts killpartsstr]
infile ...
.\" cligPart SYNOPSIS end

.\" cligPart OPTIONS
.SH OPTIONS
.IP -noxwin
Do not show the result plots on-screen, only make postscript files.
.IP -showfold
Use the input fold paramters (i.e. not the optimized values) when showing the plot.
.IP -scaleparts
Scale the part profiles independently.
.IP -allgrey
Make all the images greyscale instead of color.
.IP -justprofs
Only output the profile portions of the plot.
.IP -portrait
Orient the output in portrait mode (for -justprofs).
.IP -events
The folded data were events instead of samples or bins.
.IP -infoonly
Display the pfd info and exit without generating plots..
.IP -fixchi
Adjust the reduced chi^2 values so that off-pulse reduced chi^2 = 1.
.IP -samples
Treat the data as samples and not as finite-duration integrated data.
.IP -normalize
Normalize stats for each fold (i.e. to bandpass flatten subbands).
.IP -killsubs
Comma separated string (no spaces!) of subbands to explicitly remove from analysis (i.e. zero out).  Ranges are specified by min:max[:step],
.br
1 String value
.IP -killparts
Comma separated string (no spaces!) of intervals to explicitly remove from analysis (i.e. zero-out).  Ranges are specified by min:max[:step],
.br
1 String value
.IP infile
The input 'pfd' file name..
.\" cligPart OPTIONS end

.\" cligPart DESCRIPTION
.SH DESCRIPTION
This manual page was generated automagically by clig, the
Command Line Interface Generator. Actually the programmer
using clig was supposed to edit this part of the manual
page after
generating it with clig, but obviously (s)he didn't.

Sadly enough clig does not yet have the power to pick a good
program description out of blue air ;-(
.\" cligPart DESCRIPTION end
