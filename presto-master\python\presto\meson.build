py3.install_sources(
  ['barycenter.py', 'bestprof.py', 'binary_psr.py', 'cosine_rand.py', 
  'events.py', 'fftfit.py', 'filterbank.py', 'infodata.py', 'injectpsr.py', 
  'kuiper.py', 'mpfit.py', 'parfile.py', 'Pgplot.py', 'polycos.py', 
  'prepfold.py', 'psr_constants.py', 'psrfits.py', 'psr_utils.py', 'pypsrcat.py', 
  'residuals.py', 'rfifind.py', 'sifting.py', 'sigproc.py', 'simple_roots.py', 
  'sinc_interp.py', 'spectra.py', 'waterfaller.py', '__init__.py'],
  subdir: 'presto'
)
