# Admin data

Name prepfold

Usage "Prepares (if required) and folds raw radio data, standard time series, or events."

Version [exec date +%d%b%y]

Commandline full_cmd_line

# Options (in order you want them to appear)

Int -ncpus   ncpus      {Number of processors to use with OpenMP} \
	-r 1 oo  -d 1
String -o       outfile {Root of the output file names}
Flag   -filterbank  filterbank  {Raw data in SIGPROC filterbank format}
Flag   -psrfits     psrfits     {Raw data in PSRFITS format}
Flag   -noweights  noweights  {Do not apply PSRFITS weights}
Flag   -noscales   noscales   {Do not apply PSRFITS scales}
Flag   -nooffsets  nooffsets  {Do not apply PSRFITS offsets}
Flag   -wapp    wapp    {Raw data in Wideband Arecibo Pulsar Processor (WAPP) format}
Flag   -window  window  {Window correlator lags with a Hamming window before FFTing}
Flag   -topo    topo    {Fold the data topocentrically (i.e. don't barycenter)}
Flag   -invert  invert  {For rawdata, flip (or invert) the band}
Flag   -zerodm  zerodm  {Subtract the mean of all channels from each sample (i.e. remove zero DM)}
Flag   -absphase absphase  {Use the absolute phase associated with polycos}
Flag   -barypolycos barypolycos  {Force the use of polycos for barycentered events}
Flag   -debug  debug {Show debugging output when calling TEMPO for polycos}
Flag   -samples samples {Treat the data as samples and not as finite-duration integrated data}
Flag   -normalize normalize {Bandpass flatten the data by normalizing the subbands}
Int    -numwapps numwapps  {Number of WAPPs used with contiguous frequencies} \
	-r 1 8    -d 1
Int    -if      ifs     {A specific IF to use if available (summed IFs is the default)} \
	-r 0 1
Float  -clip    clip    {Time-domain sigma to use for clipping (0.0 = no clipping, 6.0 = default} \
	-r 0 1000.0  -d 6.0
Flag   -noclip  noclip  {Do not clip the data.  (The default is to _always_ clip!)}
Flag   -noxwin  noxwin  {Do not show the result plots on-screen, only make postscript files}
Flag   -runavg  runavg  {Subtract each blocks average as it is read (single channel data only)}
Flag   -fine fine       {A finer gridding in the p/pdot plane (for well known p and pdot)}
Flag   -coarse coarse   {A coarser gridding in the p/pdot plane (for uknown p and pdot)}
Flag   -slow slow       {Sets useful flags for slow pulsars}
Flag   -searchpdd searchpdd {Search p-dotdots as well as p and p-dots}
Flag   -searchfdd searchfdd {Search f-dotdots as well as f and f-dots}
Flag   -nosearch nosearch {Show but do not search the p/pdot and/or DM phase spaces}
Flag   -nopsearch nopsearch {Show but do not search over period}
Flag   -nopdsearch nopdsearch {Show but do not search over p-dot}
Flag   -nodmsearch nodmsearch {Show but do not search over DM}
Flag   -scaleparts scaleparts {Scale the part profiles independently}
Flag   -allgrey allgrey     {Make all the images greyscale instead of color}
Flag   -fixchi  fixchi      {Adjust the reduced chi^2 values so that off-pulse reduced chi^2 = 1}
Flag   -justprofs justprofs {Only output the profile portions of the plot}
Double -dm      dm      {The central DM of the search (cm^-3 pc)} \
	-r 0 oo  -d 0
Int    -n       proflen {The number of bins in the profile.  Defaults to the number of sampling bins which correspond to one folded period}
Int    -nsub    nsub    {The number of sub-bands to use for the DM search.  If unspecified, will use something reasonable.} \
	-r 1 4096
Int    -npart   npart   {The number of sub-integrations to use for the period search} \
	-r 1 4096  -d 64
Int    -pstep    pstep   {The minimum period stepsize over the observation in profile bins} \
	-r 1 10  -d 2
Int    -pdstep    pdstep   {The minimum P-dot stepsize over the observation in profile bins} \
	-r 1 20  -d 4
Int    -dmstep    dmstep   {The minimum DM stepsize over the observation in profile bins} \
	-r 1 10  -d 2
Int    -npfact   npfact   {2 * npfact * proflen + 1 periods and p-dots will be searched} \
	-r 1 10  -d 2
Int    -ndmfact  ndmfact   {2 * ndmfact * proflen + 1 DMs will be searched} \
	-r 1 1000  -d 3
Double -p    p    {The nominative folding period (s)} \
	-r 0 oo
Double -pd   pd	 {The nominative period derivative (s/s)} \
	-r -oo oo  -d 0.0
Double -pdd   pdd {The nominative period 2nd derivative (s/s^2)} \
	-r -oo oo  -d 0.0
Double -f    f   {The nominative folding frequency (hz)} \
	-r 0 oo
Double -fd   fd  {The nominative frequency derivative (hz/s)} \
	-r -oo oo  -d 0
Double -fdd  fdd {The nominative frequency 2nd derivative (hz/s^2)} \
	-r -oo oo  -d 0
Double -pfact pfact {A factor to multiple the candidate p and p-dot by} \
	-r 0.0 100.0  -d 1.0
Double -ffact ffact {A factor to multiple the candidate f and f-dot by} \
	-r 0.0 100.0  -d 1.0
Double -phs  phs {Offset phase for the profil} \
	-r 0.0 1.0  -d 0.0
Double -start  startT  {The folding start time as a fraction of the full obs} \
	-r 0.0 1.0  -d 0.0
Double -end  endT  {The folding end time as a fraction of the full obs} \
	-r 0.0 1.0  -d 1.0
String -psr psrname {Name of pulsar to fold (do not include J or B)}
String -par parname {Name of a TEMPO par file from which to get PSR params}
String -polycos polycofile {File containing TEMPO polycos for psrname (not required)}
String -timing timing {Sets useful flags for TOA generation. Generates polycos (if required) based on the par file specified as the argument. (This means you don't need the -par or -psr commands!)}
Int -rzwcand rzwcand {The candidate number to fold from 'infile'_rzw.cand} \
        -r 1 oo
String -rzwfile rzwfile {Name of the rzw search '.cand' file to use (with suffix)}
Int -accelcand accelcand {The candidate number to fold from 'infile'_rzw.cand} \
        -r 1 oo
String -accelfile accelfile {Name of the accel search '.cand' file to use (with suffix)}

# Parameters for a binary pulsar

Flag -bin  binary {Fold a binary pulsar.  Must include all of the following parameters}
Double -pb   pb      {The orbital period (s)} \
	-r 0 oo
Double -x    asinic  {The projected orbital semi-major axis (lt-sec)} \
	-r 0 oo
Double -e    e       {The orbital eccentricity} \
	-r 0 0.9999999  -d 0
Double -To   To      {The time of periastron passage (MJD)} \
	-r 0 oo
Double -w    w       {Longitude of periastron (deg)} \
	-r 0 360
Double -wdot wdot    {Rate of advance of periastron (deg/yr)} \
	-r -oo oo  -d 0

String -mask    maskfile   {File containing masking information to use}

String -ignorechan ignorechanstr {Comma separated string (no spaces!) of channels to ignore (or file containing such string).  Ranges are specified by min:max[:step]}

# Parameters if we are using events instead of a timeseries

Flag -events events  {Use a event file instead of a time series (.dat) file}
Flag -days   days   {Events are in days since the EPOCH in the '.inf' file (default is seconds)}
Flag -mjds   mjds   {Events are in MJDs}
Flag -double double {Events are in binary double precision (default is ASCII)}
Double -offset offset {A time offset to add to the 1st event in the same units as the events} \
	-r -oo oo  -d 0

# Rest of command line:

Rest infile {Input data file name.  If the data is not in a regognized raw data format, it should be a file containing a time series of single-precision floats or short ints.  In this case a '.inf' file with the same root filename must also exist (Note that this means that the input data file must have a suffix that starts with a period)} \
        -c 1 16384
