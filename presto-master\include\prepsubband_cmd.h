#ifndef __prepsubband_cmd__
#define __prepsubband_cmd__
/*****
  command line parser interface -- generated by clig 
  (http://wsd.iitb.fhg.de/~geg/clighome/)

  The command line parser `clig':
  (C) 1995-2004 <PERSON> (<EMAIL>)
*****/

typedef struct s_Cmdline {
  /***** -ncpus: Number of processors to use with OpenMP */
  char ncpusP;
  int ncpus;
  int ncpusC;
  /***** -o: Root of the output file names */
  char outfileP;
  char* outfile;
  int outfileC;
  /***** -filterbank: Raw data in SIGPROC filterbank format */
  char filterbankP;
  /***** -psrfits: Raw data in PSRFITS format */
  char psrfitsP;
  /***** -noweights: Do not apply PSRFITS weights */
  char noweightsP;
  /***** -noscales: Do not apply PSRFITS scales */
  char noscalesP;
  /***** -nooffsets: Do not apply PSRFITS offsets */
  char nooffsetsP;
  /***** -wapp: Raw data in Wideband Arecibo Pulsar Processor (WAPP) format */
  char wappP;
  /***** -window: Window correlator lags with a Hamming window before FFTing */
  char windowP;
  /***** -numwapps: Number of WAPPs used with contiguous frequencies */
  char numwappsP;
  int numwapps;
  int numwappsC;
  /***** -if: A specific IF to use if available (summed IFs is the default) */
  char ifsP;
  int ifs;
  int ifsC;
  /***** -clip: Time-domain sigma to use for clipping (0.0 = no clipping, 6.0 = default */
  char clipP;
  float clip;
  int clipC;
  /***** -noclip: Do not clip the data.  (The default is to _always_ clip!) */
  char noclipP;
  /***** -invert: For rawdata, flip (or invert) the band */
  char invertP;
  /***** -zerodm: Subtract the mean of all channels from each sample (i.e. remove zero DM) */
  char zerodmP;
  /***** -runavg: Running mean subtraction from the input data */
  char runavgP;
  /***** -sub: Write subbands instead of de-dispersed data */
  char subP;
  /***** -subdm: The DM to use when de-dispersing subbands for -sub */
  char subdmP;
  double subdm;
  int subdmC;
  /***** -numout: Output this many values.  If there are not enough values in the original data file, will pad the output file with the average value */
  char numoutP;
  long numout;
  int numoutC;
  /***** -nobary: Do not barycenter the data */
  char nobaryP;
  /***** -offset: Number of spectra to offset into as starting data point */
  char offsetP;
  long offset;
  int offsetC;
  /***** -start: Starting point of the processing as a fraction of the full obs */
  char startP;
  double start;
  int startC;
  /***** -lodm: The lowest dispersion measure to de-disperse (cm^-3 pc) */
  char lodmP;
  double lodm;
  int lodmC;
  /***** -dmstep: The stepsize in dispersion measure to use(cm^-3 pc) */
  char dmstepP;
  double dmstep;
  int dmstepC;
  /***** -numdms: The number of DMs to de-disperse */
  char numdmsP;
  int numdms;
  int numdmsC;
  /***** -nsub: The number of sub-bands to use */
  char nsubP;
  int nsub;
  int nsubC;
  /***** -downsamp: The number of neighboring bins to co-add */
  char downsampP;
  int downsamp;
  int downsampC;
  /***** -dmprec: The number of decimals in the precision of the DM in the filename. */
  char dmprecP;
  int dmprec;
  int dmprecC;
  /***** -mask: File containing masking information to use */
  char maskfileP;
  char* maskfile;
  int maskfileC;
  /***** -ignorechan: Comma separated string (no spaces!) of channels to ignore (or file containing such string).  Ranges are specified by min:max[:step] */
  char ignorechanstrP;
  char* ignorechanstr;
  int ignorechanstrC;
  /***** uninterpreted command line parameters */
  int argc;
  /*@null*/char **argv;
  /***** the whole command line concatenated */
  char *full_cmd_line;
} Cmdline;


extern char *Program;
extern void usage(void);
extern /*@shared*/Cmdline *parseCmdline(int argc, char **argv);

extern void showOptionValues(void);

#endif

