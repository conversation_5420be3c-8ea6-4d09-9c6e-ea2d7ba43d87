/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (https://www.swig.org).
 * Version 4.3.0
 *
 * Do not make changes to this file unless you know what you are doing - modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */


#define SWIG_VERSION 0x040300
#define SWIGPYTHON
#define SWIG_PYTHON_DIRECTOR_NO_VTABLE

/* -----------------------------------------------------------------------------
 *  This section contains generic SWIG labels for method/variable
 *  declarations/attributes, and other compiler dependent labels.
 * ----------------------------------------------------------------------------- */

/* template workaround for compilers that cannot correctly implement the C++ standard */
#ifndef SWIGTEMPLATEDISAMBIGUATOR
# if defined(__SUNPRO_CC) && (__SUNPRO_CC <= 0x560)
#  define SWIGTEMPLATEDISAMBIGUATOR template
# elif defined(__HP_aCC)
/* Needed even with `aCC -AA' when `aCC -V' reports HP ANSI C++ B3910B A.03.55 */
/* If we find a maximum version that requires this, the test would be __HP_aCC <= 35500 for A.03.55 */
#  define SWIGTEMPLATEDISAMBIGUATOR template
# else
#  define SWIGTEMPLATEDISAMBIGUATOR
# endif
#endif

/* inline attribute */
#ifndef SWIGINLINE
# if defined(__cplusplus) || (defined(__GNUC__) && !defined(__STRICT_ANSI__))
#   define SWIGINLINE inline
# else
#   define SWIGINLINE
# endif
#endif

/* attribute recognised by some compilers to avoid 'unused' warnings */
#ifndef SWIGUNUSED
# if defined(__GNUC__)
#   if !(defined(__cplusplus)) || (__GNUC__ > 3 || (__GNUC__ == 3 && __GNUC_MINOR__ >= 4))
#     define SWIGUNUSED __attribute__ ((__unused__))
#   else
#     define SWIGUNUSED
#   endif
# elif defined(__ICC)
#   define SWIGUNUSED __attribute__ ((__unused__))
# else
#   define SWIGUNUSED
# endif
#endif

#ifndef SWIG_MSC_UNSUPPRESS_4505
# if defined(_MSC_VER)
#   pragma warning(disable : 4505) /* unreferenced local function has been removed */
# endif
#endif

#ifndef SWIGUNUSEDPARM
# ifdef __cplusplus
#   define SWIGUNUSEDPARM(p)
# else
#   define SWIGUNUSEDPARM(p) p SWIGUNUSED
# endif
#endif

/* internal SWIG method */
#ifndef SWIGINTERN
# define SWIGINTERN static SWIGUNUSED
#endif

/* internal inline SWIG method */
#ifndef SWIGINTERNINLINE
# define SWIGINTERNINLINE SWIGINTERN SWIGINLINE
#endif

/* exporting methods */
#if defined(__GNUC__)
#  if (__GNUC__ >= 4) || (__GNUC__ == 3 && __GNUC_MINOR__ >= 4)
#    ifndef GCC_HASCLASSVISIBILITY
#      define GCC_HASCLASSVISIBILITY
#    endif
#  endif
#endif

#ifndef SWIGEXPORT
# if defined(_WIN32) || defined(__WIN32__) || defined(__CYGWIN__)
#   if defined(STATIC_LINKED)
#     define SWIGEXPORT
#   else
#     define SWIGEXPORT __declspec(dllexport)
#   endif
# else
#   if defined(__GNUC__) && defined(GCC_HASCLASSVISIBILITY)
#     define SWIGEXPORT __attribute__ ((visibility("default")))
#   else
#     define SWIGEXPORT
#   endif
# endif
#endif

/* calling conventions for Windows */
#ifndef SWIGSTDCALL
# if defined(_WIN32) || defined(__WIN32__) || defined(__CYGWIN__)
#   define SWIGSTDCALL __stdcall
# else
#   define SWIGSTDCALL
# endif
#endif

/* Deal with Microsoft's attempt at deprecating C standard runtime functions */
#if !defined(SWIG_NO_CRT_SECURE_NO_DEPRECATE) && defined(_MSC_VER) && !defined(_CRT_SECURE_NO_DEPRECATE)
# define _CRT_SECURE_NO_DEPRECATE
#endif

/* Deal with Microsoft's attempt at deprecating methods in the standard C++ library */
#if !defined(SWIG_NO_SCL_SECURE_NO_DEPRECATE) && defined(_MSC_VER) && !defined(_SCL_SECURE_NO_DEPRECATE)
# define _SCL_SECURE_NO_DEPRECATE
#endif

/* Deal with Apple's deprecated 'AssertMacros.h' from Carbon-framework */
#if defined(__APPLE__) && !defined(__ASSERT_MACROS_DEFINE_VERSIONS_WITHOUT_UNDERSCORES)
# define __ASSERT_MACROS_DEFINE_VERSIONS_WITHOUT_UNDERSCORES 0
#endif

/* Intel's compiler complains if a variable which was never initialised is
 * cast to void, which is a common idiom which we use to indicate that we
 * are aware a variable isn't used.  So we just silence that warning.
 * See: https://github.com/swig/swig/issues/192 for more discussion.
 */
#ifdef __INTEL_COMPILER
# pragma warning disable 592
#endif

#if defined(__cplusplus) && __cplusplus >=201103L
# define SWIG_NULLPTR nullptr
#else
# define SWIG_NULLPTR NULL
#endif 

/* -----------------------------------------------------------------------------
 * swigcompat.swg
 *
 * Macros to provide support compatibility with older C and C++ standards.
 * ----------------------------------------------------------------------------- */

/* C99 and C++11 should provide snprintf, but define SWIG_NO_SNPRINTF
 * if you're missing it.
 */
#if ((defined __STDC_VERSION__ && __STDC_VERSION__ >= 199901L) || \
     (defined __cplusplus && __cplusplus >= 201103L) || \
     defined SWIG_HAVE_SNPRINTF) && \
    !defined SWIG_NO_SNPRINTF
# define SWIG_snprintf(O,S,F,A) snprintf(O,S,F,A)
# define SWIG_snprintf2(O,S,F,A,B) snprintf(O,S,F,A,B)
#else
/* Fallback versions ignore the buffer size, but most of our uses either have a
 * fixed maximum possible size or dynamically allocate a buffer that's large
 * enough.
 */
# define SWIG_snprintf(O,S,F,A) sprintf(O,F,A)
# define SWIG_snprintf2(O,S,F,A,B) sprintf(O,F,A,B)
#endif


#if defined(__GNUC__) && defined(_WIN32) && !defined(SWIG_PYTHON_NO_HYPOT_WORKAROUND)
/* Workaround for '::hypot' has not been declared', see https://bugs.python.org/issue11566 */
# include <math.h>
#endif

#if !defined(PY_SSIZE_T_CLEAN) && !defined(SWIG_NO_PY_SSIZE_T_CLEAN)
#define PY_SSIZE_T_CLEAN
#endif

#if __GNUC__ >= 7
#pragma GCC diagnostic push
#if defined(__cplusplus) && __cplusplus >=201703L
#pragma GCC diagnostic ignored "-Wregister" /* For python-2.7 headers that use register */
#endif
#endif

#if defined(_DEBUG) && defined(SWIG_PYTHON_INTERPRETER_NO_DEBUG)
/* Use debug wrappers with the Python release dll */

#if defined(_MSC_VER) && _MSC_VER >= 1929
/* Workaround compilation errors when redefining _DEBUG in MSVC 2019 version 16.10 and later
 * See https://github.com/swig/swig/issues/2090 */
# include <corecrt.h>
#endif

# undef _DEBUG
# include <Python.h>
# define _DEBUG 1
#else
# include <Python.h>
#endif

#if __GNUC__ >= 7
#pragma GCC diagnostic pop
#endif

#include <stdio.h>

/* -----------------------------------------------------------------------------
 * swigrun.swg
 *
 * This file contains generic C API SWIG runtime support for pointer
 * type checking.
 * ----------------------------------------------------------------------------- */

/* This should only be incremented when either the layout of swig_type_info changes,
   or for whatever reason, the runtime changes incompatibly */
#define SWIG_RUNTIME_VERSION "4"

/* define SWIG_TYPE_TABLE_NAME as "SWIG_TYPE_TABLE" */
#ifdef SWIG_TYPE_TABLE
# define SWIG_QUOTE_STRING(x) #x
# define SWIG_EXPAND_AND_QUOTE_STRING(x) SWIG_QUOTE_STRING(x)
# define SWIG_TYPE_TABLE_NAME SWIG_EXPAND_AND_QUOTE_STRING(SWIG_TYPE_TABLE)
#else
# define SWIG_TYPE_TABLE_NAME
#endif

/*
  You can use the SWIGRUNTIME and SWIGRUNTIMEINLINE macros for
  creating a static or dynamic library from the SWIG runtime code.
  In 99.9% of the cases, SWIG just needs to declare them as 'static'.

  But only do this if strictly necessary, ie, if you have problems
  with your compiler or suchlike.
*/

#ifndef SWIGRUNTIME
# define SWIGRUNTIME SWIGINTERN
#endif

#ifndef SWIGRUNTIMEINLINE
# define SWIGRUNTIMEINLINE SWIGRUNTIME SWIGINLINE
#endif

/*  Generic buffer size */
#ifndef SWIG_BUFFER_SIZE
# define SWIG_BUFFER_SIZE 1024
#endif

/* Flags for pointer conversions */
#define SWIG_POINTER_DISOWN        0x1
#define SWIG_CAST_NEW_MEMORY       0x2
#define SWIG_POINTER_NO_NULL       0x4
#define SWIG_POINTER_CLEAR         0x8
#define SWIG_POINTER_RELEASE       (SWIG_POINTER_CLEAR | SWIG_POINTER_DISOWN)

/* Flags for new pointer objects */
#define SWIG_POINTER_OWN           0x1


/*
   Flags/methods for returning states.

   The SWIG conversion methods, as ConvertPtr, return an integer
   that tells if the conversion was successful or not. And if not,
   an error code can be returned (see swigerrors.swg for the codes).

   Use the following macros/flags to set or process the returning
   states.

   In old versions of SWIG, code such as the following was usually written:

     if (SWIG_ConvertPtr(obj,vptr,ty.flags) != -1) {
       // success code
     } else {
       //fail code
     }

   Now you can be more explicit:

    int res = SWIG_ConvertPtr(obj,vptr,ty.flags);
    if (SWIG_IsOK(res)) {
      // success code
    } else {
      // fail code
    }

   which is the same really, but now you can also do

    Type *ptr;
    int res = SWIG_ConvertPtr(obj,(void **)(&ptr),ty.flags);
    if (SWIG_IsOK(res)) {
      // success code
      if (SWIG_IsNewObj(res) {
        ...
	delete *ptr;
      } else {
        ...
      }
    } else {
      // fail code
    }

   I.e., now SWIG_ConvertPtr can return new objects and you can
   identify the case and take care of the deallocation. Of course that
   also requires SWIG_ConvertPtr to return new result values, such as

      int SWIG_ConvertPtr(obj, ptr,...) {
        if (<obj is ok>) {
          if (<need new object>) {
            *ptr = <ptr to new allocated object>;
            return SWIG_NEWOBJ;
          } else {
            *ptr = <ptr to old object>;
            return SWIG_OLDOBJ;
          }
        } else {
          return SWIG_BADOBJ;
        }
      }

   Of course, returning the plain '0(success)/-1(fail)' still works, but you can be
   more explicit by returning SWIG_BADOBJ, SWIG_ERROR or any of the
   SWIG errors code.

   Finally, if the SWIG_CASTRANK_MODE is enabled, the result code
   allows returning the 'cast rank', for example, if you have this

       int food(double)
       int fooi(int);

   and you call

      food(1)   // cast rank '1'  (1 -> 1.0)
      fooi(1)   // cast rank '0'

   just use the SWIG_AddCast()/SWIG_CheckState()
*/

#define SWIG_OK                    (0)
/* Runtime errors are < 0 */
#define SWIG_ERROR                 (-1)
/* Errors in range -1 to -99 are in swigerrors.swg (errors for all languages including those not using the runtime) */
/* Errors in range -100 to -199 are language specific errors defined in *errors.swg */
/* Errors < -200 are generic runtime specific errors */
#define SWIG_ERROR_RELEASE_NOT_OWNED (-200)

#define SWIG_IsOK(r)               (r >= 0)
#define SWIG_ArgError(r)           ((r != SWIG_ERROR) ? r : SWIG_TypeError)

/* The CastRankLimit says how many bits are used for the cast rank */
#define SWIG_CASTRANKLIMIT         (1 << 8)
/* The NewMask denotes the object was created (using new/malloc) */
#define SWIG_NEWOBJMASK            (SWIG_CASTRANKLIMIT  << 1)
/* The TmpMask is for in/out typemaps that use temporary objects */
#define SWIG_TMPOBJMASK            (SWIG_NEWOBJMASK << 1)
/* Simple returning values */
#define SWIG_BADOBJ                (SWIG_ERROR)
#define SWIG_OLDOBJ                (SWIG_OK)
#define SWIG_NEWOBJ                (SWIG_OK | SWIG_NEWOBJMASK)
#define SWIG_TMPOBJ                (SWIG_OK | SWIG_TMPOBJMASK)
/* Check, add and del object mask methods */
#define SWIG_AddNewMask(r)         (SWIG_IsOK(r) ? (r | SWIG_NEWOBJMASK) : r)
#define SWIG_DelNewMask(r)         (SWIG_IsOK(r) ? (r & ~SWIG_NEWOBJMASK) : r)
#define SWIG_IsNewObj(r)           (SWIG_IsOK(r) && (r & SWIG_NEWOBJMASK))
#define SWIG_AddTmpMask(r)         (SWIG_IsOK(r) ? (r | SWIG_TMPOBJMASK) : r)
#define SWIG_DelTmpMask(r)         (SWIG_IsOK(r) ? (r & ~SWIG_TMPOBJMASK) : r)
#define SWIG_IsTmpObj(r)           (SWIG_IsOK(r) && (r & SWIG_TMPOBJMASK))

/* Cast-Rank Mode */
#if defined(SWIG_CASTRANK_MODE)
#  ifndef SWIG_TypeRank
#    define SWIG_TypeRank             unsigned long
#  endif
#  ifndef SWIG_MAXCASTRANK            /* Default cast allowed */
#    define SWIG_MAXCASTRANK          (2)
#  endif
#  define SWIG_CASTRANKMASK          ((SWIG_CASTRANKLIMIT) -1)
#  define SWIG_CastRank(r)           (r & SWIG_CASTRANKMASK)
SWIGINTERNINLINE int SWIG_AddCast(int r) {
  return SWIG_IsOK(r) ? ((SWIG_CastRank(r) < SWIG_MAXCASTRANK) ? (r + 1) : SWIG_ERROR) : r;
}
SWIGINTERNINLINE int SWIG_CheckState(int r) {
  return SWIG_IsOK(r) ? SWIG_CastRank(r) + 1 : 0;
}
#else /* no cast-rank mode */
#  define SWIG_AddCast(r) (r)
#  define SWIG_CheckState(r) (SWIG_IsOK(r) ? 1 : 0)
#endif


#include <string.h>

#ifdef __cplusplus
extern "C" {
#endif

typedef void *(*swig_converter_func)(void *, int *);
typedef struct swig_type_info *(*swig_dycast_func)(void **);

/* Structure to store information on one type */
typedef struct swig_type_info {
  const char             *name;			/* mangled name of this type */
  const char             *str;			/* human readable name of this type */
  swig_dycast_func        dcast;		/* dynamic cast function down a hierarchy */
  struct swig_cast_info  *cast;			/* linked list of types that can cast into this type */
  void                   *clientdata;		/* language specific type data */
  int                    owndata;		/* flag if the structure owns the clientdata */
} swig_type_info;

/* Structure to store a type and conversion function used for casting */
typedef struct swig_cast_info {
  swig_type_info         *type;			/* pointer to type that is equivalent to this type */
  swig_converter_func     converter;		/* function to cast the void pointers */
  struct swig_cast_info  *next;			/* pointer to next cast in linked list */
  struct swig_cast_info  *prev;			/* pointer to the previous cast */
} swig_cast_info;

/* Structure used to store module information
 * Each module generates one structure like this, and the runtime collects
 * all of these structures and stores them in a circularly linked list.*/
typedef struct swig_module_info {
  swig_type_info         **types;		/* Array of pointers to swig_type_info structures that are in this module */
  size_t                 size;		        /* Number of types in this module */
  struct swig_module_info *next;		/* Pointer to next element in circularly linked list */
  swig_type_info         **type_initial;	/* Array of initially generated type structures */
  swig_cast_info         **cast_initial;	/* Array of initially generated casting structures */
  void                    *clientdata;		/* Language specific module data */
} swig_module_info;

/*
  Compare two type names skipping the space characters, therefore
  "char*" == "char *" and "Class<int>" == "Class<int >", etc.

  Return 0 when the two name types are equivalent, as in
  strncmp, but skipping ' '.
*/
SWIGRUNTIME int
SWIG_TypeNameComp(const char *f1, const char *l1,
		  const char *f2, const char *l2) {
  for (;(f1 != l1) && (f2 != l2); ++f1, ++f2) {
    while ((*f1 == ' ') && (f1 != l1)) ++f1;
    while ((*f2 == ' ') && (f2 != l2)) ++f2;
    if (*f1 != *f2) return (*f1 > *f2) ? 1 : -1;
  }
  return (int)((l1 - f1) - (l2 - f2));
}

/*
  Check type equivalence in a name list like <name1>|<name2>|...
  Return 0 if equal, -1 if nb < tb, 1 if nb > tb
*/
SWIGRUNTIME int
SWIG_TypeCmp(const char *nb, const char *tb) {
  int equiv = 1;
  const char* te = tb + strlen(tb);
  const char* ne = nb;
  while (equiv != 0 && *ne) {
    for (nb = ne; *ne; ++ne) {
      if (*ne == '|') break;
    }
    equiv = SWIG_TypeNameComp(nb, ne, tb, te);
    if (*ne) ++ne;
  }
  return equiv;
}

/*
  Check type equivalence in a name list like <name1>|<name2>|...
  Return 0 if not equal, 1 if equal
*/
SWIGRUNTIME int
SWIG_TypeEquiv(const char *nb, const char *tb) {
  return SWIG_TypeCmp(nb, tb) == 0 ? 1 : 0;
}

/*
  Check the typename
*/
SWIGRUNTIME swig_cast_info *
SWIG_TypeCheck(const char *c, swig_type_info *ty) {
  if (ty) {
    swig_cast_info *iter = ty->cast;
    while (iter) {
      if (strcmp(iter->type->name, c) == 0) {
        if (iter == ty->cast)
          return iter;
        /* Move iter to the top of the linked list */
        iter->prev->next = iter->next;
        if (iter->next)
          iter->next->prev = iter->prev;
        iter->next = ty->cast;
        iter->prev = 0;
        if (ty->cast) ty->cast->prev = iter;
        ty->cast = iter;
        return iter;
      }
      iter = iter->next;
    }
  }
  return 0;
}

/*
  Identical to SWIG_TypeCheck, except strcmp is replaced with a pointer comparison
*/
SWIGRUNTIME swig_cast_info *
SWIG_TypeCheckStruct(const swig_type_info *from, swig_type_info *ty) {
  if (ty) {
    swig_cast_info *iter = ty->cast;
    while (iter) {
      if (iter->type == from) {
        if (iter == ty->cast)
          return iter;
        /* Move iter to the top of the linked list */
        iter->prev->next = iter->next;
        if (iter->next)
          iter->next->prev = iter->prev;
        iter->next = ty->cast;
        iter->prev = 0;
        if (ty->cast) ty->cast->prev = iter;
        ty->cast = iter;
        return iter;
      }
      iter = iter->next;
    }
  }
  return 0;
}

/*
  Cast a pointer up an inheritance hierarchy
*/
SWIGRUNTIMEINLINE void *
SWIG_TypeCast(swig_cast_info *ty, void *ptr, int *newmemory) {
  return ((!ty) || (!ty->converter)) ? ptr : (*ty->converter)(ptr, newmemory);
}

/*
   Dynamic pointer casting. Down an inheritance hierarchy
*/
SWIGRUNTIME swig_type_info *
SWIG_TypeDynamicCast(swig_type_info *ty, void **ptr) {
  swig_type_info *lastty = ty;
  if (!ty || !ty->dcast) return ty;
  while (ty && (ty->dcast)) {
    ty = (*ty->dcast)(ptr);
    if (ty) lastty = ty;
  }
  return lastty;
}

/*
  Return the name associated with this type
*/
SWIGRUNTIMEINLINE const char *
SWIG_TypeName(const swig_type_info *ty) {
  return ty->name;
}

/*
  Return the pretty name associated with this type,
  that is an unmangled type name in a form presentable to the user.
*/
SWIGRUNTIME const char *
SWIG_TypePrettyName(const swig_type_info *type) {
  /* The "str" field contains the equivalent pretty names of the
     type, separated by vertical-bar characters.  Choose the last
     name. It should be the most specific; a fully resolved name
     but not necessarily with default template parameters expanded. */
  if (!type) return NULL;
  if (type->str != NULL) {
    const char *last_name = type->str;
    const char *s;
    for (s = type->str; *s; s++)
      if (*s == '|') last_name = s+1;
    return last_name;
  }
  else
    return type->name;
}

/*
   Set the clientdata field for a type
*/
SWIGRUNTIME void
SWIG_TypeClientData(swig_type_info *ti, void *clientdata) {
  swig_cast_info *cast = ti->cast;
  /* if (ti->clientdata == clientdata) return; */
  ti->clientdata = clientdata;

  while (cast) {
    if (!cast->converter) {
      swig_type_info *tc = cast->type;
      if (!tc->clientdata) {
	SWIG_TypeClientData(tc, clientdata);
      }
    }
    cast = cast->next;
  }
}
SWIGRUNTIME void
SWIG_TypeNewClientData(swig_type_info *ti, void *clientdata) {
  SWIG_TypeClientData(ti, clientdata);
  ti->owndata = 1;
}

/*
  Search for a swig_type_info structure only by mangled name
  Search is a O(log #types)

  We start searching at module start, and finish searching when start == end.
  Note: if start == end at the beginning of the function, we go all the way around
  the circular list.
*/
SWIGRUNTIME swig_type_info *
SWIG_MangledTypeQueryModule(swig_module_info *start,
                            swig_module_info *end,
		            const char *name) {
  swig_module_info *iter = start;
  do {
    if (iter->size) {
      size_t l = 0;
      size_t r = iter->size - 1;
      do {
	/* since l+r >= 0, we can (>> 1) instead (/ 2) */
	size_t i = (l + r) >> 1;
	const char *iname = iter->types[i]->name;
	if (iname) {
	  int compare = strcmp(name, iname);
	  if (compare == 0) {
	    return iter->types[i];
	  } else if (compare < 0) {
	    if (i) {
	      r = i - 1;
	    } else {
	      break;
	    }
	  } else if (compare > 0) {
	    l = i + 1;
	  }
	} else {
	  break; /* should never happen */
	}
      } while (l <= r);
    }
    iter = iter->next;
  } while (iter != end);
  return 0;
}

/*
  Search for a swig_type_info structure for either a mangled name or a human readable name.
  It first searches the mangled names of the types, which is a O(log #types)
  If a type is not found it then searches the human readable names, which is O(#types).

  We start searching at module start, and finish searching when start == end.
  Note: if start == end at the beginning of the function, we go all the way around
  the circular list.
*/
SWIGRUNTIME swig_type_info *
SWIG_TypeQueryModule(swig_module_info *start,
                     swig_module_info *end,
		     const char *name) {
  /* STEP 1: Search the name field using binary search */
  swig_type_info *ret = SWIG_MangledTypeQueryModule(start, end, name);
  if (ret) {
    return ret;
  } else {
    /* STEP 2: If the type hasn't been found, do a complete search
       of the str field (the human readable name) */
    swig_module_info *iter = start;
    do {
      size_t i = 0;
      for (; i < iter->size; ++i) {
	if (iter->types[i]->str && (SWIG_TypeEquiv(iter->types[i]->str, name)))
	  return iter->types[i];
      }
      iter = iter->next;
    } while (iter != end);
  }

  /* neither found a match */
  return 0;
}

/*
   Pack binary data into a string
*/
SWIGRUNTIME char *
SWIG_PackData(char *c, void *ptr, size_t sz) {
  static const char hex[17] = "0123456789abcdef";
  const unsigned char *u = (unsigned char *) ptr;
  const unsigned char *eu =  u + sz;
  for (; u != eu; ++u) {
    unsigned char uu = *u;
    *(c++) = hex[(uu & 0xf0) >> 4];
    *(c++) = hex[uu & 0xf];
  }
  return c;
}

/*
   Unpack binary data from a string
*/
SWIGRUNTIME const char *
SWIG_UnpackData(const char *c, void *ptr, size_t sz) {
  unsigned char *u = (unsigned char *) ptr;
  const unsigned char *eu = u + sz;
  for (; u != eu; ++u) {
    char d = *(c++);
    unsigned char uu;
    if ((d >= '0') && (d <= '9'))
      uu = (unsigned char)((d - '0') << 4);
    else if ((d >= 'a') && (d <= 'f'))
      uu = (unsigned char)((d - ('a'-10)) << 4);
    else
      return (char *) 0;
    d = *(c++);
    if ((d >= '0') && (d <= '9'))
      uu |= (unsigned char)(d - '0');
    else if ((d >= 'a') && (d <= 'f'))
      uu |= (unsigned char)(d - ('a'-10));
    else
      return (char *) 0;
    *u = uu;
  }
  return c;
}

/*
   Pack 'void *' into a string buffer.
*/
SWIGRUNTIME char *
SWIG_PackVoidPtr(char *buff, void *ptr, const char *name, size_t bsz) {
  char *r = buff;
  if ((2*sizeof(void *) + 2) > bsz) return 0;
  *(r++) = '_';
  r = SWIG_PackData(r,&ptr,sizeof(void *));
  if (strlen(name) + 1 > (bsz - (r - buff))) return 0;
  strcpy(r,name);
  return buff;
}

SWIGRUNTIME const char *
SWIG_UnpackVoidPtr(const char *c, void **ptr, const char *name) {
  if (*c != '_') {
    if (strcmp(c,"NULL") == 0) {
      *ptr = (void *) 0;
      return name;
    } else {
      return 0;
    }
  }
  return SWIG_UnpackData(++c,ptr,sizeof(void *));
}

SWIGRUNTIME char *
SWIG_PackDataName(char *buff, void *ptr, size_t sz, const char *name, size_t bsz) {
  char *r = buff;
  size_t lname = (name ? strlen(name) : 0);
  if ((2*sz + 2 + lname) > bsz) return 0;
  *(r++) = '_';
  r = SWIG_PackData(r,ptr,sz);
  if (lname) {
    strncpy(r,name,lname+1);
  } else {
    *r = 0;
  }
  return buff;
}

SWIGRUNTIME const char *
SWIG_UnpackDataName(const char *c, void *ptr, size_t sz, const char *name) {
  if (*c != '_') {
    if (strcmp(c,"NULL") == 0) {
      memset(ptr,0,sz);
      return name;
    } else {
      return 0;
    }
  }
  return SWIG_UnpackData(++c,ptr,sz);
}

#ifdef __cplusplus
}
#endif

/* SWIG Errors applicable to all language modules, values are reserved from -1 to -99 */
#define  SWIG_UnknownError    	   -1
#define  SWIG_IOError        	   -2
#define  SWIG_RuntimeError   	   -3
#define  SWIG_IndexError     	   -4
#define  SWIG_TypeError      	   -5
#define  SWIG_DivisionByZero 	   -6
#define  SWIG_OverflowError  	   -7
#define  SWIG_SyntaxError    	   -8
#define  SWIG_ValueError     	   -9
#define  SWIG_SystemError    	   -10
#define  SWIG_AttributeError 	   -11
#define  SWIG_MemoryError    	   -12
#define  SWIG_NullReferenceError   -13


/* Compatibility macros for Python 3 */
#if PY_VERSION_HEX >= 0x03000000

#define PyClass_Check(obj) PyObject_IsInstance(obj, (PyObject *)&PyType_Type)
#define PyInt_Check(x) PyLong_Check(x)
#define PyInt_AsLong(x) PyLong_AsLong(x)
#define PyInt_FromLong(x) PyLong_FromLong(x)
#define PyInt_FromSize_t(x) PyLong_FromSize_t(x)
#define PyString_Check(name) PyBytes_Check(name)
#define PyString_FromString(x) PyUnicode_FromString(x)
#define PyString_Format(fmt, args)  PyUnicode_Format(fmt, args)
#define PyString_AsString(str) PyBytes_AsString(str)
#define PyString_Size(str) PyBytes_Size(str)	
#define PyString_InternFromString(key) PyUnicode_InternFromString(key)
#define Py_TPFLAGS_HAVE_CLASS Py_TPFLAGS_BASETYPE
#define _PyLong_FromSsize_t(x) PyLong_FromSsize_t(x)

#endif

/* SWIG APIs for compatibility of both Python 2 & 3 */

#if PY_VERSION_HEX >= 0x03000000
#  define SWIG_Python_str_FromFormat PyUnicode_FromFormat
#else
#  define SWIG_Python_str_FromFormat PyString_FromFormat
#endif


/* Wrapper around PyUnicode_AsUTF8AndSize - call Py_XDECREF on the returned pbytes when finished with the returned string */
SWIGINTERN const char *
SWIG_PyUnicode_AsUTF8AndSize(PyObject *str, Py_ssize_t *psize, PyObject **pbytes)
{
#if PY_VERSION_HEX >= 0x03030000
# if !defined(Py_LIMITED_API) || Py_LIMITED_API+0 >= 0x030A0000
  *pbytes = NULL;
  return PyUnicode_AsUTF8AndSize(str, psize);
# else
  const char *chars;
  *pbytes = PyUnicode_AsUTF8String(str);
  chars = *pbytes ? PyBytes_AsString(*pbytes) : NULL;
  if (chars && psize)
    *psize = PyBytes_Size(*pbytes);
  return chars;
# endif
#else
  char *chars = NULL;
  *pbytes = NULL;
  PyString_AsStringAndSize(str, &chars, psize);
  return chars;
#endif
}

SWIGINTERN PyObject*
SWIG_Python_str_FromChar(const char *c)
{
#if PY_VERSION_HEX >= 0x03000000
  return PyUnicode_FromString(c); 
#else
  return PyString_FromString(c);
#endif
}

/* SWIGPY_USE_CAPSULE is no longer used within SWIG itself, but some user interface files check for it. */
# define SWIGPY_USE_CAPSULE
#ifdef SWIGPYTHON_BUILTIN
# define SWIGPY_CAPSULE_ATTR_NAME "type_pointer_capsule_builtin" SWIG_TYPE_TABLE_NAME
#else
# define SWIGPY_CAPSULE_ATTR_NAME "type_pointer_capsule" SWIG_TYPE_TABLE_NAME
#endif
# define SWIGPY_CAPSULE_NAME ("swig_runtime_data" SWIG_RUNTIME_VERSION "." SWIGPY_CAPSULE_ATTR_NAME)

#if PY_VERSION_HEX < 0x03020000
#define PyDescr_TYPE(x) (((PyDescrObject *)(x))->d_type)
#define PyDescr_NAME(x) (((PyDescrObject *)(x))->d_name)
#define Py_hash_t long
#endif

#ifdef Py_LIMITED_API
# define PyTuple_GET_ITEM PyTuple_GetItem
/* Note that PyTuple_SetItem() has different semantics from PyTuple_SET_ITEM as it decref's the original tuple item, so in general they cannot be used
  interchangeably. However in SWIG-generated code PyTuple_SET_ITEM is only used with newly initialized tuples without any items and for them this does work. */
# define PyTuple_SET_ITEM PyTuple_SetItem
# define PyTuple_GET_SIZE PyTuple_Size
# define PyCFunction_GET_FLAGS PyCFunction_GetFlags
# define PyCFunction_GET_FUNCTION PyCFunction_GetFunction
# define PyCFunction_GET_SELF PyCFunction_GetSelf
# define PyList_GET_ITEM PyList_GetItem
# define PyList_SET_ITEM PyList_SetItem
# define PySliceObject PyObject
#endif

/* Increment and Decrement wrappers - for portability when using the stable abi and for performance otherwise */
#ifdef Py_LIMITED_API
# define SWIG_Py_INCREF Py_IncRef
# define SWIG_Py_XINCREF Py_IncRef
# define SWIG_Py_DECREF Py_DecRef
# define SWIG_Py_XDECREF Py_DecRef
#else
# define SWIG_Py_INCREF Py_INCREF
# define SWIG_Py_XINCREF Py_XINCREF
# define SWIG_Py_DECREF Py_DECREF
# define SWIG_Py_XDECREF Py_XDECREF
#endif

/* -----------------------------------------------------------------------------
 * error manipulation
 * ----------------------------------------------------------------------------- */

SWIGRUNTIME PyObject*
SWIG_Python_ErrorType(int code) {
  PyObject* type = 0;
  switch(code) {
  case SWIG_MemoryError:
    type = PyExc_MemoryError;
    break;
  case SWIG_IOError:
    type = PyExc_IOError;
    break;
  case SWIG_RuntimeError:
    type = PyExc_RuntimeError;
    break;
  case SWIG_IndexError:
    type = PyExc_IndexError;
    break;
  case SWIG_TypeError:
    type = PyExc_TypeError;
    break;
  case SWIG_DivisionByZero:
    type = PyExc_ZeroDivisionError;
    break;
  case SWIG_OverflowError:
    type = PyExc_OverflowError;
    break;
  case SWIG_SyntaxError:
    type = PyExc_SyntaxError;
    break;
  case SWIG_ValueError:
    type = PyExc_ValueError;
    break;
  case SWIG_SystemError:
    type = PyExc_SystemError;
    break;
  case SWIG_AttributeError:
    type = PyExc_AttributeError;
    break;
  default:
    type = PyExc_RuntimeError;
  }
  return type;
}


SWIGRUNTIME void
SWIG_Python_AddErrorMsg(const char* mesg)
{
  PyObject *type = 0;
  PyObject *value = 0;
  PyObject *traceback = 0;

  if (PyErr_Occurred())
    PyErr_Fetch(&type, &value, &traceback);
  if (value) {
    PyObject *old_str = PyObject_Str(value);
    PyObject *bytes = NULL;
    const char *tmp = SWIG_PyUnicode_AsUTF8AndSize(old_str, NULL, &bytes);
    PyErr_Clear();
    SWIG_Py_XINCREF(type);
    if (tmp)
      PyErr_Format(type, "%s %s", tmp, mesg);
    else
      PyErr_Format(type, "%s", mesg);
    SWIG_Py_XDECREF(bytes);
    SWIG_Py_DECREF(old_str);
    SWIG_Py_DECREF(value);
  } else {
    PyErr_SetString(PyExc_RuntimeError, mesg);
  }
}

SWIGRUNTIME int
SWIG_Python_TypeErrorOccurred(PyObject *obj)
{
  PyObject *error;
  if (obj)
    return 0;
  error = PyErr_Occurred();
  return error && PyErr_GivenExceptionMatches(error, PyExc_TypeError);
}

SWIGRUNTIME void
SWIG_Python_RaiseOrModifyTypeError(const char *message)
{
  if (SWIG_Python_TypeErrorOccurred(NULL)) {
    /* Use existing TypeError to preserve stacktrace and enhance with given message */
    PyObject *newvalue;
    PyObject *type = NULL, *value = NULL, *traceback = NULL;
    PyErr_Fetch(&type, &value, &traceback);
#if PY_VERSION_HEX >= 0x03000000
    newvalue = PyUnicode_FromFormat("%S\nAdditional information:\n%s", value, message);
#else
    newvalue = PyString_FromFormat("%s\nAdditional information:\n%s", PyString_AsString(value), message);
#endif
    if (newvalue) {
      SWIG_Py_XDECREF(value);
      PyErr_Restore(type, newvalue, traceback);
    } else {
      PyErr_Restore(type, value, traceback);
    }
  } else {
    /* Raise TypeError using given message */
    PyErr_SetString(PyExc_TypeError, message);
  }
}

#if defined(SWIG_PYTHON_NO_THREADS)
#  if defined(SWIG_PYTHON_THREADS)
#    undef SWIG_PYTHON_THREADS
#  endif
#endif
#if defined(SWIG_PYTHON_THREADS) /* Threading support is enabled */
#  if !defined(SWIG_PYTHON_USE_GIL) && !defined(SWIG_PYTHON_NO_USE_GIL)
#    define SWIG_PYTHON_USE_GIL
#  endif
#  if defined(SWIG_PYTHON_USE_GIL) /* Use PyGILState threads calls */
#    if !defined(SWIG_PYTHON_INITIALIZE_THREADS)
#      if PY_VERSION_HEX < 0x03070000
#        define SWIG_PYTHON_INITIALIZE_THREADS PyEval_InitThreads()
#      else
#        define SWIG_PYTHON_INITIALIZE_THREADS
#      endif
#    endif
#    ifdef __cplusplus /* C++ code */
       class SWIG_Python_Thread_Block {
         bool status;
         PyGILState_STATE state;
       public:
         void end() { if (status) { PyGILState_Release(state); status = false;} }
         SWIG_Python_Thread_Block() : status(true), state(PyGILState_Ensure()) {}
         ~SWIG_Python_Thread_Block() { end(); }
       };
       class SWIG_Python_Thread_Allow {
         bool status;
         PyThreadState *save;
       public:
         void end() { if (status) { status = false; PyEval_RestoreThread(save); }}
         SWIG_Python_Thread_Allow() : status(true), save(PyEval_SaveThread()) {}
         ~SWIG_Python_Thread_Allow() { end(); }
       };
#      define SWIG_PYTHON_THREAD_BEGIN_BLOCK   SWIG_Python_Thread_Block _swig_thread_block
#      define SWIG_PYTHON_THREAD_END_BLOCK     _swig_thread_block.end()
#      define SWIG_PYTHON_THREAD_BEGIN_ALLOW   SWIG_Python_Thread_Allow _swig_thread_allow
#      define SWIG_PYTHON_THREAD_END_ALLOW     _swig_thread_allow.end()
#    else /* C code */
#      define SWIG_PYTHON_THREAD_BEGIN_BLOCK   PyGILState_STATE _swig_thread_block = PyGILState_Ensure()
#      define SWIG_PYTHON_THREAD_END_BLOCK     PyGILState_Release(_swig_thread_block)
#      define SWIG_PYTHON_THREAD_BEGIN_ALLOW   PyThreadState *_swig_thread_allow = PyEval_SaveThread()
#      define SWIG_PYTHON_THREAD_END_ALLOW     PyEval_RestoreThread(_swig_thread_allow)
#    endif
#  else /* Old thread way, not implemented, user must provide it */
#    if !defined(SWIG_PYTHON_INITIALIZE_THREADS)
#      define SWIG_PYTHON_INITIALIZE_THREADS
#    endif
#    if !defined(SWIG_PYTHON_THREAD_BEGIN_BLOCK)
#      define SWIG_PYTHON_THREAD_BEGIN_BLOCK
#    endif
#    if !defined(SWIG_PYTHON_THREAD_END_BLOCK)
#      define SWIG_PYTHON_THREAD_END_BLOCK
#    endif
#    if !defined(SWIG_PYTHON_THREAD_BEGIN_ALLOW)
#      define SWIG_PYTHON_THREAD_BEGIN_ALLOW
#    endif
#    if !defined(SWIG_PYTHON_THREAD_END_ALLOW)
#      define SWIG_PYTHON_THREAD_END_ALLOW
#    endif
#  endif
#else /* No thread support */
#  define SWIG_PYTHON_INITIALIZE_THREADS
#  define SWIG_PYTHON_THREAD_BEGIN_BLOCK
#  define SWIG_PYTHON_THREAD_END_BLOCK
#  define SWIG_PYTHON_THREAD_BEGIN_ALLOW
#  define SWIG_PYTHON_THREAD_END_ALLOW
#endif

/* -----------------------------------------------------------------------------
 * Python API portion that goes into the runtime
 * ----------------------------------------------------------------------------- */

#ifdef __cplusplus
extern "C" {
#endif

/* -----------------------------------------------------------------------------
 * Constant declarations
 * ----------------------------------------------------------------------------- */

/* Constant Types */
#define SWIG_PY_POINTER 4
#define SWIG_PY_BINARY  5

/* Constant information structure */
typedef struct swig_const_info {
  int type;
  const char *name;
  long lvalue;
  double dvalue;
  void   *pvalue;
  swig_type_info **ptype;
} swig_const_info;

#ifdef __cplusplus
}
#endif


/* -----------------------------------------------------------------------------
 * pyrun.swg
 *
 * This file contains the runtime support for Python modules
 * and includes code for managing global variables and pointer
 * type checking.
 *
 * ----------------------------------------------------------------------------- */

#if PY_VERSION_HEX < 0x02070000 /* 2.7.0 */
# error "This version of SWIG only supports Python >= 2.7"
#endif

#if PY_VERSION_HEX >= 0x03000000 && PY_VERSION_HEX < 0x03030000
# error "This version of SWIG only supports Python 3 >= 3.3"
#endif

/* Common SWIG API */

/* for raw pointers */
#define SWIG_Python_ConvertPtr(obj, pptr, type, flags)  SWIG_Python_ConvertPtrAndOwn(obj, pptr, type, flags, 0)
#define SWIG_ConvertPtr(obj, pptr, type, flags)         SWIG_Python_ConvertPtr(obj, pptr, type, flags)
#define SWIG_ConvertPtrAndOwn(obj,pptr,type,flags,own)  SWIG_Python_ConvertPtrAndOwn(obj, pptr, type, flags, own)

#ifdef SWIGPYTHON_BUILTIN
#define SWIG_NewPointerObj(ptr, type, flags)            SWIG_Python_NewPointerObj(self, ptr, type, flags)
#else
#define SWIG_NewPointerObj(ptr, type, flags)            SWIG_Python_NewPointerObj(NULL, ptr, type, flags)
#endif

#define SWIG_InternalNewPointerObj(ptr, type, flags)	SWIG_Python_NewPointerObj(NULL, ptr, type, flags)

#define SWIG_CheckImplicit(ty)                          SWIG_Python_CheckImplicit(ty) 
#define SWIG_AcquirePtr(ptr, src)                       SWIG_Python_AcquirePtr(ptr, src)
#define swig_owntype                                    int

/* for raw packed data */
#define SWIG_ConvertPacked(obj, ptr, sz, ty)            SWIG_Python_ConvertPacked(obj, ptr, sz, ty)
#define SWIG_NewPackedObj(ptr, sz, type)                SWIG_Python_NewPackedObj(ptr, sz, type)

/* for class or struct pointers */
#define SWIG_ConvertInstance(obj, pptr, type, flags)    SWIG_ConvertPtr(obj, pptr, type, flags)
#define SWIG_NewInstanceObj(ptr, type, flags)           SWIG_NewPointerObj(ptr, type, flags)

/* for C or C++ function pointers */
#define SWIG_ConvertFunctionPtr(obj, pptr, type)        SWIG_Python_ConvertFunctionPtr(obj, pptr, type)
#define SWIG_NewFunctionPtrObj(ptr, type)               SWIG_Python_NewPointerObj(NULL, ptr, type, 0)

/* for C++ member pointers, ie, member methods */
#define SWIG_ConvertMember(obj, ptr, sz, ty)            SWIG_Python_ConvertPacked(obj, ptr, sz, ty)
#define SWIG_NewMemberObj(ptr, sz, type)                SWIG_Python_NewPackedObj(ptr, sz, type)


/* Runtime API */

#define SWIG_GetModule(clientdata)                      SWIG_Python_GetModule(clientdata)
#define SWIG_SetModule(clientdata, pointer)             SWIG_Python_SetModule(pointer)
#define SWIG_NewClientData(obj)                         SwigPyClientData_New(obj)

#define SWIG_SetErrorObj                                SWIG_Python_SetErrorObj                            
#define SWIG_SetErrorMsg                        	SWIG_Python_SetErrorMsg				   
#define SWIG_ErrorType(code)                    	SWIG_Python_ErrorType(code)                        
#define SWIG_Error(code, msg)            		SWIG_Python_SetErrorMsg(SWIG_ErrorType(code), msg) 
#define SWIG_fail                        		goto fail					   


/* Runtime API implementation */

/* Error manipulation */

SWIGINTERN void 
SWIG_Python_SetErrorObj(PyObject *errtype, PyObject *obj) {
  SWIG_PYTHON_THREAD_BEGIN_BLOCK; 
  PyErr_SetObject(errtype, obj);
  SWIG_Py_DECREF(obj);
  SWIG_PYTHON_THREAD_END_BLOCK;
}

SWIGINTERN void 
SWIG_Python_SetErrorMsg(PyObject *errtype, const char *msg) {
  SWIG_PYTHON_THREAD_BEGIN_BLOCK;
  PyErr_SetString(errtype, msg);
  SWIG_PYTHON_THREAD_END_BLOCK;
}

#define SWIG_Python_Raise(obj, type, desc)  SWIG_Python_SetErrorObj(SWIG_Python_ExceptionType(desc), obj)

/* Set a constant value */

#if defined(SWIGPYTHON_BUILTIN)

SWIGINTERN void
SwigPyBuiltin_AddPublicSymbol(PyObject *seq, const char *key) {
  PyObject *s = PyString_InternFromString(key);
  PyList_Append(seq, s);
  SWIG_Py_DECREF(s);
}

SWIGINTERN void
SWIG_Python_SetConstant(PyObject *d, PyObject *public_interface, const char *name, PyObject *obj) {   
  PyDict_SetItemString(d, name, obj);
  SWIG_Py_DECREF(obj);
  if (public_interface)
    SwigPyBuiltin_AddPublicSymbol(public_interface, name);
}

#else

SWIGINTERN void
SWIG_Python_SetConstant(PyObject *d, const char *name, PyObject *obj) {   
  PyDict_SetItemString(d, name, obj);
  SWIG_Py_DECREF(obj);
}

#endif

/* Append a value to the result obj */

SWIGINTERN PyObject*
SWIG_Python_AppendOutput(PyObject* result, PyObject* obj) {
  if (!result) {
    result = obj;
  } else if (result == Py_None) {
    SWIG_Py_DECREF(result);
    result = obj;
  } else {
    if (!PyList_Check(result)) {
      PyObject *o2 = result;
      result = PyList_New(1);
      if (result) {
        PyList_SET_ITEM(result, 0, o2);
      } else {
        SWIG_Py_DECREF(obj);
        return o2;
      }
    }
    PyList_Append(result,obj);
    SWIG_Py_DECREF(obj);
  }
  return result;
}

/* Unpack the argument tuple */

SWIGINTERN Py_ssize_t
SWIG_Python_UnpackTuple(PyObject *args, const char *name, Py_ssize_t min, Py_ssize_t max, PyObject **objs)
{
  if (!args) {
    if (!min && !max) {
      return 1;
    } else {
      PyErr_Format(PyExc_TypeError, "%s expected %s%d arguments, got none", 
		   name, (min == max ? "" : "at least "), (int)min);
      return 0;
    }
  }  
  if (!PyTuple_Check(args)) {
    if (min <= 1 && max >= 1) {
      Py_ssize_t i;
      objs[0] = args;
      for (i = 1; i < max; ++i) {
	objs[i] = 0;
      }
      return 2;
    }
    PyErr_SetString(PyExc_SystemError, "UnpackTuple() argument list is not a tuple");
    return 0;
  } else {
    Py_ssize_t l = PyTuple_GET_SIZE(args);
    if (l < min) {
      PyErr_Format(PyExc_TypeError, "%s expected %s%d arguments, got %d", 
		   name, (min == max ? "" : "at least "), (int)min, (int)l);
      return 0;
    } else if (l > max) {
      PyErr_Format(PyExc_TypeError, "%s expected %s%d arguments, got %d", 
		   name, (min == max ? "" : "at most "), (int)max, (int)l);
      return 0;
    } else {
      Py_ssize_t i;
      for (i = 0; i < l; ++i) {
	objs[i] = PyTuple_GET_ITEM(args, i);
      }
      for (; l < max; ++l) {
	objs[l] = 0;
      }
      return i + 1;
    }    
  }
}

SWIGINTERN int
SWIG_Python_CheckNoKeywords(PyObject *kwargs, const char *name) {
  int no_kwargs = 1;
  if (kwargs) {
    assert(PyDict_Check(kwargs));
    if (PyDict_Size(kwargs) > 0) {
      PyErr_Format(PyExc_TypeError, "%s() does not take keyword arguments", name);
      no_kwargs = 0;
    }
  }
  return no_kwargs;
}

/* A functor is a function object with one single object argument */
#define SWIG_Python_CallFunctor(functor, obj)	        PyObject_CallFunctionObjArgs(functor, obj, NULL);

/*
  Helper for static pointer initialization for both C and C++ code, for example
  static PyObject *SWIG_STATIC_POINTER(MyVar) = NewSomething(...);
*/
#ifdef __cplusplus
#define SWIG_STATIC_POINTER(var)  var
#else
#define SWIG_STATIC_POINTER(var)  var = 0; if (!var) var
#endif

#ifdef __cplusplus
extern "C" {
#endif

/* Python-specific SWIG API */
#define SWIG_newvarlink()                             SWIG_Python_newvarlink()
#define SWIG_addvarlink(p, name, get_attr, set_attr)  SWIG_Python_addvarlink(p, name, get_attr, set_attr)
#define SWIG_InstallConstants(d, constants)           SWIG_Python_InstallConstants(d, constants)
 
/* -----------------------------------------------------------------------------
 * global variable support code.
 * ----------------------------------------------------------------------------- */
 
typedef struct swig_globalvar {   
  char       *name;                  /* Name of global variable */
  PyObject *(*get_attr)(void);       /* Return the current value */
  int       (*set_attr)(PyObject *); /* Set the value */
  struct swig_globalvar *next;
} swig_globalvar;

typedef struct swig_varlinkobject {
  PyObject_HEAD
  swig_globalvar *vars;
} swig_varlinkobject;

SWIGINTERN PyObject *
swig_varlink_repr(PyObject *SWIGUNUSEDPARM(v)) {
#if PY_VERSION_HEX >= 0x03000000
  return PyUnicode_InternFromString("<Swig global variables>");
#else
  return PyString_FromString("<Swig global variables>");
#endif
}

SWIGINTERN PyObject *
swig_varlink_str(PyObject *o) {
  swig_varlinkobject *v = (swig_varlinkobject *) o;
#if PY_VERSION_HEX >= 0x03000000
  PyObject *str = PyUnicode_InternFromString("(");
  PyObject *tail;
  PyObject *joined;
  swig_globalvar *var;
  for (var = v->vars; var; var=var->next) {
    tail = PyUnicode_FromString(var->name);
    joined = PyUnicode_Concat(str, tail);
    SWIG_Py_DECREF(str);
    SWIG_Py_DECREF(tail);
    str = joined;
    if (var->next) {
        tail = PyUnicode_InternFromString(", ");
        joined = PyUnicode_Concat(str, tail);
        SWIG_Py_DECREF(str);
        SWIG_Py_DECREF(tail);
        str = joined;
    }
  }
  tail = PyUnicode_InternFromString(")");
  joined = PyUnicode_Concat(str, tail);
  SWIG_Py_DECREF(str);
  SWIG_Py_DECREF(tail);
  str = joined;
#else
  PyObject *str = PyString_FromString("(");
  swig_globalvar *var;
  for (var = v->vars; var; var=var->next) {
    PyString_ConcatAndDel(&str,PyString_FromString(var->name));
    if (var->next) PyString_ConcatAndDel(&str,PyString_FromString(", "));
  }
  PyString_ConcatAndDel(&str,PyString_FromString(")"));
#endif
  return str;
}

SWIGINTERN void
swig_varlink_dealloc(PyObject *o) {
  swig_varlinkobject *v = (swig_varlinkobject *) o;
  swig_globalvar *var = v->vars;
  while (var) {
    swig_globalvar *n = var->next;
    free(var->name);
    free(var);
    var = n;
  }
}

SWIGINTERN PyObject *
swig_varlink_getattr(PyObject *o, char *n) {
  swig_varlinkobject *v = (swig_varlinkobject *) o;
  PyObject *res = NULL;
  swig_globalvar *var = v->vars;
  while (var) {
    if (strcmp(var->name,n) == 0) {
      res = (*var->get_attr)();
      break;
    }
    var = var->next;
  }
  if (res == NULL && !PyErr_Occurred()) {
    PyErr_Format(PyExc_AttributeError, "Unknown C global variable '%s'", n);
  }
  return res;
}

SWIGINTERN int
swig_varlink_setattr(PyObject *o, char *n, PyObject *p) {
  swig_varlinkobject *v = (swig_varlinkobject *) o;
  int res = 1;
  swig_globalvar *var = v->vars;
  while (var) {
    if (strcmp(var->name,n) == 0) {
      res = (*var->set_attr)(p);
      break;
    }
    var = var->next;
  }
  if (res == 1 && !PyErr_Occurred()) {
    PyErr_Format(PyExc_AttributeError, "Unknown C global variable '%s'", n);
  }
  return res;
}

SWIGINTERN PyTypeObject*
swig_varlink_type(void) {
  static char varlink__doc__[] = "Swig var link object";
#ifndef Py_LIMITED_API
  static PyTypeObject varlink_type;
  static int type_init = 0;
  if (!type_init) {
    const PyTypeObject tmp = {
#if PY_VERSION_HEX >= 0x03000000
      PyVarObject_HEAD_INIT(NULL, 0)
#else
      PyObject_HEAD_INIT(NULL)
      0,                                  /* ob_size */
#endif
      "swigvarlink",                      /* tp_name */
      sizeof(swig_varlinkobject),         /* tp_basicsize */
      0,                                  /* tp_itemsize */
      (destructor) swig_varlink_dealloc,  /* tp_dealloc */
#if PY_VERSION_HEX < 0x030800b4
      (printfunc)0,                       /*tp_print*/
#else
      (Py_ssize_t)0,                      /*tp_vectorcall_offset*/
#endif
      (getattrfunc) swig_varlink_getattr, /* tp_getattr */
      (setattrfunc) swig_varlink_setattr, /* tp_setattr */
      0,                                  /* tp_compare */
      (reprfunc) swig_varlink_repr,       /* tp_repr */
      0,                                  /* tp_as_number */
      0,                                  /* tp_as_sequence */
      0,                                  /* tp_as_mapping */
      0,                                  /* tp_hash */
      0,                                  /* tp_call */
      (reprfunc) swig_varlink_str,        /* tp_str */
      0,                                  /* tp_getattro */
      0,                                  /* tp_setattro */
      0,                                  /* tp_as_buffer */
      0,                                  /* tp_flags */
      varlink__doc__,                     /* tp_doc */
      0,                                  /* tp_traverse */
      0,                                  /* tp_clear */
      0,                                  /* tp_richcompare */
      0,                                  /* tp_weaklistoffset */
      0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0, /* tp_iter -> tp_weaklist */
      0,                                  /* tp_del */
      0,                                  /* tp_version_tag */
#if PY_VERSION_HEX >= 0x03040000
      0,                                  /* tp_finalize */
#endif
#if PY_VERSION_HEX >= 0x03080000
      0,                                  /* tp_vectorcall */
#endif
#if (PY_VERSION_HEX >= 0x03080000) && (PY_VERSION_HEX < 0x03090000)
      0,                                  /* tp_print */
#endif
#if PY_VERSION_HEX >= 0x030C0000
      0,                                  /* tp_watched */
#endif
#ifdef COUNT_ALLOCS
      0,                                  /* tp_allocs */
      0,                                  /* tp_frees */
      0,                                  /* tp_maxalloc */
      0,                                  /* tp_prev */
      0                                   /* tp_next */
#endif
    };
    varlink_type = tmp;
    type_init = 1;
    if (PyType_Ready(&varlink_type) < 0)
      return NULL;
  }
  return &varlink_type;
#else
  PyType_Slot slots[] = {
    { Py_tp_dealloc, (void *)swig_varlink_dealloc },
    { Py_tp_repr, (void *)swig_varlink_repr },
    { Py_tp_getattr, (void *)swig_varlink_getattr },
    { Py_tp_setattr, (void *)swig_varlink_setattr },
    { Py_tp_str, (void *)swig_varlink_str },
    { Py_tp_doc, (void *)varlink__doc__ },
    { 0, NULL }
  };
  PyType_Spec spec = {
    "swigvarlink",
    sizeof(swig_varlinkobject),
    0,
    Py_TPFLAGS_DEFAULT,
    slots
  };
  return (PyTypeObject *)PyType_FromSpec(&spec);
#endif
}

/* Create a variable linking object for use later */
SWIGINTERN PyObject *
SWIG_Python_newvarlink(void) {
  swig_varlinkobject *result = PyObject_New(swig_varlinkobject, swig_varlink_type());
  if (result) {
    result->vars = 0;
  }
  return ((PyObject*) result);
}

SWIGINTERN void 
SWIG_Python_addvarlink(PyObject *p, const char *name, PyObject *(*get_attr)(void), int (*set_attr)(PyObject *p)) {
  swig_varlinkobject *v = (swig_varlinkobject *) p;
  swig_globalvar *gv = (swig_globalvar *) malloc(sizeof(swig_globalvar));
  if (gv) {
    size_t size = strlen(name)+1;
    gv->name = (char *)malloc(size);
    if (gv->name) {
      memcpy(gv->name, name, size);
      gv->get_attr = get_attr;
      gv->set_attr = set_attr;
      gv->next = v->vars;
    }
  }
  v->vars = gv;
}


static PyObject *Swig_Globals_global = NULL;
  
SWIGINTERN PyObject *
SWIG_globals(void) {
  if (Swig_Globals_global == NULL) {
    Swig_Globals_global = SWIG_newvarlink();
  }
  return Swig_Globals_global;
}

#ifdef __cplusplus
}
#endif

/* -----------------------------------------------------------------------------
 * Pointer declarations
 * ----------------------------------------------------------------------------- */

/* Flags for new pointer objects */
#define SWIG_POINTER_NOSHADOW       (SWIG_POINTER_OWN      << 1)
#define SWIG_POINTER_NEW            (SWIG_POINTER_NOSHADOW | SWIG_POINTER_OWN)

#define SWIG_POINTER_IMPLICIT_CONV  (SWIG_POINTER_DISOWN   << 1)

#define SWIG_BUILTIN_TP_INIT	    (SWIG_POINTER_OWN << 2)
#define SWIG_BUILTIN_INIT	    (SWIG_BUILTIN_TP_INIT | SWIG_POINTER_OWN)

#ifdef __cplusplus
extern "C" {
#endif

/* The python void return value */

SWIGRUNTIMEINLINE PyObject * 
SWIG_Py_Void(void)
{
  PyObject *none = Py_None;
  SWIG_Py_INCREF(none);
  return none;
}

/* SwigPyClientData */

typedef struct {
  PyObject *klass;
  PyObject *newraw;
  PyObject *newargs;
  PyObject *destroy;
  int delargs;
  int implicitconv;
  PyTypeObject *pytype;
} SwigPyClientData;

SWIGRUNTIMEINLINE int 
SWIG_Python_CheckImplicit(swig_type_info *ty)
{
  SwigPyClientData *data = (SwigPyClientData *)ty->clientdata;
  int fail = data ? data->implicitconv : 0;
  if (fail)
    PyErr_SetString(PyExc_TypeError, "Implicit conversion is prohibited for explicit constructors.");
  return fail;
}

SWIGRUNTIMEINLINE PyObject *
SWIG_Python_ExceptionType(swig_type_info *desc) {
  SwigPyClientData *data = desc ? (SwigPyClientData *) desc->clientdata : 0;
  PyObject *klass = data ? data->klass : 0;
  return (klass ? klass : PyExc_RuntimeError);
}


SWIGRUNTIME SwigPyClientData * 
SwigPyClientData_New(PyObject* obj)
{
  if (!obj) {
    return 0;
  } else {
    SwigPyClientData *data = (SwigPyClientData *)malloc(sizeof(SwigPyClientData));
    /* the klass element */
    data->klass = obj;
    SWIG_Py_INCREF(data->klass);
    /* the newraw method and newargs arguments used to create a new raw instance */
    if (PyClass_Check(obj)) {
      data->newraw = 0;
      SWIG_Py_INCREF(obj);
      data->newargs = obj;
    } else {
      data->newraw = PyObject_GetAttrString(data->klass, "__new__");
      if (data->newraw) {
        data->newargs = PyTuple_New(1);
        if (data->newargs) {
          SWIG_Py_INCREF(obj);
          PyTuple_SET_ITEM(data->newargs, 0, obj);
        } else {
          SWIG_Py_DECREF(data->newraw);
          SWIG_Py_DECREF(data->klass);
          free(data);
          return 0;
        }
      } else {
        SWIG_Py_INCREF(obj);
        data->newargs = obj;
      }
    }
    /* the destroy method, aka as the C++ delete method */
    data->destroy = PyObject_GetAttrString(data->klass, "__swig_destroy__");
    if (PyErr_Occurred()) {
      PyErr_Clear();
      data->destroy = 0;
    }
    if (data->destroy) {
      data->delargs = !(PyCFunction_GET_FLAGS(data->destroy) & METH_O);
    } else {
      data->delargs = 0;
    }
    data->implicitconv = 0;
    data->pytype = 0;
    return data;
  }
}

SWIGRUNTIME void 
SwigPyClientData_Del(SwigPyClientData *data)
{
  SWIG_Py_XDECREF(data->klass);
  SWIG_Py_XDECREF(data->newraw);
  SWIG_Py_XDECREF(data->newargs);
  SWIG_Py_XDECREF(data->destroy);
  free(data);
}

/* =============== SwigPyObject =====================*/

typedef struct {
  PyObject_HEAD
  void *ptr;
  swig_type_info *ty;
  int own;
  PyObject *next;
#ifdef SWIGPYTHON_BUILTIN
  PyObject *dict;
#endif
} SwigPyObject;


#ifdef SWIGPYTHON_BUILTIN

SWIGRUNTIME PyObject *
SwigPyObject_get___dict__(PyObject *v, PyObject *SWIGUNUSEDPARM(args))
{
  SwigPyObject *sobj = (SwigPyObject *)v;

  if (!sobj->dict)
    sobj->dict = PyDict_New();

  SWIG_Py_XINCREF(sobj->dict);
  return sobj->dict;
}

#endif

SWIGRUNTIME PyObject *
SwigPyObject_long(SwigPyObject *v)
{
  return PyLong_FromVoidPtr(v->ptr);
}

SWIGRUNTIME PyObject *
SwigPyObject_format(const char* fmt, SwigPyObject *v)
{
  PyObject *res = NULL;
  PyObject *args = PyTuple_New(1);
  if (args) {
    PyObject *val = SwigPyObject_long(v);
    if (val) {
      PyObject *ofmt;
      PyTuple_SET_ITEM(args, 0, val);
      ofmt = SWIG_Python_str_FromChar(fmt);
      if (ofmt) {
#if PY_VERSION_HEX >= 0x03000000
        res = PyUnicode_Format(ofmt,args);
#else
        res = PyString_Format(ofmt,args);
#endif
        SWIG_Py_DECREF(ofmt);
      }
    }
    SWIG_Py_DECREF(args);
  }
  return res;
}

SWIGRUNTIME PyObject *
SwigPyObject_oct(SwigPyObject *v)
{
  return SwigPyObject_format("%o",v);
}

SWIGRUNTIME PyObject *
SwigPyObject_hex(SwigPyObject *v)
{
  return SwigPyObject_format("%x",v);
}

SWIGRUNTIME PyObject *
SwigPyObject_repr(SwigPyObject *v)
{
  const char *name = SWIG_TypePrettyName(v->ty);
  PyObject *repr = SWIG_Python_str_FromFormat("<Swig Object of type '%s' at %p>", (name ? name : "unknown"), (void *)v);
  if (repr && v->next) {
    PyObject *nrep = SwigPyObject_repr((SwigPyObject *)v->next);
    if (nrep) {
# if PY_VERSION_HEX >= 0x03000000
      PyObject *joined = PyUnicode_Concat(repr, nrep);
      SWIG_Py_DECREF(repr);
      SWIG_Py_DECREF(nrep);
      repr = joined;
# else
      PyString_ConcatAndDel(&repr,nrep);
# endif
    } else {
      SWIG_Py_DECREF(repr);
      repr = NULL;
    }
  }
  return repr;
}

/* We need a version taking two PyObject* parameters so it's a valid
 * PyCFunction to use in swigobject_methods[]. */
SWIGRUNTIME PyObject *
SwigPyObject_repr2(PyObject *v, PyObject *SWIGUNUSEDPARM(args))
{
  return SwigPyObject_repr((SwigPyObject*)v);
}

SWIGRUNTIME int
SwigPyObject_compare(SwigPyObject *v, SwigPyObject *w)
{
  void *i = v->ptr;
  void *j = w->ptr;
  return (i < j) ? -1 : ((i > j) ? 1 : 0);
}

/* Added for Python 3.x, would it also be useful for Python 2.x? */
SWIGRUNTIME PyObject*
SwigPyObject_richcompare(SwigPyObject *v, SwigPyObject *w, int op)
{
  PyObject* res = NULL;
  if (!PyErr_Occurred()) {
    if (op != Py_EQ && op != Py_NE) {
      SWIG_Py_INCREF(Py_NotImplemented);
      return Py_NotImplemented;
    }
    res = PyBool_FromLong( (SwigPyObject_compare(v, w)==0) == (op == Py_EQ) ? 1 : 0);
  }
  return res;  
}


SWIGRUNTIME PyTypeObject* SwigPyObject_TypeOnce(void);

#ifdef SWIGPYTHON_BUILTIN
static swig_type_info *SwigPyObject_stype = 0;
SWIGRUNTIME PyTypeObject*
SwigPyObject_type(void) {
    SwigPyClientData *cd;
    assert(SwigPyObject_stype);
    cd = (SwigPyClientData*) SwigPyObject_stype->clientdata;
    assert(cd);
    assert(cd->pytype);
    return cd->pytype;
}
#else
SWIGRUNTIME PyTypeObject*
SwigPyObject_type(void) {
  static PyTypeObject *SWIG_STATIC_POINTER(type) = SwigPyObject_TypeOnce();
  return type;
}
#endif

SWIGRUNTIMEINLINE int
SwigPyObject_Check(PyObject *op) {
  PyTypeObject *target_tp = SwigPyObject_type();
  PyTypeObject *op_type = Py_TYPE(op);
#ifdef SWIGPYTHON_BUILTIN
  if (PyType_IsSubtype(op_type, target_tp))
    return 1;
  return (strcmp(op_type->tp_name, "SwigPyObject") == 0);
#else
# ifdef Py_LIMITED_API
  int cmp;
  PyObject *tp_name;
#endif
  if (op_type == target_tp)
    return 1;
# ifdef Py_LIMITED_API
  tp_name = PyObject_GetAttrString((PyObject *)op_type, "__name__");
  if (!tp_name)
    return 0;
  cmp = PyUnicode_CompareWithASCIIString(tp_name, "SwigPyObject");
  SWIG_Py_DECREF(tp_name);
  return cmp == 0;
# else
  return (strcmp(op_type->tp_name, "SwigPyObject") == 0);
# endif
#endif
}

SWIGRUNTIME PyObject *
SwigPyObject_New(void *ptr, swig_type_info *ty, int own);

static PyObject* Swig_Capsule_global = NULL;

SWIGRUNTIME void
SwigPyObject_dealloc(PyObject *v)
{
  SwigPyObject *sobj = (SwigPyObject *) v;
  PyObject *next = sobj->next;
  if (sobj->own == SWIG_POINTER_OWN) {
    swig_type_info *ty = sobj->ty;
    SwigPyClientData *data = ty ? (SwigPyClientData *) ty->clientdata : 0;
    PyObject *destroy = data ? data->destroy : 0;
    if (destroy) {
      /* destroy is always a VARARGS method */
      PyObject *res;

      /* PyObject_CallFunction() has the potential to silently drop
         the active exception.  In cases of unnamed temporary
         variable or where we just finished iterating over a generator
         StopIteration will be active right now, and this needs to
         remain true upon return from SwigPyObject_dealloc.  So save
         and restore. */
      
      PyObject *type = NULL, *value = NULL, *traceback = NULL;
      PyErr_Fetch(&type, &value, &traceback);

      if (data->delargs) {
        /* we need to create a temporary object to carry the destroy operation */
        PyObject *tmp = SwigPyObject_New(sobj->ptr, ty, 0);
        if (tmp) {
          res = SWIG_Python_CallFunctor(destroy, tmp);
        } else {
          res = 0;
        }
        SWIG_Py_XDECREF(tmp);
      } else {
        PyCFunction meth = PyCFunction_GET_FUNCTION(destroy);
        PyObject *mself = PyCFunction_GET_SELF(destroy);
        res = ((*meth)(mself, v));
      }
      if (!res)
        PyErr_WriteUnraisable(destroy);

      PyErr_Restore(type, value, traceback);

      SWIG_Py_XDECREF(res);
    } 
#if !defined(SWIG_PYTHON_SILENT_MEMLEAK)
    else {
      const char *name = SWIG_TypePrettyName(ty);
      printf("swig/python detected a memory leak of type '%s', no destructor found.\n", (name ? name : "unknown"));
    }
#endif
    SWIG_Py_XDECREF(Swig_Capsule_global);
  }
  SWIG_Py_XDECREF(next);
#ifdef SWIGPYTHON_BUILTIN
  SWIG_Py_XDECREF(sobj->dict);
#endif
  PyObject_Free(v);
}

SWIGRUNTIME PyObject* 
SwigPyObject_append(PyObject* v, PyObject* next)
{
  SwigPyObject *sobj = (SwigPyObject *) v;
  if (!SwigPyObject_Check(next)) {
    PyErr_SetString(PyExc_TypeError, "Attempt to append a non SwigPyObject");
    return NULL;
  }
  ((SwigPyObject *)next)->next = sobj->next;
  sobj->next = next;
  SWIG_Py_INCREF(next);
  return SWIG_Py_Void();
}

SWIGRUNTIME PyObject* 
SwigPyObject_next(PyObject* v, PyObject *SWIGUNUSEDPARM(args))
{
  SwigPyObject *sobj = (SwigPyObject *) v;
  if (sobj->next) {    
    SWIG_Py_INCREF(sobj->next);
    return sobj->next;
  } else {
    return SWIG_Py_Void();
  }
}

SWIGINTERN PyObject*
SwigPyObject_disown(PyObject* v, PyObject *SWIGUNUSEDPARM(args))
{
  SwigPyObject *sobj = (SwigPyObject *)v;
  sobj->own = 0;
  return SWIG_Py_Void();
}

SWIGINTERN PyObject*
SwigPyObject_acquire(PyObject* v, PyObject *SWIGUNUSEDPARM(args))
{
  SwigPyObject *sobj = (SwigPyObject *)v;
  sobj->own = SWIG_POINTER_OWN;
  return SWIG_Py_Void();
}

SWIGINTERN PyObject*
SwigPyObject_own(PyObject *v, PyObject *args)
{
  PyObject *val = 0;
  if (!PyArg_UnpackTuple(args, "own", 0, 1, &val)) {
    return NULL;
  } else {
    SwigPyObject *sobj = (SwigPyObject *)v;
    PyObject *obj = PyBool_FromLong(sobj->own);
    if (val) {
      if (PyObject_IsTrue(val)) {
        SWIG_Py_DECREF(SwigPyObject_acquire(v,args));
      } else {
        SWIG_Py_DECREF(SwigPyObject_disown(v,args));
      }
    } 
    return obj;
  }
}

static PyMethodDef
swigobject_methods[] = {
  {"disown",  SwigPyObject_disown,  METH_NOARGS,  "releases ownership of the pointer"},
  {"acquire", SwigPyObject_acquire, METH_NOARGS,  "acquires ownership of the pointer"},
  {"own",     SwigPyObject_own,     METH_VARARGS, "returns/sets ownership of the pointer"},
  {"append",  SwigPyObject_append,  METH_O,       "appends another 'this' object"},
  {"next",    SwigPyObject_next,    METH_NOARGS,  "returns the next 'this' object"},
  {"__repr__",SwigPyObject_repr2,   METH_NOARGS,  "returns object representation"},
  {0, 0, 0, 0}  
};

SWIGRUNTIME PyTypeObject*
SwigPyObject_TypeOnce(void) {
  static char swigobject_doc[] = "Swig object carries a C/C++ instance pointer";
#ifndef Py_LIMITED_API
  static PyNumberMethods SwigPyObject_as_number = {
    (binaryfunc)0, /*nb_add*/
    (binaryfunc)0, /*nb_subtract*/
    (binaryfunc)0, /*nb_multiply*/
    /* nb_divide removed in Python 3 */
#if PY_VERSION_HEX < 0x03000000
    (binaryfunc)0, /*nb_divide*/
#endif
    (binaryfunc)0, /*nb_remainder*/
    (binaryfunc)0, /*nb_divmod*/
    (ternaryfunc)0,/*nb_power*/
    (unaryfunc)0,  /*nb_negative*/
    (unaryfunc)0,  /*nb_positive*/
    (unaryfunc)0,  /*nb_absolute*/
    (inquiry)0,    /*nb_nonzero*/
    0,		   /*nb_invert*/
    0,		   /*nb_lshift*/
    0,		   /*nb_rshift*/
    0,		   /*nb_and*/
    0,		   /*nb_xor*/
    0,		   /*nb_or*/
#if PY_VERSION_HEX < 0x03000000
    0,   /*nb_coerce*/
#endif
    (unaryfunc)SwigPyObject_long, /*nb_int*/
#if PY_VERSION_HEX < 0x03000000
    (unaryfunc)SwigPyObject_long, /*nb_long*/
#else
    0, /*nb_reserved*/
#endif
    (unaryfunc)0,                 /*nb_float*/
#if PY_VERSION_HEX < 0x03000000
    (unaryfunc)SwigPyObject_oct,  /*nb_oct*/
    (unaryfunc)SwigPyObject_hex,  /*nb_hex*/
#endif
#if PY_VERSION_HEX >= 0x03050000 /* 3.5 */
    0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0 /* nb_inplace_add -> nb_inplace_matrix_multiply */
#elif PY_VERSION_HEX >= 0x03000000 /* 3.0 */
    0,0,0,0,0,0,0,0,0,0,0,0,0,0,0 /* nb_inplace_add -> nb_index, nb_inplace_divide removed */
#else
    0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0 /* nb_inplace_add -> nb_index */
#endif
  };

  static PyTypeObject swigpyobject_type;
  static int type_init = 0;
  if (!type_init) {
    const PyTypeObject tmp = {
#if PY_VERSION_HEX >= 0x03000000
      PyVarObject_HEAD_INIT(NULL, 0)
#else
      PyObject_HEAD_INIT(NULL)
      0,                                    /* ob_size */
#endif
      "SwigPyObject",                       /* tp_name */
      sizeof(SwigPyObject),                 /* tp_basicsize */
      0,                                    /* tp_itemsize */
      (destructor)SwigPyObject_dealloc,     /* tp_dealloc */
#if PY_VERSION_HEX < 0x030800b4
      (printfunc)0,                         /*tp_print*/
#else
      (Py_ssize_t)0,                        /*tp_vectorcall_offset*/
#endif
      (getattrfunc)0,                       /* tp_getattr */
      (setattrfunc)0,                       /* tp_setattr */
#if PY_VERSION_HEX >= 0x03000000
      0, /* tp_reserved in 3.0.1, tp_compare in 3.0.0 but not used */
#else
      (cmpfunc)SwigPyObject_compare,        /* tp_compare */
#endif
      (reprfunc)SwigPyObject_repr,          /* tp_repr */
      &SwigPyObject_as_number,              /* tp_as_number */
      0,                                    /* tp_as_sequence */
      0,                                    /* tp_as_mapping */
      (hashfunc)0,                          /* tp_hash */
      (ternaryfunc)0,                       /* tp_call */
      0,                                    /* tp_str */
      PyObject_GenericGetAttr,              /* tp_getattro */
      0,                                    /* tp_setattro */
      0,                                    /* tp_as_buffer */
      Py_TPFLAGS_DEFAULT,                   /* tp_flags */
      swigobject_doc,                       /* tp_doc */
      0,                                    /* tp_traverse */
      0,                                    /* tp_clear */
      (richcmpfunc)SwigPyObject_richcompare,/* tp_richcompare */
      0,                                    /* tp_weaklistoffset */
      0,                                    /* tp_iter */
      0,                                    /* tp_iternext */
      swigobject_methods,                   /* tp_methods */
      0,                                    /* tp_members */
      0,                                    /* tp_getset */
      0,                                    /* tp_base */
      0,                                    /* tp_dict */
      0,                                    /* tp_descr_get */
      0,                                    /* tp_descr_set */
      0,                                    /* tp_dictoffset */
      0,                                    /* tp_init */
      0,                                    /* tp_alloc */
      0,                                    /* tp_new */
      0,                                    /* tp_free */
      0,                                    /* tp_is_gc */
      0,                                    /* tp_bases */
      0,                                    /* tp_mro */
      0,                                    /* tp_cache */
      0,                                    /* tp_subclasses */
      0,                                    /* tp_weaklist */
      0,                                    /* tp_del */
      0,                                    /* tp_version_tag */
#if PY_VERSION_HEX >= 0x03040000
      0,                                    /* tp_finalize */
#endif
#if PY_VERSION_HEX >= 0x03080000
      0,                                    /* tp_vectorcall */
#endif
#if (PY_VERSION_HEX >= 0x03080000) && (PY_VERSION_HEX < 0x03090000)
      0,                                    /* tp_print */
#endif
#if PY_VERSION_HEX >= 0x030C0000
      0,                                    /* tp_watched */
#endif
#ifdef COUNT_ALLOCS
      0,                                    /* tp_allocs */
      0,                                    /* tp_frees */
      0,                                    /* tp_maxalloc */
      0,                                    /* tp_prev */
      0                                     /* tp_next */
#endif
    };
    swigpyobject_type = tmp;
    type_init = 1;
    if (PyType_Ready(&swigpyobject_type) != 0)
      return NULL;
  }
  return &swigpyobject_type;
#else
  PyType_Slot slots[] = {
    { Py_tp_dealloc, (void *)SwigPyObject_dealloc },
    { Py_tp_repr, (void *)SwigPyObject_repr },
    { Py_tp_getattro, (void *)PyObject_GenericGetAttr },
    { Py_tp_doc, (void *)swigobject_doc },
    { Py_tp_richcompare, (void *)SwigPyObject_richcompare },
    { Py_tp_methods, (void *)swigobject_methods },
    { Py_nb_int, (void *)SwigPyObject_long },
    { 0, NULL }
  };
  PyType_Spec spec = {
    "SwigPyObject",
    sizeof(SwigPyObject),
    0,
    Py_TPFLAGS_DEFAULT,
    slots
  };
  return (PyTypeObject *)PyType_FromSpec(&spec);
#endif
}

SWIGRUNTIME PyObject *
SwigPyObject_New(void *ptr, swig_type_info *ty, int own)
{
  SwigPyObject *sobj = PyObject_New(SwigPyObject, SwigPyObject_type());
  if (sobj) {
    sobj->ptr  = ptr;
    sobj->ty   = ty;
    sobj->own  = own;
    sobj->next = 0;
#ifdef SWIGPYTHON_BUILTIN
    sobj->dict = 0;
#endif
    if (own == SWIG_POINTER_OWN) {
      /* Obtain a reference to the Python capsule wrapping the module information, so that the
       * module information is correctly destroyed after all SWIG python objects have been freed
       * by the GC (and corresponding destructors invoked) */
      SWIG_Py_XINCREF(Swig_Capsule_global);
    }
  }
  return (PyObject *)sobj;
}

/* -----------------------------------------------------------------------------
 * Implements a simple Swig Packed type, and use it instead of string
 * ----------------------------------------------------------------------------- */

typedef struct {
  PyObject_HEAD
  void *pack;
  swig_type_info *ty;
  size_t size;
} SwigPyPacked;

SWIGRUNTIME PyObject *
SwigPyPacked_repr(SwigPyPacked *v)
{
  char result[SWIG_BUFFER_SIZE];
  if (SWIG_PackDataName(result, v->pack, v->size, 0, sizeof(result))) {
    return SWIG_Python_str_FromFormat("<Swig Packed at %s%s>", result, v->ty->name);
  } else {
    return SWIG_Python_str_FromFormat("<Swig Packed %s>", v->ty->name);
  }  
}

SWIGRUNTIME PyObject *
SwigPyPacked_str(SwigPyPacked *v)
{
  char result[SWIG_BUFFER_SIZE];
  if (SWIG_PackDataName(result, v->pack, v->size, 0, sizeof(result))){
    return SWIG_Python_str_FromFormat("%s%s", result, v->ty->name);
  } else {
    return SWIG_Python_str_FromChar(v->ty->name);
  }  
}

SWIGRUNTIME int
SwigPyPacked_compare(SwigPyPacked *v, SwigPyPacked *w)
{
  size_t i = v->size;
  size_t j = w->size;
  int s = (i < j) ? -1 : ((i > j) ? 1 : 0);
  return s ? s : strncmp((const char *)v->pack, (const char *)w->pack, 2*v->size);
}

SWIGRUNTIME PyTypeObject* SwigPyPacked_TypeOnce(void);

SWIGRUNTIME PyTypeObject*
SwigPyPacked_type(void) {
  static PyTypeObject *SWIG_STATIC_POINTER(type) = SwigPyPacked_TypeOnce();
  return type;
}

SWIGRUNTIMEINLINE int
SwigPyPacked_Check(PyObject *op) {
#ifdef Py_LIMITED_API
  int cmp;
  PyObject *tp_name;
#endif
  PyTypeObject* op_type = Py_TYPE(op);
  if (op_type == SwigPyPacked_TypeOnce())
    return 1;
#ifdef Py_LIMITED_API
  tp_name = PyObject_GetAttrString((PyObject *)op_type, "__name__");
  if (!tp_name)
    return 0;
  cmp = PyUnicode_CompareWithASCIIString(tp_name, "SwigPyPacked");
  SWIG_Py_DECREF(tp_name);
  return cmp == 0;
#else
  return (strcmp(op_type->tp_name, "SwigPyPacked") == 0);
#endif
}

SWIGRUNTIME void
SwigPyPacked_dealloc(PyObject *v)
{
  if (SwigPyPacked_Check(v)) {
    SwigPyPacked *sobj = (SwigPyPacked *) v;
    free(sobj->pack);
  }
  PyObject_Free(v);
}

SWIGRUNTIME PyTypeObject*
SwigPyPacked_TypeOnce(void) {
  static char swigpacked_doc[] = "Swig object carries a C/C++ instance pointer";
#ifndef Py_LIMITED_API
  static PyTypeObject swigpypacked_type;
  static int type_init = 0;
  if (!type_init) {
    const PyTypeObject tmp = {
#if PY_VERSION_HEX>=0x03000000
      PyVarObject_HEAD_INIT(NULL, 0)
#else
      PyObject_HEAD_INIT(NULL)
      0,                                    /* ob_size */
#endif
      "SwigPyPacked",                       /* tp_name */
      sizeof(SwigPyPacked),                 /* tp_basicsize */
      0,                                    /* tp_itemsize */
      (destructor)SwigPyPacked_dealloc,     /* tp_dealloc */
#if PY_VERSION_HEX < 0x030800b4
      (printfunc)0,                         /*tp_print*/
#else
      (Py_ssize_t)0,                        /*tp_vectorcall_offset*/
#endif
      (getattrfunc)0,                       /* tp_getattr */
      (setattrfunc)0,                       /* tp_setattr */
#if PY_VERSION_HEX>=0x03000000
      0, /* tp_reserved in 3.0.1 */
#else
      (cmpfunc)SwigPyPacked_compare,        /* tp_compare */
#endif
      (reprfunc)SwigPyPacked_repr,          /* tp_repr */
      0,                                    /* tp_as_number */
      0,                                    /* tp_as_sequence */
      0,                                    /* tp_as_mapping */
      (hashfunc)0,                          /* tp_hash */
      (ternaryfunc)0,                       /* tp_call */
      (reprfunc)SwigPyPacked_str,           /* tp_str */
      PyObject_GenericGetAttr,              /* tp_getattro */
      0,                                    /* tp_setattro */
      0,                                    /* tp_as_buffer */
      Py_TPFLAGS_DEFAULT,                   /* tp_flags */
      swigpacked_doc,                       /* tp_doc */
      0,                                    /* tp_traverse */
      0,                                    /* tp_clear */
      0,                                    /* tp_richcompare */
      0,                                    /* tp_weaklistoffset */
      0,                                    /* tp_iter */
      0,                                    /* tp_iternext */
      0,                                    /* tp_methods */
      0,                                    /* tp_members */
      0,                                    /* tp_getset */
      0,                                    /* tp_base */
      0,                                    /* tp_dict */
      0,                                    /* tp_descr_get */
      0,                                    /* tp_descr_set */
      0,                                    /* tp_dictoffset */
      0,                                    /* tp_init */
      0,                                    /* tp_alloc */
      0,                                    /* tp_new */
      0,                                    /* tp_free */
      0,                                    /* tp_is_gc */
      0,                                    /* tp_bases */
      0,                                    /* tp_mro */
      0,                                    /* tp_cache */
      0,                                    /* tp_subclasses */
      0,                                    /* tp_weaklist */
      0,                                    /* tp_del */
      0,                                    /* tp_version_tag */
#if PY_VERSION_HEX >= 0x03040000
      0,                                    /* tp_finalize */
#endif
#if PY_VERSION_HEX >= 0x03080000
      0,                                    /* tp_vectorcall */
#endif
#if (PY_VERSION_HEX >= 0x03080000) && (PY_VERSION_HEX < 0x03090000)
      0,                                    /* tp_print */
#endif
#if PY_VERSION_HEX >= 0x030C0000
      0,                                    /* tp_watched */
#endif
#ifdef COUNT_ALLOCS
      0,                                    /* tp_allocs */
      0,                                    /* tp_frees */
      0,                                    /* tp_maxalloc */
      0,                                    /* tp_prev */
      0                                     /* tp_next */
#endif
    };
    swigpypacked_type = tmp;
    type_init = 1;
    if (PyType_Ready(&swigpypacked_type) != 0)
      return NULL;
  }
  return &swigpypacked_type;
#else
  PyType_Slot slots[] = {
    { Py_tp_dealloc, (void *)SwigPyPacked_dealloc },
    { Py_tp_repr, (void *)SwigPyPacked_repr },
    { Py_tp_str, (void *)SwigPyPacked_str },
    { Py_tp_getattro, (void *)PyObject_GenericGetAttr },
    { Py_tp_doc, (void *)swigpacked_doc },
    { 0, NULL }
  };
  PyType_Spec spec = {
    "SwigPyPacked",
    sizeof(SwigPyPacked),
    0,
    Py_TPFLAGS_DEFAULT,
    slots
  };
  return (PyTypeObject *)PyType_FromSpec(&spec);
#endif
}

SWIGRUNTIME PyObject *
SwigPyPacked_New(void *ptr, size_t size, swig_type_info *ty)
{
  SwigPyPacked *sobj = PyObject_New(SwigPyPacked, SwigPyPacked_type());
  if (sobj) {
    void *pack = malloc(size);
    if (pack) {
      memcpy(pack, ptr, size);
      sobj->pack = pack;
      sobj->ty   = ty;
      sobj->size = size;
    } else {
      PyObject_Free((PyObject *)sobj);
      sobj = 0;
    }
  }
  return (PyObject *) sobj;
}

SWIGRUNTIME swig_type_info *
SwigPyPacked_UnpackData(PyObject *obj, void *ptr, size_t size)
{
  if (SwigPyPacked_Check(obj)) {
    SwigPyPacked *sobj = (SwigPyPacked *)obj;
    if (sobj->size != size) return 0;
    memcpy(ptr, sobj->pack, size);
    return sobj->ty;
  } else {
    return 0;
  }
}

/* -----------------------------------------------------------------------------
 * pointers/data manipulation
 * ----------------------------------------------------------------------------- */

static PyObject *Swig_This_global = NULL;

SWIGRUNTIME PyObject *
SWIG_This(void)
{
  if (Swig_This_global == NULL)
    Swig_This_global = SWIG_Python_str_FromChar("this");
  return Swig_This_global;
}

/* #define SWIG_PYTHON_SLOW_GETSET_THIS */

/* TODO: I don't know how to implement the fast getset in Python 3 right now */
#if PY_VERSION_HEX>=0x03000000
#define SWIG_PYTHON_SLOW_GETSET_THIS 
#endif

SWIGRUNTIME SwigPyObject *
SWIG_Python_GetSwigThis(PyObject *pyobj) 
{
  PyObject *obj;

  if (SwigPyObject_Check(pyobj))
    return (SwigPyObject *) pyobj;

#ifdef SWIGPYTHON_BUILTIN
  (void)obj;
# ifdef PyWeakref_CheckProxy
  if (PyWeakref_CheckProxy(pyobj)) {
    pyobj = PyWeakref_GET_OBJECT(pyobj);
    if (pyobj && SwigPyObject_Check(pyobj))
      return (SwigPyObject*) pyobj;
  }
# endif
  return NULL;
#else

  obj = 0;

#if !defined(SWIG_PYTHON_SLOW_GETSET_THIS)
  if (PyInstance_Check(pyobj)) {
    obj = _PyInstance_Lookup(pyobj, SWIG_This());      
  } else {
    PyObject **dictptr = _PyObject_GetDictPtr(pyobj);
    if (dictptr != NULL) {
      PyObject *dict = *dictptr;
      obj = dict ? PyDict_GetItem(dict, SWIG_This()) : 0;
    } else {
#ifdef PyWeakref_CheckProxy
      if (PyWeakref_CheckProxy(pyobj)) {
	PyObject *wobj = PyWeakref_GET_OBJECT(pyobj);
	return wobj ? SWIG_Python_GetSwigThis(wobj) : 0;
      }
#endif
      obj = PyObject_GetAttr(pyobj,SWIG_This());
      if (obj) {
	SWIG_Py_DECREF(obj);
      } else {
	if (PyErr_Occurred()) PyErr_Clear();
	return 0;
      }
    }
  }
#else
  obj = PyObject_GetAttr(pyobj,SWIG_This());
  if (obj) {
    SWIG_Py_DECREF(obj);
  } else {
    if (PyErr_Occurred()) PyErr_Clear();
    return 0;
  }
#endif
  if (obj && !SwigPyObject_Check(obj)) {
    /* a PyObject is called 'this', try to get the 'real this'
       SwigPyObject from it */ 
    return SWIG_Python_GetSwigThis(obj);
  }
  return (SwigPyObject *)obj;
#endif
}

/* Acquire a pointer value */

SWIGRUNTIME int
SWIG_Python_AcquirePtr(PyObject *obj, int own) {
  if (own == SWIG_POINTER_OWN) {
    SwigPyObject *sobj = SWIG_Python_GetSwigThis(obj);
    if (sobj) {
      int oldown = sobj->own;
      sobj->own = own;
      return oldown;
    }
  }
  return 0;
}

/* Convert a pointer value */

SWIGRUNTIME int
SWIG_Python_ConvertPtrAndOwn(PyObject *obj, void **ptr, swig_type_info *ty, int flags, int *own) {
  int res;
  SwigPyObject *sobj;
  int implicit_conv = (flags & SWIG_POINTER_IMPLICIT_CONV) != 0;

  if (!obj)
    return SWIG_ERROR;
  if (obj == Py_None && !implicit_conv) {
    if (ptr)
      *ptr = 0;
    return (flags & SWIG_POINTER_NO_NULL) ? SWIG_NullReferenceError : SWIG_OK;
  }

  res = SWIG_ERROR;

  sobj = SWIG_Python_GetSwigThis(obj);
  if (own)
    *own = 0;
  while (sobj) {
    void *vptr = sobj->ptr;
    if (ty) {
      swig_type_info *to = sobj->ty;
      if (to == ty) {
        /* no type cast needed */
        if (ptr) *ptr = vptr;
        break;
      } else {
        swig_cast_info *tc = SWIG_TypeCheck(to->name,ty);
        if (!tc) {
          sobj = (SwigPyObject *)sobj->next;
        } else {
          if (ptr) {
            int newmemory = 0;
            *ptr = SWIG_TypeCast(tc,vptr,&newmemory);
            if (newmemory == SWIG_CAST_NEW_MEMORY) {
              assert(own); /* badly formed typemap which will lead to a memory leak - it must set and use own to delete *ptr */
              if (own)
                *own = *own | SWIG_CAST_NEW_MEMORY;
            }
          }
          break;
        }
      }
    } else {
      if (ptr) *ptr = vptr;
      break;
    }
  }
  if (sobj) {
    if (((flags & SWIG_POINTER_RELEASE) == SWIG_POINTER_RELEASE) && !sobj->own) {
      res = SWIG_ERROR_RELEASE_NOT_OWNED;
    } else {
      if (own)
        *own = *own | sobj->own;
      if (flags & SWIG_POINTER_DISOWN) {
        sobj->own = 0;
      }
      if (flags & SWIG_POINTER_CLEAR) {
        sobj->ptr = 0;
      }
      res = SWIG_OK;
    }
  } else {
    if (implicit_conv) {
      SwigPyClientData *data = ty ? (SwigPyClientData *) ty->clientdata : 0;
      if (data && !data->implicitconv) {
        PyObject *klass = data->klass;
        if (klass) {
          PyObject *impconv;
          data->implicitconv = 1; /* avoid recursion and call 'explicit' constructors*/
          impconv = SWIG_Python_CallFunctor(klass, obj);
          data->implicitconv = 0;
          if (PyErr_Occurred()) {
            PyErr_Clear();
            impconv = 0;
          }
          if (impconv) {
            SwigPyObject *iobj = SWIG_Python_GetSwigThis(impconv);
            if (iobj) {
              void *vptr;
              res = SWIG_Python_ConvertPtrAndOwn((PyObject*)iobj, &vptr, ty, 0, 0);
              if (SWIG_IsOK(res)) {
                if (ptr) {
                  *ptr = vptr;
                  /* transfer the ownership to 'ptr' */
                  iobj->own = 0;
                  res = SWIG_AddCast(res);
                  res = SWIG_AddNewMask(res);
                } else {
                  res = SWIG_AddCast(res);		    
                }
              }
            }
            SWIG_Py_DECREF(impconv);
          }
        }
      }
      if (!SWIG_IsOK(res) && obj == Py_None) {
        if (ptr)
          *ptr = 0;
        if (PyErr_Occurred())
          PyErr_Clear();
        res = SWIG_OK;
      }
    }
  }
  return res;
}

/* Convert a function ptr value */

SWIGRUNTIME int
SWIG_Python_ConvertFunctionPtr(PyObject *obj, void **ptr, swig_type_info *ty) {
  if (!PyCFunction_Check(obj)) {
    return SWIG_ConvertPtr(obj, ptr, ty, 0);
  } else {
    void *vptr = 0;
    swig_cast_info *tc;

    /* here we get the method pointer for callbacks */
#ifndef Py_LIMITED_API
    const char *doc = (((PyCFunctionObject *)obj) -> m_ml -> ml_doc);
#else
    PyObject* pystr_doc = PyObject_GetAttrString(obj, "__doc__");
    PyObject *bytes = NULL;
    const char *doc = pystr_doc ? SWIG_PyUnicode_AsUTF8AndSize(pystr_doc, NULL, &bytes) : 0;
#endif
    const char *desc = doc ? strstr(doc, "swig_ptr: ") : 0;
    if (desc)
      desc = ty ? SWIG_UnpackVoidPtr(desc + 10, &vptr, ty->name) : 0;
#ifdef Py_LIMITED_API
    SWIG_Py_XDECREF(bytes);
    SWIG_Py_XDECREF(pystr_doc);
#endif
    if (!desc)
      return SWIG_ERROR;
    tc = SWIG_TypeCheck(desc,ty);
    if (tc) {
      int newmemory = 0;
      *ptr = SWIG_TypeCast(tc,vptr,&newmemory);
      assert(!newmemory); /* newmemory handling not yet implemented */
    } else {
      return SWIG_ERROR;
    }
    return SWIG_OK;
  }
}

/* Convert a packed pointer value */

SWIGRUNTIME int
SWIG_Python_ConvertPacked(PyObject *obj, void *ptr, size_t sz, swig_type_info *ty) {
  swig_type_info *to = SwigPyPacked_UnpackData(obj, ptr, sz);
  if (!to) return SWIG_ERROR;
  if (ty) {
    if (to != ty) {
      /* check type cast? */
      swig_cast_info *tc = SWIG_TypeCheck(to->name,ty);
      if (!tc) return SWIG_ERROR;
    }
  }
  return SWIG_OK;
}  

/* -----------------------------------------------------------------------------
 * Create a new pointer object
 * ----------------------------------------------------------------------------- */

/*
  Create a new instance object, without calling __init__, and set the
  'this' attribute.
*/

SWIGRUNTIME PyObject* 
SWIG_Python_NewShadowInstance(SwigPyClientData *data, PyObject *swig_this)
{
  PyObject *inst = 0;
  PyObject *newraw = data->newraw;
  if (newraw) {
    inst = PyObject_Call(newraw, data->newargs, NULL);
    if (inst) {
#if !defined(SWIG_PYTHON_SLOW_GETSET_THIS)
      PyObject **dictptr = _PyObject_GetDictPtr(inst);
      if (dictptr != NULL) {
        PyObject *dict = *dictptr;
        if (dict == NULL) {
          dict = PyDict_New();
          *dictptr = dict;
        }
        if (dict) {
          PyDict_SetItem(dict, SWIG_This(), swig_this);
        } else{
          SWIG_Py_DECREF(inst);
          inst = 0;
        }
      }
#else
      if (PyObject_SetAttr(inst, SWIG_This(), swig_this) == -1) {
        SWIG_Py_DECREF(inst);
        inst = 0;
      }
#endif
    }
  } else {
#if PY_VERSION_HEX >= 0x03000000
    PyObject *empty_args = PyTuple_New(0);
    if (empty_args) {
      PyObject *empty_kwargs = PyDict_New();
      if (empty_kwargs) {
#ifndef Py_LIMITED_API
        newfunc newfn = ((PyTypeObject *)data->newargs)->tp_new;
#else
        newfunc newfn = (newfunc)PyType_GetSlot((PyTypeObject *)data->newargs, Py_tp_new);
#endif
        inst = newfn((PyTypeObject *)data->newargs, empty_args, empty_kwargs);
        SWIG_Py_DECREF(empty_kwargs);
        if (inst) {
          if (PyObject_SetAttr(inst, SWIG_This(), swig_this) == -1) {
            SWIG_Py_DECREF(inst);
            inst = 0;
          } else {
            PyType_Modified(Py_TYPE(inst));
          }
        }
      }
      SWIG_Py_DECREF(empty_args);
    }
#else
    PyObject *dict = PyDict_New();
    if (dict) {
      PyDict_SetItem(dict, SWIG_This(), swig_this);
      inst = PyInstance_NewRaw(data->newargs, dict);
      SWIG_Py_DECREF(dict);
    }
#endif
  }
  return inst;
}

SWIGRUNTIME int
SWIG_Python_SetSwigThis(PyObject *inst, PyObject *swig_this)
{
#if !defined(SWIG_PYTHON_SLOW_GETSET_THIS)
  PyObject **dictptr = _PyObject_GetDictPtr(inst);
  if (dictptr != NULL) {
    PyObject *dict = *dictptr;
    if (dict == NULL) {
      dict = PyDict_New();
      *dictptr = dict;
    }
    if (dict) {
      return PyDict_SetItem(dict, SWIG_This(), swig_this);
    } else{
      return -1;
    }
  }
#endif
  return PyObject_SetAttr(inst, SWIG_This(), swig_this);
} 


SWIGINTERN PyObject *
SWIG_Python_InitShadowInstance(PyObject *args) {
  PyObject *obj[2];
  if (!SWIG_Python_UnpackTuple(args, "swiginit", 2, 2, obj)) {
    return NULL;
  } else {
    SwigPyObject *sthis = SWIG_Python_GetSwigThis(obj[0]);
    if (sthis) {
      SWIG_Py_DECREF(SwigPyObject_append((PyObject*) sthis, obj[1]));
    } else {
      if (SWIG_Python_SetSwigThis(obj[0], obj[1]) != 0)
        return NULL;
    }
    return SWIG_Py_Void();
  }
}

/* Create a new pointer object */

SWIGRUNTIME PyObject *
SWIG_Python_NewPointerObj(PyObject *self, void *ptr, swig_type_info *type, int flags) {
  SwigPyClientData *clientdata;
  PyObject * robj;
  int own;

  if (!ptr)
    return SWIG_Py_Void();

  clientdata = type ? (SwigPyClientData *)(type->clientdata) : 0;
  own = (flags & SWIG_POINTER_OWN) ? SWIG_POINTER_OWN : 0;
  if (clientdata && clientdata->pytype) {
    SwigPyObject *newobj;
    if (flags & SWIG_BUILTIN_TP_INIT) {
      newobj = (SwigPyObject*) self;
      if (newobj->ptr) {
#ifndef Py_LIMITED_API
        allocfunc alloc = clientdata->pytype->tp_alloc;
#else
        allocfunc alloc = (allocfunc)PyType_GetSlot(clientdata->pytype, Py_tp_alloc);
#endif
        PyObject *next_self = alloc(clientdata->pytype, 0);
        while (newobj->next)
	  newobj = (SwigPyObject *) newobj->next;
        newobj->next = next_self;
        newobj = (SwigPyObject *)next_self;
#ifdef SWIGPYTHON_BUILTIN
        newobj->dict = 0;
#endif
      }
    } else {
      newobj = PyObject_New(SwigPyObject, clientdata->pytype);
#ifdef SWIGPYTHON_BUILTIN
      if (newobj) {
        newobj->dict = 0;
      }
#endif
    }
    if (newobj) {
      newobj->ptr = ptr;
      newobj->ty = type;
      newobj->own = own;
      newobj->next = 0;
      return (PyObject*) newobj;
    }
    return SWIG_Py_Void();
  }

  assert(!(flags & SWIG_BUILTIN_TP_INIT));

  robj = SwigPyObject_New(ptr, type, own);
  if (robj && clientdata && !(flags & SWIG_POINTER_NOSHADOW)) {
    PyObject *inst = SWIG_Python_NewShadowInstance(clientdata, robj);
    SWIG_Py_DECREF(robj);
    robj = inst;
  }
  return robj;
}

/* Create a new packed object */

SWIGRUNTIMEINLINE PyObject *
SWIG_Python_NewPackedObj(void *ptr, size_t sz, swig_type_info *type) {
  return ptr ? SwigPyPacked_New((void *) ptr, sz, type) : SWIG_Py_Void();
}

/* -----------------------------------------------------------------------------*
 *  Get type list 
 * -----------------------------------------------------------------------------*/

#ifdef SWIG_LINK_RUNTIME
void *SWIG_ReturnGlobalTypeList(void *);
#endif

static PyObject *Swig_TypeCache_global = NULL;

/* The python cached type query */
SWIGRUNTIME PyObject *
SWIG_Python_TypeCache(void) {
  if (Swig_TypeCache_global == NULL) {
    Swig_TypeCache_global = PyDict_New();
  }
  return Swig_TypeCache_global;
}

SWIGRUNTIME swig_module_info *
SWIG_Python_GetModule(void *SWIGUNUSEDPARM(clientdata)) {
#ifdef SWIG_LINK_RUNTIME
  static void *type_pointer = (void *)0;
  /* first check if module already created */
  if (!type_pointer) {
    type_pointer = SWIG_ReturnGlobalTypeList((void *)0);
  }
#else
  void *type_pointer = PyCapsule_Import(SWIGPY_CAPSULE_NAME, 0);
  if (PyErr_Occurred()) {
    PyErr_Clear();
    type_pointer = (void *)0;
  }
#endif
  return (swig_module_info *) type_pointer;
}


static int interpreter_counter = 0; /* how many (sub-)interpreters are using swig_module's types */

SWIGRUNTIME void
SWIG_Python_DestroyModule(PyObject *obj)
{
  swig_module_info *swig_module = (swig_module_info *) PyCapsule_GetPointer(obj, SWIGPY_CAPSULE_NAME);
  swig_type_info **types = swig_module->types;
  size_t i;
  if (--interpreter_counter != 0) /* another sub-interpreter may still be using the swig_module's types */
    return;
  for (i =0; i < swig_module->size; ++i) {
    swig_type_info *ty = types[i];
    if (ty->owndata) {
      SwigPyClientData *data = (SwigPyClientData *) ty->clientdata;
      ty->clientdata = 0;
      if (data) SwigPyClientData_Del(data);
    }
  }
  SWIG_Py_DECREF(SWIG_This());
  Swig_This_global = NULL;
  SWIG_Py_DECREF(SWIG_globals());
  Swig_Globals_global = NULL;
  SWIG_Py_DECREF(SWIG_Python_TypeCache());
  Swig_TypeCache_global = NULL;
  Swig_Capsule_global = NULL;
}

SWIGRUNTIME void
SWIG_Python_SetModule(swig_module_info *swig_module) {
#if PY_VERSION_HEX >= 0x03000000
 /* Add a dummy module object into sys.modules */
  PyObject *module = PyImport_AddModule("swig_runtime_data" SWIG_RUNTIME_VERSION);
#else
  static PyMethodDef swig_empty_runtime_method_table[] = { {NULL, NULL, 0, NULL} }; /* Sentinel */
  PyObject *module = Py_InitModule("swig_runtime_data" SWIG_RUNTIME_VERSION, swig_empty_runtime_method_table);
#endif
  PyObject *pointer = PyCapsule_New((void *) swig_module, SWIGPY_CAPSULE_NAME, SWIG_Python_DestroyModule);
  if (pointer && module) {
    if (PyModule_AddObject(module, SWIGPY_CAPSULE_ATTR_NAME, pointer) == 0) {
      ++interpreter_counter;
      Swig_Capsule_global = pointer;
    } else {
      SWIG_Py_DECREF(pointer);
    }
  } else {
    SWIG_Py_XDECREF(pointer);
  }
}

SWIGRUNTIME swig_type_info *
SWIG_Python_TypeQuery(const char *type)
{
  PyObject *cache = SWIG_Python_TypeCache();
  PyObject *key = SWIG_Python_str_FromChar(type); 
  PyObject *obj = PyDict_GetItem(cache, key);
  swig_type_info *descriptor;
  if (obj) {
    descriptor = (swig_type_info *) PyCapsule_GetPointer(obj, NULL);
  } else {
    swig_module_info *swig_module = SWIG_GetModule(0);
    descriptor = SWIG_TypeQueryModule(swig_module, swig_module, type);
    if (descriptor) {
      obj = PyCapsule_New((void*) descriptor, NULL, NULL);
      if (obj) {
        PyDict_SetItem(cache, key, obj);
        SWIG_Py_DECREF(obj);
      }
    }
  }
  SWIG_Py_DECREF(key);
  return descriptor;
}

/* 
   For backward compatibility only
*/
#define SWIG_POINTER_EXCEPTION  0
#define SWIG_arg_fail(arg)      SWIG_Python_ArgFail(arg)
#define SWIG_MustGetPtr(p, type, argnum, flags)  SWIG_Python_MustGetPtr(p, type, argnum, flags)

SWIGRUNTIME int
SWIG_Python_AddErrMesg(const char* mesg, int infront)
{  
  if (PyErr_Occurred()) {
    PyObject *type = 0;
    PyObject *value = 0;
    PyObject *traceback = 0;
    PyErr_Fetch(&type, &value, &traceback);
    if (value) {
      PyObject *old_str = PyObject_Str(value);
      PyObject *bytes = NULL;
      const char *tmp = SWIG_PyUnicode_AsUTF8AndSize(old_str, NULL, &bytes);
      const char *errmesg = tmp ? tmp : "Invalid error message";
      SWIG_Py_XINCREF(type);
      PyErr_Clear();
      if (infront) {
	PyErr_Format(type, "%s %s", mesg, errmesg);
      } else {
	PyErr_Format(type, "%s %s", errmesg, mesg);
      }
      SWIG_Py_XDECREF(bytes);
      SWIG_Py_DECREF(old_str);
    }
    return 1;
  } else {
    return 0;
  }
}
  
SWIGRUNTIME int
SWIG_Python_ArgFail(int argnum)
{
  if (PyErr_Occurred()) {
    /* add information about failing argument */
    char mesg[256];
    PyOS_snprintf(mesg, sizeof(mesg), "argument number %d:", argnum);
    return SWIG_Python_AddErrMesg(mesg, 1);
  } else {
    return 0;
  }
}

SWIGRUNTIMEINLINE const char *
SwigPyObject_GetDesc(PyObject *self)
{
  SwigPyObject *v = (SwigPyObject *)self;
  swig_type_info *ty = v ? v->ty : 0;
  return ty ? ty->str : "";
}

SWIGRUNTIME void
SWIG_Python_TypeError(const char *type, PyObject *obj)
{
  (void) obj;
  if (type) {
#if defined(SWIG_COBJECT_TYPES)
    if (obj && SwigPyObject_Check(obj)) {
      const char *otype = (const char *) SwigPyObject_GetDesc(obj);
      if (otype) {
	PyErr_Format(PyExc_TypeError, "a '%s' is expected, 'SwigPyObject(%s)' is received",
		     type, otype);
	return;
      }
    } else 
#endif      
    {
#ifndef Py_LIMITED_API
      /* tp_name is not accessible */
      const char *otype = (obj ? obj->ob_type->tp_name : 0); 
      if (otype) {
	PyObject *str = PyObject_Str(obj);
	PyObject *bytes = NULL;
	const char *cstr = str ? SWIG_PyUnicode_AsUTF8AndSize(str, NULL, &bytes) : 0;
	if (cstr) {
	  PyErr_Format(PyExc_TypeError, "a '%s' is expected, '%s(%s)' is received",
		       type, otype, cstr);
	} else {
	  PyErr_Format(PyExc_TypeError, "a '%s' is expected, '%s' is received",
		       type, otype);
	}
	SWIG_Py_XDECREF(bytes);
	SWIG_Py_XDECREF(str);
	return;
      }
#endif
    }   
    PyErr_Format(PyExc_TypeError, "a '%s' is expected", type);
  } else {
    PyErr_Format(PyExc_TypeError, "unexpected type is received");
  }
}


/* Convert a pointer value, signal an exception on a type mismatch */
SWIGRUNTIME void *
SWIG_Python_MustGetPtr(PyObject *obj, swig_type_info *ty, int SWIGUNUSEDPARM(argnum), int flags) {
  void *result;
  if (SWIG_Python_ConvertPtr(obj, &result, ty, flags) == -1) {
    PyErr_Clear();
  }
  return result;
}

#ifdef SWIGPYTHON_BUILTIN
SWIGRUNTIME int
SWIG_Python_NonDynamicSetAttr(PyObject *obj, PyObject *name, PyObject *value) {
  PyTypeObject *tp = obj->ob_type;
  PyObject *descr;
  PyObject *encoded_name;
  descrsetfunc f;
  int res = -1;

# ifdef Py_USING_UNICODE
  if (PyString_Check(name)) {
    name = PyUnicode_Decode(PyString_AsString(name), PyString_Size(name), NULL, NULL);
    if (!name)
      return -1;
  } else if (!PyUnicode_Check(name))
# else
  if (!PyString_Check(name))
# endif
  {
    PyErr_Format(PyExc_TypeError, "attribute name must be string, not '%.200s'", name->ob_type->tp_name);
    return -1;
  } else {
    SWIG_Py_INCREF(name);
  }

  if (!tp->tp_dict) {
    if (PyType_Ready(tp) != 0)
      goto done;
  }

  descr = _PyType_Lookup(tp, name);
  f = NULL;
  if (descr != NULL)
    f = descr->ob_type->tp_descr_set;
  if (!f) {
    if (PyString_Check(name)) {
      encoded_name = name;
      SWIG_Py_INCREF(name);
    } else {
      encoded_name = PyUnicode_AsUTF8String(name);
      if (!encoded_name)
        goto done;
    }
    PyErr_Format(PyExc_AttributeError, "'%.100s' object has no attribute '%.200s'", tp->tp_name, PyString_AsString(encoded_name));
    SWIG_Py_DECREF(encoded_name);
  } else {
    res = f(descr, obj, value);
  }
  
  done:
  SWIG_Py_DECREF(name);
  return res;
}
#endif


#ifdef __cplusplus
}
#endif



#define SWIG_exception_fail(code, msg) do { SWIG_Error(code, msg); SWIG_fail; } while(0) 

#define SWIG_contract_assert(expr, msg) do { if (!(expr)) { SWIG_Error(SWIG_RuntimeError, msg); SWIG_fail; } } while (0) 



/* -------- TYPES TABLE (BEGIN) -------- */

#define SWIGTYPE_p_FCOMPLEX swig_types[0]
#define SWIGTYPE_p_FILE swig_types[1]
#define SWIGTYPE_p_FOURIERPROPS swig_types[2]
#define SWIGTYPE_p_INFODATA swig_types[3]
#define SWIGTYPE_p_PSRPARAMS swig_types[4]
#define SWIGTYPE_p_RDERIVS swig_types[5]
#define SWIGTYPE_p_binaryprops swig_types[6]
#define SWIGTYPE_p_char swig_types[7]
#define SWIGTYPE_p_double swig_types[8]
#define SWIGTYPE_p_float swig_types[9]
#define SWIGTYPE_p_foldstats swig_types[10]
#define SWIGTYPE_p_int swig_types[11]
#define SWIGTYPE_p_long swig_types[12]
#define SWIGTYPE_p_orbitparams swig_types[13]
#define SWIGTYPE_p_p_FCOMPLEX swig_types[14]
#define SWIGTYPE_p_p_double swig_types[15]
#define SWIGTYPE_p_p_float swig_types[16]
#define SWIGTYPE_p_presto_checkaliased swig_types[17]
#define SWIGTYPE_p_presto_datainf swig_types[18]
#define SWIGTYPE_p_presto_ffts swig_types[19]
#define SWIGTYPE_p_presto_interp_acc swig_types[20]
#define SWIGTYPE_p_presto_interptype swig_types[21]
#define SWIGTYPE_p_presto_optype swig_types[22]
#define SWIGTYPE_p_rzwerrs swig_types[23]
static swig_type_info *swig_types[25];
static swig_module_info swig_module = {swig_types, 24, 0, 0, 0, 0};
#define SWIG_TypeQuery(name) SWIG_TypeQueryModule(&swig_module, &swig_module, name)
#define SWIG_MangledTypeQuery(name) SWIG_MangledTypeQueryModule(&swig_module, &swig_module, name)

/* -------- TYPES TABLE (END) -------- */

#ifdef SWIG_TypeQuery
# undef SWIG_TypeQuery
#endif
#define SWIG_TypeQuery SWIG_Python_TypeQuery

/*-----------------------------------------------
              @(target):= _presto.so
  ------------------------------------------------*/
#if PY_VERSION_HEX >= 0x03000000
#  define SWIG_init    PyInit__presto

#else
#  define SWIG_init    init_presto

#endif
#define SWIG_name    "_presto"

#define SWIG_as_voidptr(a) (void *)((const void *)(a)) 
#define SWIG_as_voidptrptr(a) ((void)SWIG_as_voidptr(*a),(void**)(a)) 


#define SWIG_FILE_WITH_INIT


#include "presto.h"
#include "errno.h"

// A few function declarations from some functions not in headers
int fresnl(double xxa, double *ssa, double *cca);
int get_psr_from_parfile(char *parfilenm, double epoch, psrparams * psr);


#ifndef SWIG_FILE_WITH_INIT
#define NO_IMPORT_ARRAY
#endif
#include "stdio.h"
#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION
#include <numpy/arrayobject.h>


#include <float.h>


#include <math.h>


/* Getting isfinite working pre C99 across multiple platforms is non-trivial. Users can provide SWIG_isfinite on older platforms. */
#ifndef SWIG_isfinite
/* isfinite() is a macro for C99 */
# if defined(isfinite)
#  define SWIG_isfinite(X) (isfinite(X))
# elif defined(__cplusplus) && __cplusplus >= 201103L
/* Use a template so that this works whether isfinite() is std::isfinite() or
 * in the global namespace.  The reality seems to vary between compiler
 * versions.
 *
 * Make sure namespace std exists to avoid compiler warnings.
 *
 * extern "C++" is required as this fragment can end up inside an extern "C" { } block
 */
namespace std { }
extern "C++" template<typename T>
inline int SWIG_isfinite_func(T x) {
  using namespace std;
  return isfinite(x);
}
#  define SWIG_isfinite(X) (SWIG_isfinite_func(X))
# elif defined(__GNUC__) && (__GNUC__ > 4 || (__GNUC__ == 4 && __GNUC_MINOR__ >= 2))
#  define SWIG_isfinite(X) (__builtin_isfinite(X))
# elif defined(_MSC_VER)
#  define SWIG_isfinite(X) (_finite(X))
# elif defined(__sun) && defined(__SVR4)
#  include <ieeefp.h>
#  define SWIG_isfinite(X) (finite(X))
# endif
#endif


/* Accept infinite as a valid float value unless we are unable to check if a value is finite */
#ifdef SWIG_isfinite
# define SWIG_Float_Overflow_Check(X) ((X < -FLT_MAX || X > FLT_MAX) && SWIG_isfinite(X))
#else
# define SWIG_Float_Overflow_Check(X) ((X < -FLT_MAX || X > FLT_MAX))
#endif


SWIGINTERN int
SWIG_AsVal_double (PyObject *obj, double *val)
{
  int res = SWIG_TypeError;
  if (PyFloat_Check(obj)) {
    if (val) *val = PyFloat_AsDouble(obj);
    return SWIG_OK;
#if PY_VERSION_HEX < 0x03000000
  } else if (PyInt_Check(obj)) {
    if (val) *val = (double) PyInt_AsLong(obj);
    return SWIG_OK;
#endif
  } else if (PyLong_Check(obj)) {
    double v = PyLong_AsDouble(obj);
    if (!PyErr_Occurred()) {
      if (val) *val = v;
      return SWIG_OK;
    } else {
      PyErr_Clear();
    }
  }
#ifdef SWIG_PYTHON_CAST_MODE
  {
    int dispatch = 0;
    double d = PyFloat_AsDouble(obj);
    if (!PyErr_Occurred()) {
      if (val) *val = d;
      return SWIG_AddCast(SWIG_OK);
    } else {
      PyErr_Clear();
    }
    if (!dispatch) {
      long v = PyLong_AsLong(obj);
      if (!PyErr_Occurred()) {
	if (val) *val = v;
	return SWIG_AddCast(SWIG_AddCast(SWIG_OK));
      } else {
	PyErr_Clear();
      }
    }
  }
#endif
  return res;
}


SWIGINTERN int
SWIG_AsVal_float (PyObject * obj, float *val)
{
  double v;
  int res = SWIG_AsVal_double (obj, &v);
  if (SWIG_IsOK(res)) {
    if (SWIG_Float_Overflow_Check(v)) {
      return SWIG_OverflowError;
    } else {
      if (val) *val = (float)(v);
    }
  }  
  return res;
}


  #define SWIG_From_double   PyFloat_FromDouble 


SWIGINTERNINLINE PyObject *
SWIG_From_float  (float value)
{    
  return SWIG_From_double  (value);
}


SWIGINTERNINLINE PyObject*
  SWIG_From_int  (int value)
{
  return PyInt_FromLong((long) value);
}


SWIGINTERNINLINE int
SWIG_CanCastAsInteger(double *d, double min, double max) {
  double x = *d;
  if ((min <= x && x <= max)) {
   double fx, cx, rd;
   errno = 0;
   fx = floor(x);
   cx = ceil(x);
   rd =  ((x - fx) < 0.5) ? fx : cx; /* simple rint */
   if ((errno == EDOM) || (errno == ERANGE)) {
     errno = 0;
   } else {
     double summ, reps, diff;
     if (rd < x) {
       diff = x - rd;
     } else if (rd > x) {
       diff = rd - x;
     } else {
       return 1;
     }
     summ = rd + x;
     reps = diff/summ;
     if (reps < 8*DBL_EPSILON) {
       *d = rd;
       return 1;
     }
   }
  }
  return 0;
}


SWIGINTERN int
SWIG_AsVal_long (PyObject *obj, long* val)
{
#if PY_VERSION_HEX < 0x03000000
  if (PyInt_Check(obj)) {
    if (val) *val = PyInt_AsLong(obj);
    return SWIG_OK;
  } else
#endif
  if (PyLong_Check(obj)) {
    long v = PyLong_AsLong(obj);
    if (!PyErr_Occurred()) {
      if (val) *val = v;
      return SWIG_OK;
    } else {
      PyErr_Clear();
      return SWIG_OverflowError;
    }
  }
#ifdef SWIG_PYTHON_CAST_MODE
  {
    int dispatch = 0;
    long v = PyInt_AsLong(obj);
    if (!PyErr_Occurred()) {
      if (val) *val = v;
      return SWIG_AddCast(SWIG_OK);
    } else {
      PyErr_Clear();
    }
    if (!dispatch) {
      double d;
      int res = SWIG_AddCast(SWIG_AsVal_double (obj,&d));
      // Largest double not larger than LONG_MAX (not portably calculated easily)
      // Note that double(LONG_MAX) is stored in a double rounded up by one (for 64-bit long)
      // 0x7ffffffffffffc00LL == (int64_t)std::nextafter(double(__uint128_t(LONG_MAX)+1), double(0))
      const double long_max = sizeof(long) == 8 ? 0x7ffffffffffffc00LL : LONG_MAX;
      // No equivalent needed for 64-bit double(LONG_MIN) is exactly LONG_MIN
      if (SWIG_IsOK(res) && SWIG_CanCastAsInteger(&d, LONG_MIN, long_max)) {
	if (val) *val = (long)(d);
	return res;
      }
    }
  }
#endif
  return SWIG_TypeError;
}


#include <limits.h>
#if !defined(SWIG_NO_LLONG_MAX)
# if !defined(LLONG_MAX) && defined(__GNUC__) && defined (__LONG_LONG_MAX__)
#   define LLONG_MAX __LONG_LONG_MAX__
#   define LLONG_MIN (-LLONG_MAX - 1LL)
#   define ULLONG_MAX (LLONG_MAX * 2ULL + 1ULL)
# endif
#endif


#if defined(LLONG_MAX) && !defined(SWIG_LONG_LONG_AVAILABLE)
#  define SWIG_LONG_LONG_AVAILABLE
#endif


#ifdef SWIG_LONG_LONG_AVAILABLE
SWIGINTERN int
SWIG_AsVal_long_SS_long (PyObject *obj, long long *val)
{
  int res = SWIG_TypeError;
  if (PyLong_Check(obj)) {
    long long v = PyLong_AsLongLong(obj);
    if (!PyErr_Occurred()) {
      if (val) *val = v;
      return SWIG_OK;
    } else {
      PyErr_Clear();
      res = SWIG_OverflowError;
    }
  } else {
    long v;
    res = SWIG_AsVal_long (obj,&v);
    if (SWIG_IsOK(res)) {
      if (val) *val = v;
      return res;
    }
  }
#ifdef SWIG_PYTHON_CAST_MODE
  {
    const double mant_max = 1LL << DBL_MANT_DIG;
    const double mant_min = -mant_max;
    double d;
    res = SWIG_AsVal_double (obj,&d);
    if (SWIG_IsOK(res) && !SWIG_CanCastAsInteger(&d, mant_min, mant_max))
      return SWIG_OverflowError;
    if (SWIG_IsOK(res) && SWIG_CanCastAsInteger(&d, mant_min, mant_max)) {
      if (val) *val = (long long)(d);
      return SWIG_AddCast(res);
    }
    res = SWIG_TypeError;
  }
#endif
  return res;
}
#endif


#ifdef SWIG_LONG_LONG_AVAILABLE
SWIGINTERNINLINE PyObject* 
SWIG_From_long_SS_long  (long long value)
{
  return ((value < LONG_MIN) || (value > LONG_MAX)) ?
    PyLong_FromLongLong(value) : PyInt_FromLong((long)(value));
}
#endif


#if NPY_API_VERSION < 0x00000007
#define NPY_ARRAY_DEFAULT NPY_DEFAULT
#define NPY_ARRAY_FARRAY  NPY_FARRAY
#define NPY_FORTRANORDER  NPY_FORTRAN
#endif


/* Macros to extract array attributes.
 */
#if NPY_API_VERSION < 0x00000007
#define is_array(a)            ((a) && PyArray_Check((PyArrayObject*)a))
#define array_type(a)          (int)(PyArray_TYPE((PyArrayObject*)a))
#define array_numdims(a)       (((PyArrayObject*)a)->nd)
#define array_dimensions(a)    (((PyArrayObject*)a)->dimensions)
#define array_size(a,i)        (((PyArrayObject*)a)->dimensions[i])
#define array_strides(a)       (((PyArrayObject*)a)->strides)
#define array_stride(a,i)      (((PyArrayObject*)a)->strides[i])
#define array_data(a)          (((PyArrayObject*)a)->data)
#define array_descr(a)         (((PyArrayObject*)a)->descr)
#define array_flags(a)         (((PyArrayObject*)a)->flags)
#define array_clearflags(a,f)  (((PyArrayObject*)a)->flags) &= ~f
#define array_enableflags(a,f) (((PyArrayObject*)a)->flags) = f
#define array_is_fortran(a)    (PyArray_ISFORTRAN((PyArrayObject*)a))
#else
#define is_array(a)            ((a) && PyArray_Check(a))
#define array_type(a)          PyArray_TYPE((PyArrayObject*)a)
#define array_numdims(a)       PyArray_NDIM((PyArrayObject*)a)
#define array_dimensions(a)    PyArray_DIMS((PyArrayObject*)a)
#define array_strides(a)       PyArray_STRIDES((PyArrayObject*)a)
#define array_stride(a,i)      PyArray_STRIDE((PyArrayObject*)a,i)
#define array_size(a,i)        PyArray_DIM((PyArrayObject*)a,i)
#define array_data(a)          PyArray_DATA((PyArrayObject*)a)
#define array_descr(a)         PyArray_DESCR((PyArrayObject*)a)
#define array_flags(a)         PyArray_FLAGS((PyArrayObject*)a)
#define array_enableflags(a,f) PyArray_ENABLEFLAGS((PyArrayObject*)a,f)
#define array_clearflags(a,f)  PyArray_CLEARFLAGS((PyArrayObject*)a,f)
#define array_is_fortran(a)    (PyArray_IS_F_CONTIGUOUS((PyArrayObject*)a))
#endif
#define array_is_contiguous(a) (PyArray_ISCONTIGUOUS((PyArrayObject*)a))
#define array_is_native(a)     (PyArray_ISNOTSWAPPED((PyArrayObject*)a))


  /* Given a PyObject, return a string describing its type.
   */
  const char* pytype_string(PyObject* py_obj)
  {
    if (py_obj == NULL          ) return "C NULL value";
    if (py_obj == Py_None       ) return "Python None" ;
    if (PyCallable_Check(py_obj)) return "callable"    ;
    if (PyBytes_Check(   py_obj)) return "string"      ;
    if (PyLong_Check(    py_obj)) return "int"         ;
    if (PyFloat_Check(   py_obj)) return "float"       ;
    if (PyDict_Check(    py_obj)) return "dict"        ;
    if (PyList_Check(    py_obj)) return "list"        ;
    if (PyTuple_Check(   py_obj)) return "tuple"       ;

    return "unknown type";
  }

  /* Given a NumPy typecode, return a string describing the type.
   */
  const char* typecode_string(int typecode)
  {
    static const char* type_names[25] = {"bool",
                                         "byte",
                                         "unsigned byte",
                                         "short",
                                         "unsigned short",
                                         "int",
                                         "unsigned int",
                                         "long",
                                         "unsigned long",
                                         "long long",
                                         "unsigned long long",
                                         "float",
                                         "double",
                                         "long double",
                                         "complex float",
                                         "complex double",
                                         "complex long double",
                                         "object",
                                         "string",
                                         "unicode",
                                         "void",
                                         "ntypes",
                                         "notype",
                                         "char",
                                         "unknown"};
    return typecode < 24 ? type_names[typecode] : type_names[24];
  }

  /* Make sure input has correct numpy type.  This now just calls
     PyArray_EquivTypenums().
   */
  int type_match(int actual_type,
                 int desired_type)
  {
    return PyArray_EquivTypenums(actual_type, desired_type);
  }

#ifdef SWIGPY_USE_CAPSULE
  void free_cap(PyObject * cap)
  {
    void* array = (void*) PyCapsule_GetPointer(cap,SWIGPY_CAPSULE_NAME);
    if (array != NULL) free(array);
  }
#endif




  /* Given a PyObject pointer, cast it to a PyArrayObject pointer if
   * legal.  If not, set the python error string appropriately and
   * return NULL.
   */
  PyArrayObject* obj_to_array_no_conversion(PyObject* input,
                                            int        typecode)
  {
    PyArrayObject* ary = NULL;
    if (is_array(input) && (typecode == NPY_NOTYPE ||
                            PyArray_EquivTypenums(array_type(input), typecode)))
    {
      ary = (PyArrayObject*) input;
    }
    else if is_array(input)
    {
      const char* desired_type = typecode_string(typecode);
      const char* actual_type  = typecode_string(array_type(input));
      PyErr_Format(PyExc_TypeError,
                   "Array of type '%s' required.  Array of type '%s' given",
                   desired_type, actual_type);
      ary = NULL;
    }
    else
    {
      const char* desired_type = typecode_string(typecode);
      const char* actual_type  = pytype_string(input);
      PyErr_Format(PyExc_TypeError,
                   "Array of type '%s' required.  A '%s' was given",
                   desired_type,
                   actual_type);
      ary = NULL;
    }
    return ary;
  }

  /* Convert the given PyObject to a NumPy array with the given
   * typecode.  On success, return a valid PyArrayObject* with the
   * correct type.  On failure, the python error string will be set and
   * the routine returns NULL.
   */
  PyArrayObject* obj_to_array_allow_conversion(PyObject* input,
                                               int       typecode,
                                               int*      is_new_object)
  {
    PyArrayObject* ary = NULL;
    PyObject*      py_obj;
    if (is_array(input) && (typecode == NPY_NOTYPE ||
                            PyArray_EquivTypenums(array_type(input),typecode)))
    {
      ary = (PyArrayObject*) input;
      *is_new_object = 0;
    }
    else
    {
      py_obj = PyArray_FROMANY(input, typecode, 0, 0, NPY_ARRAY_DEFAULT);
      /* If NULL, PyArray_FromObject will have set python error value.*/
      ary = (PyArrayObject*) py_obj;
      *is_new_object = 1;
    }
    return ary;
  }

  /* Given a PyArrayObject, check to see if it is contiguous.  If so,
   * return the input pointer and flag it as not a new object.  If it is
   * not contiguous, create a new PyArrayObject using the original data,
   * flag it as a new object and return the pointer.
   */
  PyArrayObject* make_contiguous(PyArrayObject* ary,
                                 int*           is_new_object,
                                 int            min_dims,
                                 int            max_dims)
  {
    PyArrayObject* result;
    if (array_is_contiguous(ary))
    {
      result = ary;
      *is_new_object = 0;
    }
    else
    {
      result = (PyArrayObject*) PyArray_ContiguousFromObject((PyObject*)ary,
                                                              array_type(ary),
                                                              min_dims,
                                                              max_dims);
      *is_new_object = 1;
    }
    return result;
  }

  /* Given a PyArrayObject, check to see if it is Fortran-contiguous.
   * If so, return the input pointer, but do not flag it as not a new
   * object.  If it is not Fortran-contiguous, create a new
   * PyArrayObject using the original data, flag it as a new object
   * and return the pointer.
   */
  PyArrayObject* make_fortran(PyArrayObject* ary,
                              int*           is_new_object)
  {
    PyArrayObject* result;
    if (array_is_fortran(ary))
    {
      result = ary;
      *is_new_object = 0;
    }
    else
    {
      Py_INCREF(array_descr(ary));
      result = (PyArrayObject*) PyArray_FromArray(ary,
                                                  array_descr(ary),
#if NPY_API_VERSION < 0x00000007
                                                  NPY_FORTRANORDER);
#else
                                                  NPY_ARRAY_F_CONTIGUOUS);
#endif
      *is_new_object = 1;
    }
    return result;
  }

  /* Convert a given PyObject to a contiguous PyArrayObject of the
   * specified type.  If the input object is not a contiguous
   * PyArrayObject, a new one will be created and the new object flag
   * will be set.
   */
  PyArrayObject* obj_to_array_contiguous_allow_conversion(PyObject* input,
                                                          int       typecode,
                                                          int*      is_new_object)
  {
    int is_new1 = 0;
    int is_new2 = 0;
    PyArrayObject* ary2;
    PyArrayObject* ary1 = obj_to_array_allow_conversion(input,
                                                        typecode,
                                                        &is_new1);
    if (ary1)
    {
      ary2 = make_contiguous(ary1, &is_new2, 0, 0);
      if ( is_new1 && is_new2)
      {
        Py_DECREF(ary1);
      }
      ary1 = ary2;
    }
    *is_new_object = is_new1 || is_new2;
    return ary1;
  }

  /* Convert a given PyObject to a Fortran-ordered PyArrayObject of the
   * specified type.  If the input object is not a Fortran-ordered
   * PyArrayObject, a new one will be created and the new object flag
   * will be set.
   */
  PyArrayObject* obj_to_array_fortran_allow_conversion(PyObject* input,
                                                       int       typecode,
                                                       int*      is_new_object)
  {
    int is_new1 = 0;
    int is_new2 = 0;
    PyArrayObject* ary2;
    PyArrayObject* ary1 = obj_to_array_allow_conversion(input,
                                                        typecode,
                                                        &is_new1);
    if (ary1)
    {
      ary2 = make_fortran(ary1, &is_new2);
      if (is_new1 && is_new2)
      {
        Py_DECREF(ary1);
      }
      ary1 = ary2;
    }
    *is_new_object = is_new1 || is_new2;
    return ary1;
  }


  /* Test whether a python object is contiguous.  If array is
   * contiguous, return 1.  Otherwise, set the python error string and
   * return 0.
   */
  int require_contiguous(PyArrayObject* ary)
  {
    int contiguous = 1;
    if (!array_is_contiguous(ary))
    {
      PyErr_SetString(PyExc_TypeError,
                      "Array must be contiguous.  A non-contiguous array was given");
      contiguous = 0;
    }
    return contiguous;
  }

  /* Test whether a python object is (C_ or F_) contiguous.  If array is
   * contiguous, return 1.  Otherwise, set the python error string and
   * return 0.
   */
  int require_c_or_f_contiguous(PyArrayObject* ary)
  {
    int contiguous = 1;
    if (!(array_is_contiguous(ary) || array_is_fortran(ary)))
    {
      PyErr_SetString(PyExc_TypeError,
                      "Array must be contiguous (C_ or F_).  A non-contiguous array was given");
      contiguous = 0;
    }
    return contiguous;
  }

  /* Require that a numpy array is not byte-swapped.  If the array is
   * not byte-swapped, return 1.  Otherwise, set the python error string
   * and return 0.
   */
  int require_native(PyArrayObject* ary)
  {
    int native = 1;
    if (!array_is_native(ary))
    {
      PyErr_SetString(PyExc_TypeError,
                      "Array must have native byteorder.  "
                      "A byte-swapped array was given");
      native = 0;
    }
    return native;
  }

  /* Require the given PyArrayObject to have a specified number of
   * dimensions.  If the array has the specified number of dimensions,
   * return 1.  Otherwise, set the python error string and return 0.
   */
  int require_dimensions(PyArrayObject* ary,
                         int            exact_dimensions)
  {
    int success = 1;
    if (array_numdims(ary) != exact_dimensions)
    {
      PyErr_Format(PyExc_TypeError,
                   "Array must have %d dimensions.  Given array has %d dimensions",
                   exact_dimensions,
                   array_numdims(ary));
      success = 0;
    }
    return success;
  }

  /* Require the given PyArrayObject to have one of a list of specified
   * number of dimensions.  If the array has one of the specified number
   * of dimensions, return 1.  Otherwise, set the python error string
   * and return 0.
   */
  int require_dimensions_n(PyArrayObject* ary,
                           int*           exact_dimensions,
                           int            n)
  {
    int success = 0;
    int i;
    char dims_str[255] = "";
    char s[255];
    for (i = 0; i < n && !success; i++)
    {
      if (array_numdims(ary) == exact_dimensions[i])
      {
        success = 1;
      }
    }
    if (!success)
    {
      for (i = 0; i < n-1; i++)
      {
        sprintf(s, "%d, ", exact_dimensions[i]);
        strcat(dims_str,s);
      }
      sprintf(s, " or %d", exact_dimensions[n-1]);
      strcat(dims_str,s);
      PyErr_Format(PyExc_TypeError,
                   "Array must have %s dimensions.  Given array has %d dimensions",
                   dims_str,
                   array_numdims(ary));
    }
    return success;
  }

  /* Require the given PyArrayObject to have a specified shape.  If the
   * array has the specified shape, return 1.  Otherwise, set the python
   * error string and return 0.
   */
  int require_size(PyArrayObject* ary,
                   npy_intp*      size,
                   int            n)
  {
    int i;
    int success = 1;
    size_t len;
    char desired_dims[255] = "[";
    char s[255];
    char actual_dims[255] = "[";
    for(i=0; i < n;i++)
    {
      if (size[i] != -1 &&  size[i] != array_size(ary,i))
      {
        success = 0;
      }
    }
    if (!success)
    {
      for (i = 0; i < n; i++)
      {
        if (size[i] == -1)
        {
          sprintf(s, "*,");
        }
        else
        {
          sprintf(s, "%ld,", (long int)size[i]);
        }
        strcat(desired_dims,s);
      }
      len = strlen(desired_dims);
      desired_dims[len-1] = ']';
      for (i = 0; i < n; i++)
      {
        sprintf(s, "%ld,", (long int)array_size(ary,i));
        strcat(actual_dims,s);
      }
      len = strlen(actual_dims);
      actual_dims[len-1] = ']';
      PyErr_Format(PyExc_TypeError,
                   "Array must have shape of %s.  Given array has shape of %s",
                   desired_dims,
                   actual_dims);
    }
    return success;
  }

  /* Require the given PyArrayObject to to be Fortran ordered.  If the
   * the PyArrayObject is already Fortran ordered, do nothing.  Else,
   * set the Fortran ordering flag and recompute the strides.
   */
  int require_fortran(PyArrayObject* ary)
  {
    int success = 1;
    int nd = array_numdims(ary);
    int i;
    npy_intp * strides = array_strides(ary);
    if (array_is_fortran(ary)) return success;
    int n_non_one = 0;
    /* Set the Fortran ordered flag */
    const npy_intp *dims = array_dimensions(ary);
    for (i=0; i < nd; ++i)
      n_non_one += (dims[i] != 1) ? 1 : 0;
    if (n_non_one > 1)
      array_clearflags(ary,NPY_ARRAY_CARRAY);
    array_enableflags(ary,NPY_ARRAY_FARRAY);
    /* Recompute the strides */
    strides[0] = strides[nd-1];
    for (i=1; i < nd; ++i)
      strides[i] = strides[i-1] * array_size(ary,i-1);
    return success;
  }




SWIGINTERN int
SWIG_AsVal_int (PyObject * obj, int *val)
{
  long v;
  int res = SWIG_AsVal_long (obj, &v);
  if (SWIG_IsOK(res)) {
    if ((v < INT_MIN || v > INT_MAX)) {
      return SWIG_OverflowError;
    } else {
      if (val) *val = (int)(v);
    }
  }  
  return res;
}


SWIGINTERN swig_type_info*
SWIG_pchar_descriptor(void)
{
  static int init = 0;
  static swig_type_info* info = 0;
  if (!init) {
    info = SWIG_TypeQuery("_p_char");
    init = 1;
  }
  return info;
}


/* Return string from Python obj. NOTE: obj must remain in scope in order
   to use the returned cptr (but only when alloc is set to SWIG_OLDOBJ) */
SWIGINTERN int
SWIG_AsCharPtrAndSize(PyObject *obj, char **cptr, size_t *psize, int *alloc)
{
#if PY_VERSION_HEX>=0x03000000
#if defined(SWIG_PYTHON_STRICT_BYTE_CHAR)
  if (PyBytes_Check(obj))
#else
  if (PyUnicode_Check(obj))
#endif
#else  
  if (PyString_Check(obj))
#endif
  {
    char *cstr; Py_ssize_t len;
    PyObject *bytes = NULL;
    int ret = SWIG_OK;
    if (alloc)
      *alloc = SWIG_OLDOBJ;
#if PY_VERSION_HEX>=0x03000000 && defined(SWIG_PYTHON_STRICT_BYTE_CHAR)
    if (PyBytes_AsStringAndSize(obj, &cstr, &len) == -1)
      return SWIG_TypeError;
#else
    cstr = (char *)SWIG_PyUnicode_AsUTF8AndSize(obj, &len, &bytes);
    if (!cstr)
      return SWIG_TypeError;
    /* The returned string is only duplicated if the char * returned is not owned and memory managed by obj */
    if (bytes && cptr) {
      if (alloc) {
        cstr = (char *)memcpy(malloc((len + 1)*sizeof(char)), cstr, sizeof(char)*(len + 1));
        *alloc = SWIG_NEWOBJ;
      } else {
        /* alloc must be set in order to clean up allocated memory */
        return SWIG_RuntimeError;
      }
    }
#endif
    if (cptr) *cptr = cstr;
    if (psize) *psize = len + 1;
    SWIG_Py_XDECREF(bytes);
    return ret;
  } else {
#if defined(SWIG_PYTHON_2_UNICODE)
#if defined(SWIG_PYTHON_STRICT_BYTE_CHAR)
#error "Cannot use both SWIG_PYTHON_2_UNICODE and SWIG_PYTHON_STRICT_BYTE_CHAR at once"
#endif
#if PY_VERSION_HEX<0x03000000
    if (PyUnicode_Check(obj)) {
      char *cstr; Py_ssize_t len;
      if (!alloc && cptr) {
        return SWIG_RuntimeError;
      }
      obj = PyUnicode_AsUTF8String(obj);
      if (!obj)
        return SWIG_TypeError;
      if (PyString_AsStringAndSize(obj, &cstr, &len) != -1) {
        if (cptr) {
          if (alloc) *alloc = SWIG_NEWOBJ;
          *cptr = (char *)memcpy(malloc((len + 1)*sizeof(char)), cstr, sizeof(char)*(len + 1));
        }
        if (psize) *psize = len + 1;

        SWIG_Py_XDECREF(obj);
        return SWIG_OK;
      } else {
        SWIG_Py_XDECREF(obj);
      }
    }
#endif
#endif

    swig_type_info* pchar_descriptor = SWIG_pchar_descriptor();
    if (pchar_descriptor) {
      void* vptr = 0;
      if (SWIG_ConvertPtr(obj, &vptr, pchar_descriptor, 0) == SWIG_OK) {
	if (cptr) *cptr = (char *) vptr;
	if (psize) *psize = vptr ? (strlen((char *)vptr) + 1) : 0;
	if (alloc) *alloc = SWIG_OLDOBJ;
	return SWIG_OK;
      }
    }
  }
  return SWIG_TypeError;
}





SWIGINTERNINLINE PyObject *
SWIG_FromCharPtrAndSize(const char* carray, size_t size)
{
  if (carray) {
    if (size > INT_MAX) {
      swig_type_info* pchar_descriptor = SWIG_pchar_descriptor();
      return pchar_descriptor ? 
	SWIG_InternalNewPointerObj((char *)(carray), pchar_descriptor, 0) : SWIG_Py_Void();
    } else {
#if PY_VERSION_HEX >= 0x03000000
#if defined(SWIG_PYTHON_STRICT_BYTE_CHAR)
      return PyBytes_FromStringAndSize(carray, (Py_ssize_t)(size));
#else
      return PyUnicode_DecodeUTF8(carray, (Py_ssize_t)(size), "surrogateescape");
#endif
#else
      return PyString_FromStringAndSize(carray, (Py_ssize_t)(size));
#endif
    }
  } else {
    return SWIG_Py_Void();
  }
}


SWIGINTERNINLINE PyObject * 
SWIG_FromCharPtr(const char *cptr)
{ 
  return SWIG_FromCharPtrAndSize(cptr, (cptr ? strlen(cptr) : 0));
}


    //double __getitem__(int index) {
    //    return self->onoff[index];
    //}
    //void __setitem__(int index, double val) {
    //    self->onoff[index] = val;
    //}
    char *INFODATA_notes_get(infodata *p) {
        return p->notes;
    }
    void INFODATA_notes_set(infodata *p, char *val) {
        strncpy(p->notes,val,499);
        p->notes[499] = '\0';
    }
    char *INFODATA_name_get(infodata *p) {
        return p->name;
    }
    void INFODATA_name_set(infodata *p, char *val) {
        strncpy(p->name,val,199);
        p->name[199] = '\0';
    }
    char *INFODATA_object_get(infodata *p) {
        return p->object;
    }
    void INFODATA_object_set(infodata *p, char *val) {
        strncpy(p->object,val,99);
        p->object[99] = '\0';
    }
    char *INFODATA_instrument_get(infodata *p) {
        return p->instrument;
    }
    void INFODATA_instrument_set(infodata *p, char *val) {
        strncpy(p->instrument,val,99);
        p->instrument[99] = '\0';
    }
    char *INFODATA_observer_get(infodata *p) {
        return p->observer;
    }
    void INFODATA_observer_set(infodata *p, char *val) {
        strncpy(p->observer,val,99);
        p->observer[99] = '\0';
    }
    char *INFODATA_analyzer_get(infodata *p) {
        return p->analyzer;
    }
    void INFODATA_analyzer_set(infodata *p, char *val) {
        strncpy(p->analyzer,val,99);
        p->analyzer[99] = '\0';
    }
    char *INFODATA_telescope_get(infodata *p) {
        return p->telescope;
    }
    void INFODATA_telescope_set(infodata *p, char *val) {
        strncpy(p->telescope,val,39);
        p->telescope[39] = '\0';
    }
    char *INFODATA_band_get(infodata *p) {
        return p->band;
    }
    void INFODATA_band_set(infodata *p, char *val) {
        strncpy(p->band,val,39);
        p->band[39] = '\0';
    }
    char *INFODATA_filt_get(infodata *p) {
        return p->filt;
    }
    void INFODATA_filt_set(infodata *p, char *val) {
        strncpy(p->filt,val,6);
        p->filt[6] = '\0';
    }


    char *PSRPARAMS_jname_get(psrparams *p) {
        return p->jname;
    }
    void PSRPARAMS_jname_set(psrparams *p, char *val) {
        strncpy(p->jname,val,12);
        p->jname[12] = '\0';
    }
    char *PSRPARAMS_bname_get(psrparams *p) {
        return p->bname;
    }
    void PSRPARAMS_bname_set(psrparams *p, char *val) {
        strncpy(p->bname,val,8);
        p->bname[8] = '\0';
    }
    char *PSRPARAMS_alias_get(psrparams *p) {
        return p->alias;
    }
    void PSRPARAMS_alias_set(psrparams *p, char *val) {
        strncpy(p->alias,val,9);
        p->alias[9] = '\0';
    }


void wrap_gen_fvect(long nl, float** vect1, long *n1)
{
    *vect1 = gen_fvect(nl);
    *n1 = nl;
}
void wrap_gen_cvect(long nl, fcomplex** vect2, long *n2)
{
    *vect2 = gen_cvect(nl);
    *n2 = nl;
}


void wrap_power_arr(fcomplex *dft, long N, float **vect, long *nn){
    /* Determine the spectral powers of the Fourier amplitudes 'dft'*/
    float powargr, powargi, *powers;
    long ii;
    
    powers = gen_fvect(N);
    for (ii=0; ii<N; ii++)
        powers[ii] = POWER(dft[ii].r, dft[ii].i);
    *vect = powers;
    *nn = N;
}

void wrap_phase_arr(fcomplex *dft, long N, float **vect, long *nn){
    /* Determine the spectral phases of the Fourier amplitudes 'dft'*/
    float phsargr, phsargi, phstmp, *phases;
    long ii;
    
    phases = gen_fvect(N);
    for (ii=0; ii<N; ii++)
        phases[ii] = PHASE(dft[ii].r, dft[ii].i);
    *vect = phases;
    *nn = N;
}


void wrap_dorbint(double **vect, long *nn, double Eo, long numpts, 
                 double dt, orbitparams *orb)
{
    *vect = dorbint(Eo, numpts, dt, orb);
    *nn = numpts;
}


void wrap_gen_r_response(double roffset, int numbetween, int numkern,
                         fcomplex **vect, long *nn){
    *vect = gen_r_response(roffset, numbetween, numkern);
    *nn = numkern;
}

void wrap_gen_z_response(double roffset, int numbetween, int numkern,
                         double z,
                         fcomplex **vect, long *nn){
    *vect = gen_z_response(roffset, numbetween, z, numkern);
    *nn = numkern;
}

void wrap_gen_w_response(double roffset, int numbetween, int numkern,
                         double z, double w,
                         fcomplex **vect, long *nn){
    *vect = gen_w_response(roffset, numbetween, z, w, numkern);
    *nn = numkern;
}

void wrap_gen_w_response2(double roffset, int numbetween, int numkern,
                          double z, double w,
                         fcomplex **vect, long *nn){
    *vect = gen_w_response2(roffset, numbetween, z, w, numkern);
    *nn = numkern;
}

void wrap_gen_bin_response(double roffset, int numbetween, int numkern,
                           double ppsr, double T, orbitparams *orbit,
                           fcomplex **vect, long *nn){
    *vect = gen_bin_response(roffset, numbetween, ppsr, T, 
                             orbit, numkern);
    *nn = numkern;
}


    double extended_equiv_gaussian_sigma(double logp);
    double log_asymtotic_incomplete_gamma(double a, double z);
    double log_asymtotic_gamma(double z);


SWIGINTERN int
SWIG_AsVal_unsigned_SS_long (PyObject *obj, unsigned long *val) 
{
#if PY_VERSION_HEX < 0x03000000
  if (PyInt_Check(obj)) {
    long v = PyInt_AsLong(obj);
    if (v >= 0) {
      if (val) *val = v;
      return SWIG_OK;
    } else {
      return SWIG_OverflowError;
    }
  } else
#endif
  if (PyLong_Check(obj)) {
    unsigned long v = PyLong_AsUnsignedLong(obj);
    if (!PyErr_Occurred()) {
      if (val) *val = v;
      return SWIG_OK;
    } else {
      PyErr_Clear();
      return SWIG_OverflowError;
    }
  }
#ifdef SWIG_PYTHON_CAST_MODE
  {
    int dispatch = 0;
    unsigned long v = PyLong_AsUnsignedLong(obj);
    if (!PyErr_Occurred()) {
      if (val) *val = v;
      return SWIG_AddCast(SWIG_OK);
    } else {
      PyErr_Clear();
    }
    if (!dispatch) {
      double d;
      int res = SWIG_AddCast(SWIG_AsVal_double (obj,&d));
      // Largest double not larger than ULONG_MAX (not portably calculated easily)
      // Note that double(ULONG_MAX) is stored in a double rounded up by one (for 64-bit unsigned long)
      // 0xfffffffffffff800ULL == (uint64_t)std::nextafter(double(__uint128_t(ULONG_MAX)+1), double(0))
      const double ulong_max = sizeof(unsigned long) == 8 ? 0xfffffffffffff800ULL : ULONG_MAX;
      if (SWIG_IsOK(res) && SWIG_CanCastAsInteger(&d, 0, ulong_max)) {
	if (val) *val = (unsigned long)(d);
	return res;
      }
    }
  }
#endif
  return SWIG_TypeError;
}


    void wrap_rz_interp(fcomplex *data, long N, double r, double z,
                        int kern_half_width, float * ansr, float * ansi){
        fcomplex ans;
        rz_interp(data, N, r, z, kern_half_width, &ans);
        *ansr = ans.r;
        *ansi = ans.i;
    }


    void wrap_corr_rz_plane(fcomplex *data, long N, int numbetween,
                            int startbin, double zlo, double zhi,
                            int numz, int fftlen,
                            presto_interp_acc accuracy,
                            fcomplex **arr, long *nr, long *nc){
        fcomplex **outarr;
        int nextbin;
        outarr = corr_rz_plane(data, N, numbetween, startbin, zlo, zhi,
                               numz, fftlen, accuracy, &nextbin);
        *arr = outarr[0];
        *nr = numz;
        *nc = (nextbin - startbin) * numbetween;
        vect_free(outarr);
    }


    void wrap_corr_rzw_vol(fcomplex *data, long N, int numbetween, \
                           int startbin, double zlo, double zhi, int numz, \
                           double wlo, double whi, int numw, int fftlen, \
                           presto_interp_acc accuracy, \
                           fcomplex **arr, long *nh, long *nr, long *nc){
        fcomplex ***outarr;
        int nextbin;
        outarr = corr_rzw_vol(data, N, numbetween, startbin, zlo, zhi,
                              numz, wlo, whi, numw, fftlen, accuracy, &nextbin);
        *arr = outarr[0][0];
        *nh = numw;
        *nr = numz;
        *nc = (nextbin - startbin) * numbetween;
        vect_free(outarr[0]);
        vect_free(outarr);
    }


    void wrap_max_r_arr(fcomplex * data, long numdata, double rin,
                         rderivs * derivs, double *rout, double *powout){
        double pow;
        pow = max_r_arr(data, numdata, rin, rout, derivs);
        *powout = pow;
    }


    void wrap_max_rz_arr(fcomplex * data, long numdata, double rin, double zin,
                         rderivs * derivs, double *rout, double *zout, double *powout){
        double pow;
        pow = max_rz_arr(data, numdata, rin, zin, rout, zout, derivs);
        *powout = pow;
    }


    void wrap_max_rz_arr_harmonics(fcomplex *data, long numdata,
                                   double rin, double zin,
                                   double *derivdata, int len,
                                   double *rout, double *zout){
        int ii, numharm = len / 7;
        double *powers;
        rderivs *derivs = (rderivs *)malloc(sizeof(rderivs) * numharm);
        
        powers = gen_dvect(numharm);
        max_rz_arr_harmonics(data, numdata, numharm, rin, zin, rout, zout, derivs, powers);
        vect_free(powers);
        // Hack to effectively return a array of rderivs
        for (ii = 0 ; ii < numharm ; ii++) {
            derivdata[ii*7+0] = derivs[ii].pow;
            derivdata[ii*7+1] = derivs[ii].phs;
            derivdata[ii*7+2] = derivs[ii].dpow;
            derivdata[ii*7+3] = derivs[ii].dphs;
            derivdata[ii*7+4] = derivs[ii].d2pow;
            derivdata[ii*7+5] = derivs[ii].d2phs;
            derivdata[ii*7+6] = derivs[ii].locpow;
        }
        free(derivs);
    }


    void wrap_max_rzw_arr_harmonics(fcomplex *data, long numdata,
                                    double rin, double zin, double win,
                                    double *derivdata, int len,
                                    double *rout, double *zout, double *wout){
        int ii, numharm = len / 7;
        double *powers = gen_dvect(numharm);
        rderivs *derivs = (rderivs *)malloc(sizeof(rderivs) * numharm);
        
        max_rzw_arr_harmonics(data, numdata, numharm, rin, zin, win,
                              rout, zout, wout, derivs, powers);
        vect_free(powers);
        // Hack to effectively return a array of rderivs
        for (ii = 0 ; ii < numharm ; ii++) {
            derivdata[ii*7+0] = derivs[ii].pow;
            derivdata[ii*7+1] = derivs[ii].phs;
            derivdata[ii*7+2] = derivs[ii].dpow;
            derivdata[ii*7+3] = derivs[ii].dphs;
            derivdata[ii*7+4] = derivs[ii].d2pow;
            derivdata[ii*7+5] = derivs[ii].d2phs;
            derivdata[ii*7+6] = derivs[ii].locpow;
        }
        free(derivs);
    }


    void wrap_max_rzw_arr(fcomplex * data, long numdata, double rin, double zin, double win,
                          rderivs * derivs, double *rout, double *zout, double *wout, double *powout){
        double pow;
        pow = max_rzw_arr(data, numdata, rin, zin, win, rout, zout, wout, derivs);
        *powout = pow;
    }


    void wrap_barycenter(double *topotimes, long N1,
                         double *barytimes, long N2,
                         double *voverc, long N3,
                         char *ra, char *dec, char *obs, char *ephem){
        barycenter(topotimes, barytimes, voverc, N1, ra, dec, obs, ephem);
    }


    double wrap_simplefold(float *data, long N1, double dt, double tlo,
                           double *prof, long N2, double startphs,
                           double f0, double fdot, double fdotdot,
                           int standard){
        return simplefold(data, N1, dt, tlo,
                          prof, N2, startphs,
                          f0, fdot, fdotdot, standard);
    }

#ifdef __cplusplus
extern "C" {
#endif
SWIGINTERN PyObject *_wrap_fcomplex_r_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct FCOMPLEX *arg1 = (struct FCOMPLEX *) 0 ;
  float arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  float val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "fcomplex_r_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_FCOMPLEX, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "fcomplex_r_set" "', argument " "1"" of type '" "struct FCOMPLEX *""'"); 
  }
  arg1 = (struct FCOMPLEX *)(argp1);
  ecode2 = SWIG_AsVal_float(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "fcomplex_r_set" "', argument " "2"" of type '" "float""'");
  } 
  arg2 = (float)(val2);
  if (arg1) (arg1)->r = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_fcomplex_r_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct FCOMPLEX *arg1 = (struct FCOMPLEX *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  float result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_FCOMPLEX, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "fcomplex_r_get" "', argument " "1"" of type '" "struct FCOMPLEX *""'"); 
  }
  arg1 = (struct FCOMPLEX *)(argp1);
  result = (float) ((arg1)->r);
  resultobj = SWIG_From_float((float)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_fcomplex_i_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct FCOMPLEX *arg1 = (struct FCOMPLEX *) 0 ;
  float arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  float val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "fcomplex_i_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_FCOMPLEX, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "fcomplex_i_set" "', argument " "1"" of type '" "struct FCOMPLEX *""'"); 
  }
  arg1 = (struct FCOMPLEX *)(argp1);
  ecode2 = SWIG_AsVal_float(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "fcomplex_i_set" "', argument " "2"" of type '" "float""'");
  } 
  arg2 = (float)(val2);
  if (arg1) (arg1)->i = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_fcomplex_i_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct FCOMPLEX *arg1 = (struct FCOMPLEX *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  float result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_FCOMPLEX, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "fcomplex_i_get" "', argument " "1"" of type '" "struct FCOMPLEX *""'"); 
  }
  arg1 = (struct FCOMPLEX *)(argp1);
  result = (float) ((arg1)->i);
  resultobj = SWIG_From_float((float)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_new_fcomplex(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct FCOMPLEX *result = 0 ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "new_fcomplex", 0, 0, 0)) SWIG_fail;
  {
    errno = 0;
    result = (struct FCOMPLEX *)calloc(1, sizeof(struct FCOMPLEX));
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p_FCOMPLEX, SWIG_POINTER_NEW |  0 );
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_delete_fcomplex(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct FCOMPLEX *arg1 = (struct FCOMPLEX *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_FCOMPLEX, SWIG_POINTER_DISOWN |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "delete_fcomplex" "', argument " "1"" of type '" "struct FCOMPLEX *""'"); 
  }
  arg1 = (struct FCOMPLEX *)(argp1);
  {
    errno = 0;
    free((char *) arg1);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *fcomplex_swigregister(PyObject *SWIGUNUSEDPARM(self), PyObject *args) {
  PyObject *obj;
  if (!SWIG_Python_UnpackTuple(args, "swigregister", 1, 1, &obj)) return NULL;
  SWIG_TypeNewClientData(SWIGTYPE_p_FCOMPLEX, SWIG_NewClientData(obj));
  return SWIG_Py_Void();
}

SWIGINTERN PyObject *fcomplex_swiginit(PyObject *SWIGUNUSEDPARM(self), PyObject *args) {
  return SWIG_Python_InitShadowInstance(args);
}

SWIGINTERN PyObject *_wrap_read_wisdom(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "read_wisdom", 0, 0, 0)) SWIG_fail;
  read_wisdom();
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_good_factor(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  long long arg1 ;
  long long val1 ;
  int ecode1 = 0 ;
  PyObject *swig_obj[1] ;
  long long result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  ecode1 = SWIG_AsVal_long_SS_long(swig_obj[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "good_factor" "', argument " "1"" of type '" "long long""'");
  } 
  arg1 = (long long)(val1);
  result = (long long)good_factor(arg1);
  resultobj = SWIG_From_long_SS_long((long long)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_fftwcall(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  fcomplex *arg1 = (fcomplex *) 0 ;
  long arg2 ;
  int arg3 ;
  PyArrayObject *array1 = NULL ;
  int i1 = 1 ;
  int val3 ;
  int ecode3 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "fftwcall", 2, 2, swig_obj)) SWIG_fail;
  {
    array1 = obj_to_array_no_conversion(swig_obj[0], NPY_CFLOAT);
    if (!array1 || !require_dimensions(array1,1) || !require_contiguous(array1)
      || !require_native(array1)) SWIG_fail;
    arg1 = (fcomplex*) array_data(array1);
    arg2 = 1;
    for (i1=0; i1 < array_numdims(array1); ++i1) arg2 *= array_size(array1,i1);
  }
  ecode3 = SWIG_AsVal_int(swig_obj[1], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "fftwcall" "', argument " "3"" of type '" "int""'");
  } 
  arg3 = (int)(val3);
  fftwcall(arg1,arg2,arg3);
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_tablesixstepfft(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  fcomplex *arg1 = (fcomplex *) 0 ;
  long arg2 ;
  int arg3 ;
  PyArrayObject *array1 = NULL ;
  int i1 = 1 ;
  int val3 ;
  int ecode3 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "tablesixstepfft", 2, 2, swig_obj)) SWIG_fail;
  {
    array1 = obj_to_array_no_conversion(swig_obj[0], NPY_CFLOAT);
    if (!array1 || !require_dimensions(array1,1) || !require_contiguous(array1)
      || !require_native(array1)) SWIG_fail;
    arg1 = (fcomplex*) array_data(array1);
    arg2 = 1;
    for (i1=0; i1 < array_numdims(array1); ++i1) arg2 *= array_size(array1,i1);
  }
  ecode3 = SWIG_AsVal_int(swig_obj[1], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "tablesixstepfft" "', argument " "3"" of type '" "int""'");
  } 
  arg3 = (int)(val3);
  tablesixstepfft(arg1,arg2,arg3);
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_realfft(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  float *arg1 = (float *) 0 ;
  long arg2 ;
  int arg3 ;
  PyArrayObject *array1 = NULL ;
  int i1 = 1 ;
  int val3 ;
  int ecode3 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "realfft", 2, 2, swig_obj)) SWIG_fail;
  {
    array1 = obj_to_array_no_conversion(swig_obj[0], NPY_FLOAT);
    if (!array1 || !require_dimensions(array1,1) || !require_contiguous(array1)
      || !require_native(array1)) SWIG_fail;
    arg1 = (float*) array_data(array1);
    arg2 = 1;
    for (i1=0; i1 < array_numdims(array1); ++i1) arg2 *= array_size(array1,i1);
  }
  ecode3 = SWIG_AsVal_int(swig_obj[1], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "realfft" "', argument " "3"" of type '" "int""'");
  } 
  arg3 = (int)(val3);
  realfft(arg1,arg2,arg3);
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_ra_s_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  double arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "infodata_ra_s_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_ra_s_set" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "infodata_ra_s_set" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  if (arg1) (arg1)->ra_s = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_ra_s_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_ra_s_get" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  result = (double) ((arg1)->ra_s);
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_dec_s_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  double arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "infodata_dec_s_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_dec_s_set" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "infodata_dec_s_set" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  if (arg1) (arg1)->dec_s = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_dec_s_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_dec_s_get" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  result = (double) ((arg1)->dec_s);
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_N_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  double arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "infodata_N_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_N_set" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "infodata_N_set" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  if (arg1) (arg1)->N = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_N_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_N_get" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  result = (double) ((arg1)->N);
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_dt_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  double arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "infodata_dt_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_dt_set" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "infodata_dt_set" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  if (arg1) (arg1)->dt = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_dt_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_dt_get" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  result = (double) ((arg1)->dt);
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_fov_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  double arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "infodata_fov_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_fov_set" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "infodata_fov_set" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  if (arg1) (arg1)->fov = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_fov_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_fov_get" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  result = (double) ((arg1)->fov);
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_mjd_f_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  double arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "infodata_mjd_f_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_mjd_f_set" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "infodata_mjd_f_set" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  if (arg1) (arg1)->mjd_f = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_mjd_f_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_mjd_f_get" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  result = (double) ((arg1)->mjd_f);
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_dm_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  double arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "infodata_dm_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_dm_set" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "infodata_dm_set" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  if (arg1) (arg1)->dm = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_dm_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_dm_get" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  result = (double) ((arg1)->dm);
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_freq_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  double arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "infodata_freq_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_freq_set" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "infodata_freq_set" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  if (arg1) (arg1)->freq = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_freq_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_freq_get" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  result = (double) ((arg1)->freq);
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_freqband_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  double arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "infodata_freqband_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_freqband_set" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "infodata_freqband_set" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  if (arg1) (arg1)->freqband = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_freqband_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_freqband_get" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  result = (double) ((arg1)->freqband);
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_chan_wid_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  double arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "infodata_chan_wid_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_chan_wid_set" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "infodata_chan_wid_set" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  if (arg1) (arg1)->chan_wid = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_chan_wid_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_chan_wid_get" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  result = (double) ((arg1)->chan_wid);
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_wavelen_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  double arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "infodata_wavelen_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_wavelen_set" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "infodata_wavelen_set" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  if (arg1) (arg1)->wavelen = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_wavelen_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_wavelen_get" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  result = (double) ((arg1)->wavelen);
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_waveband_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  double arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "infodata_waveband_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_waveband_set" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "infodata_waveband_set" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  if (arg1) (arg1)->waveband = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_waveband_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_waveband_get" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  result = (double) ((arg1)->waveband);
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_energy_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  double arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "infodata_energy_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_energy_set" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "infodata_energy_set" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  if (arg1) (arg1)->energy = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_energy_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_energy_get" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  result = (double) ((arg1)->energy);
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_energyband_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  double arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "infodata_energyband_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_energyband_set" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "infodata_energyband_set" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  if (arg1) (arg1)->energyband = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_energyband_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_energyband_get" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  result = (double) ((arg1)->energyband);
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_num_chan_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  int arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  int val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "infodata_num_chan_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_num_chan_set" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  ecode2 = SWIG_AsVal_int(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "infodata_num_chan_set" "', argument " "2"" of type '" "int""'");
  } 
  arg2 = (int)(val2);
  if (arg1) (arg1)->num_chan = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_num_chan_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  int result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_num_chan_get" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  result = (int) ((arg1)->num_chan);
  resultobj = SWIG_From_int((int)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_mjd_i_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  int arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  int val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "infodata_mjd_i_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_mjd_i_set" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  ecode2 = SWIG_AsVal_int(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "infodata_mjd_i_set" "', argument " "2"" of type '" "int""'");
  } 
  arg2 = (int)(val2);
  if (arg1) (arg1)->mjd_i = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_mjd_i_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  int result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_mjd_i_get" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  result = (int) ((arg1)->mjd_i);
  resultobj = SWIG_From_int((int)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_ra_h_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  int arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  int val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "infodata_ra_h_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_ra_h_set" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  ecode2 = SWIG_AsVal_int(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "infodata_ra_h_set" "', argument " "2"" of type '" "int""'");
  } 
  arg2 = (int)(val2);
  if (arg1) (arg1)->ra_h = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_ra_h_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  int result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_ra_h_get" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  result = (int) ((arg1)->ra_h);
  resultobj = SWIG_From_int((int)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_ra_m_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  int arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  int val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "infodata_ra_m_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_ra_m_set" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  ecode2 = SWIG_AsVal_int(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "infodata_ra_m_set" "', argument " "2"" of type '" "int""'");
  } 
  arg2 = (int)(val2);
  if (arg1) (arg1)->ra_m = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_ra_m_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  int result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_ra_m_get" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  result = (int) ((arg1)->ra_m);
  resultobj = SWIG_From_int((int)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_dec_d_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  int arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  int val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "infodata_dec_d_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_dec_d_set" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  ecode2 = SWIG_AsVal_int(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "infodata_dec_d_set" "', argument " "2"" of type '" "int""'");
  } 
  arg2 = (int)(val2);
  if (arg1) (arg1)->dec_d = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_dec_d_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  int result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_dec_d_get" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  result = (int) ((arg1)->dec_d);
  resultobj = SWIG_From_int((int)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_dec_m_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  int arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  int val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "infodata_dec_m_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_dec_m_set" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  ecode2 = SWIG_AsVal_int(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "infodata_dec_m_set" "', argument " "2"" of type '" "int""'");
  } 
  arg2 = (int)(val2);
  if (arg1) (arg1)->dec_m = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_dec_m_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  int result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_dec_m_get" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  result = (int) ((arg1)->dec_m);
  resultobj = SWIG_From_int((int)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_bary_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  int arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  int val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "infodata_bary_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_bary_set" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  ecode2 = SWIG_AsVal_int(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "infodata_bary_set" "', argument " "2"" of type '" "int""'");
  } 
  arg2 = (int)(val2);
  if (arg1) (arg1)->bary = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_bary_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  int result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_bary_get" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  result = (int) ((arg1)->bary);
  resultobj = SWIG_From_int((int)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_numonoff_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  int arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  int val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "infodata_numonoff_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_numonoff_set" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  ecode2 = SWIG_AsVal_int(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "infodata_numonoff_set" "', argument " "2"" of type '" "int""'");
  } 
  arg2 = (int)(val2);
  if (arg1) (arg1)->numonoff = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_numonoff_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  int result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_numonoff_get" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  result = (int) ((arg1)->numonoff);
  resultobj = SWIG_From_int((int)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_notes_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  char *arg2 = (char *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  int res2 ;
  char *buf2 = 0 ;
  int alloc2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "infodata_notes_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_notes_set" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  res2 = SWIG_AsCharPtrAndSize(swig_obj[1], &buf2, NULL, &alloc2);
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "infodata_notes_set" "', argument " "2"" of type '" "char *""'");
  }
  arg2 = (char *)(buf2);
  INFODATA_notes_set(arg1,arg2);
  resultobj = SWIG_Py_Void();
  if (alloc2 == SWIG_NEWOBJ) free((char*)buf2);
  return resultobj;
fail:
  if (alloc2 == SWIG_NEWOBJ) free((char*)buf2);
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_notes_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  char *result = 0 ;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_notes_get" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  result = (char *)INFODATA_notes_get(arg1);
  resultobj = SWIG_FromCharPtr((const char *)result);
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_name_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  char *arg2 = (char *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  int res2 ;
  char *buf2 = 0 ;
  int alloc2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "infodata_name_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_name_set" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  res2 = SWIG_AsCharPtrAndSize(swig_obj[1], &buf2, NULL, &alloc2);
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "infodata_name_set" "', argument " "2"" of type '" "char *""'");
  }
  arg2 = (char *)(buf2);
  INFODATA_name_set(arg1,arg2);
  resultobj = SWIG_Py_Void();
  if (alloc2 == SWIG_NEWOBJ) free((char*)buf2);
  return resultobj;
fail:
  if (alloc2 == SWIG_NEWOBJ) free((char*)buf2);
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_name_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  char *result = 0 ;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_name_get" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  result = (char *)INFODATA_name_get(arg1);
  resultobj = SWIG_FromCharPtr((const char *)result);
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_object_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  char *arg2 = (char *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  int res2 ;
  char *buf2 = 0 ;
  int alloc2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "infodata_object_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_object_set" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  res2 = SWIG_AsCharPtrAndSize(swig_obj[1], &buf2, NULL, &alloc2);
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "infodata_object_set" "', argument " "2"" of type '" "char *""'");
  }
  arg2 = (char *)(buf2);
  INFODATA_object_set(arg1,arg2);
  resultobj = SWIG_Py_Void();
  if (alloc2 == SWIG_NEWOBJ) free((char*)buf2);
  return resultobj;
fail:
  if (alloc2 == SWIG_NEWOBJ) free((char*)buf2);
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_object_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  char *result = 0 ;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_object_get" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  result = (char *)INFODATA_object_get(arg1);
  resultobj = SWIG_FromCharPtr((const char *)result);
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_instrument_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  char *arg2 = (char *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  int res2 ;
  char *buf2 = 0 ;
  int alloc2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "infodata_instrument_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_instrument_set" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  res2 = SWIG_AsCharPtrAndSize(swig_obj[1], &buf2, NULL, &alloc2);
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "infodata_instrument_set" "', argument " "2"" of type '" "char *""'");
  }
  arg2 = (char *)(buf2);
  INFODATA_instrument_set(arg1,arg2);
  resultobj = SWIG_Py_Void();
  if (alloc2 == SWIG_NEWOBJ) free((char*)buf2);
  return resultobj;
fail:
  if (alloc2 == SWIG_NEWOBJ) free((char*)buf2);
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_instrument_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  char *result = 0 ;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_instrument_get" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  result = (char *)INFODATA_instrument_get(arg1);
  resultobj = SWIG_FromCharPtr((const char *)result);
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_observer_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  char *arg2 = (char *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  int res2 ;
  char *buf2 = 0 ;
  int alloc2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "infodata_observer_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_observer_set" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  res2 = SWIG_AsCharPtrAndSize(swig_obj[1], &buf2, NULL, &alloc2);
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "infodata_observer_set" "', argument " "2"" of type '" "char *""'");
  }
  arg2 = (char *)(buf2);
  INFODATA_observer_set(arg1,arg2);
  resultobj = SWIG_Py_Void();
  if (alloc2 == SWIG_NEWOBJ) free((char*)buf2);
  return resultobj;
fail:
  if (alloc2 == SWIG_NEWOBJ) free((char*)buf2);
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_observer_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  char *result = 0 ;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_observer_get" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  result = (char *)INFODATA_observer_get(arg1);
  resultobj = SWIG_FromCharPtr((const char *)result);
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_analyzer_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  char *arg2 = (char *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  int res2 ;
  char *buf2 = 0 ;
  int alloc2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "infodata_analyzer_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_analyzer_set" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  res2 = SWIG_AsCharPtrAndSize(swig_obj[1], &buf2, NULL, &alloc2);
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "infodata_analyzer_set" "', argument " "2"" of type '" "char *""'");
  }
  arg2 = (char *)(buf2);
  INFODATA_analyzer_set(arg1,arg2);
  resultobj = SWIG_Py_Void();
  if (alloc2 == SWIG_NEWOBJ) free((char*)buf2);
  return resultobj;
fail:
  if (alloc2 == SWIG_NEWOBJ) free((char*)buf2);
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_analyzer_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  char *result = 0 ;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_analyzer_get" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  result = (char *)INFODATA_analyzer_get(arg1);
  resultobj = SWIG_FromCharPtr((const char *)result);
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_telescope_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  char *arg2 = (char *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  int res2 ;
  char *buf2 = 0 ;
  int alloc2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "infodata_telescope_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_telescope_set" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  res2 = SWIG_AsCharPtrAndSize(swig_obj[1], &buf2, NULL, &alloc2);
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "infodata_telescope_set" "', argument " "2"" of type '" "char *""'");
  }
  arg2 = (char *)(buf2);
  INFODATA_telescope_set(arg1,arg2);
  resultobj = SWIG_Py_Void();
  if (alloc2 == SWIG_NEWOBJ) free((char*)buf2);
  return resultobj;
fail:
  if (alloc2 == SWIG_NEWOBJ) free((char*)buf2);
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_telescope_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  char *result = 0 ;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_telescope_get" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  result = (char *)INFODATA_telescope_get(arg1);
  resultobj = SWIG_FromCharPtr((const char *)result);
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_band_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  char *arg2 = (char *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  int res2 ;
  char *buf2 = 0 ;
  int alloc2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "infodata_band_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_band_set" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  res2 = SWIG_AsCharPtrAndSize(swig_obj[1], &buf2, NULL, &alloc2);
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "infodata_band_set" "', argument " "2"" of type '" "char *""'");
  }
  arg2 = (char *)(buf2);
  INFODATA_band_set(arg1,arg2);
  resultobj = SWIG_Py_Void();
  if (alloc2 == SWIG_NEWOBJ) free((char*)buf2);
  return resultobj;
fail:
  if (alloc2 == SWIG_NEWOBJ) free((char*)buf2);
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_band_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  char *result = 0 ;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_band_get" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  result = (char *)INFODATA_band_get(arg1);
  resultobj = SWIG_FromCharPtr((const char *)result);
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_filt_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  char *arg2 = (char *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  int res2 ;
  char *buf2 = 0 ;
  int alloc2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "infodata_filt_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_filt_set" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  res2 = SWIG_AsCharPtrAndSize(swig_obj[1], &buf2, NULL, &alloc2);
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "infodata_filt_set" "', argument " "2"" of type '" "char *""'");
  }
  arg2 = (char *)(buf2);
  INFODATA_filt_set(arg1,arg2);
  resultobj = SWIG_Py_Void();
  if (alloc2 == SWIG_NEWOBJ) free((char*)buf2);
  return resultobj;
fail:
  if (alloc2 == SWIG_NEWOBJ) free((char*)buf2);
  return NULL;
}


SWIGINTERN PyObject *_wrap_infodata_filt_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  char *result = 0 ;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "infodata_filt_get" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  result = (char *)INFODATA_filt_get(arg1);
  resultobj = SWIG_FromCharPtr((const char *)result);
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_new_infodata(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *result = 0 ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "new_infodata", 0, 0, 0)) SWIG_fail;
  {
    errno = 0;
    result = (struct INFODATA *)calloc(1, sizeof(struct INFODATA));
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p_INFODATA, SWIG_POINTER_NEW |  0 );
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_delete_infodata(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct INFODATA *arg1 = (struct INFODATA *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, SWIG_POINTER_DISOWN |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "delete_infodata" "', argument " "1"" of type '" "struct INFODATA *""'"); 
  }
  arg1 = (struct INFODATA *)(argp1);
  {
    errno = 0;
    free((char *) arg1);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *infodata_swigregister(PyObject *SWIGUNUSEDPARM(self), PyObject *args) {
  PyObject *obj;
  if (!SWIG_Python_UnpackTuple(args, "swigregister", 1, 1, &obj)) return NULL;
  SWIG_TypeNewClientData(SWIGTYPE_p_INFODATA, SWIG_NewClientData(obj));
  return SWIG_Py_Void();
}

SWIGINTERN PyObject *infodata_swiginit(PyObject *SWIGUNUSEDPARM(self), PyObject *args) {
  return SWIG_Python_InitShadowInstance(args);
}

SWIGINTERN PyObject *_wrap_readinf(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  infodata *arg1 = (infodata *) 0 ;
  char *arg2 = (char *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  int res2 ;
  char *buf2 = 0 ;
  int alloc2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "readinf", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "readinf" "', argument " "1"" of type '" "infodata *""'"); 
  }
  arg1 = (infodata *)(argp1);
  res2 = SWIG_AsCharPtrAndSize(swig_obj[1], &buf2, NULL, &alloc2);
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "readinf" "', argument " "2"" of type '" "char *""'");
  }
  arg2 = (char *)(buf2);
  readinf(arg1,arg2);
  resultobj = SWIG_Py_Void();
  if (alloc2 == SWIG_NEWOBJ) free((char*)buf2);
  return resultobj;
fail:
  if (alloc2 == SWIG_NEWOBJ) free((char*)buf2);
  return NULL;
}


SWIGINTERN PyObject *_wrap_writeinf(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  infodata *arg1 = (infodata *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_INFODATA, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "writeinf" "', argument " "1"" of type '" "infodata *""'"); 
  }
  arg1 = (infodata *)(argp1);
  writeinf(arg1);
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_orbitparams_p_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct orbitparams *arg1 = (struct orbitparams *) 0 ;
  double arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "orbitparams_p_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_orbitparams, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "orbitparams_p_set" "', argument " "1"" of type '" "struct orbitparams *""'"); 
  }
  arg1 = (struct orbitparams *)(argp1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "orbitparams_p_set" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  if (arg1) (arg1)->p = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_orbitparams_p_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct orbitparams *arg1 = (struct orbitparams *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_orbitparams, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "orbitparams_p_get" "', argument " "1"" of type '" "struct orbitparams *""'"); 
  }
  arg1 = (struct orbitparams *)(argp1);
  result = (double) ((arg1)->p);
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_orbitparams_e_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct orbitparams *arg1 = (struct orbitparams *) 0 ;
  double arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "orbitparams_e_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_orbitparams, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "orbitparams_e_set" "', argument " "1"" of type '" "struct orbitparams *""'"); 
  }
  arg1 = (struct orbitparams *)(argp1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "orbitparams_e_set" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  if (arg1) (arg1)->e = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_orbitparams_e_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct orbitparams *arg1 = (struct orbitparams *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_orbitparams, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "orbitparams_e_get" "', argument " "1"" of type '" "struct orbitparams *""'"); 
  }
  arg1 = (struct orbitparams *)(argp1);
  result = (double) ((arg1)->e);
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_orbitparams_x_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct orbitparams *arg1 = (struct orbitparams *) 0 ;
  double arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "orbitparams_x_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_orbitparams, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "orbitparams_x_set" "', argument " "1"" of type '" "struct orbitparams *""'"); 
  }
  arg1 = (struct orbitparams *)(argp1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "orbitparams_x_set" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  if (arg1) (arg1)->x = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_orbitparams_x_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct orbitparams *arg1 = (struct orbitparams *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_orbitparams, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "orbitparams_x_get" "', argument " "1"" of type '" "struct orbitparams *""'"); 
  }
  arg1 = (struct orbitparams *)(argp1);
  result = (double) ((arg1)->x);
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_orbitparams_w_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct orbitparams *arg1 = (struct orbitparams *) 0 ;
  double arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "orbitparams_w_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_orbitparams, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "orbitparams_w_set" "', argument " "1"" of type '" "struct orbitparams *""'"); 
  }
  arg1 = (struct orbitparams *)(argp1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "orbitparams_w_set" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  if (arg1) (arg1)->w = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_orbitparams_w_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct orbitparams *arg1 = (struct orbitparams *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_orbitparams, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "orbitparams_w_get" "', argument " "1"" of type '" "struct orbitparams *""'"); 
  }
  arg1 = (struct orbitparams *)(argp1);
  result = (double) ((arg1)->w);
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_orbitparams_t_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct orbitparams *arg1 = (struct orbitparams *) 0 ;
  double arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "orbitparams_t_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_orbitparams, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "orbitparams_t_set" "', argument " "1"" of type '" "struct orbitparams *""'"); 
  }
  arg1 = (struct orbitparams *)(argp1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "orbitparams_t_set" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  if (arg1) (arg1)->t = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_orbitparams_t_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct orbitparams *arg1 = (struct orbitparams *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_orbitparams, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "orbitparams_t_get" "', argument " "1"" of type '" "struct orbitparams *""'"); 
  }
  arg1 = (struct orbitparams *)(argp1);
  result = (double) ((arg1)->t);
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_orbitparams_pd_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct orbitparams *arg1 = (struct orbitparams *) 0 ;
  double arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "orbitparams_pd_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_orbitparams, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "orbitparams_pd_set" "', argument " "1"" of type '" "struct orbitparams *""'"); 
  }
  arg1 = (struct orbitparams *)(argp1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "orbitparams_pd_set" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  if (arg1) (arg1)->pd = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_orbitparams_pd_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct orbitparams *arg1 = (struct orbitparams *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_orbitparams, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "orbitparams_pd_get" "', argument " "1"" of type '" "struct orbitparams *""'"); 
  }
  arg1 = (struct orbitparams *)(argp1);
  result = (double) ((arg1)->pd);
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_orbitparams_wd_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct orbitparams *arg1 = (struct orbitparams *) 0 ;
  double arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "orbitparams_wd_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_orbitparams, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "orbitparams_wd_set" "', argument " "1"" of type '" "struct orbitparams *""'"); 
  }
  arg1 = (struct orbitparams *)(argp1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "orbitparams_wd_set" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  if (arg1) (arg1)->wd = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_orbitparams_wd_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct orbitparams *arg1 = (struct orbitparams *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_orbitparams, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "orbitparams_wd_get" "', argument " "1"" of type '" "struct orbitparams *""'"); 
  }
  arg1 = (struct orbitparams *)(argp1);
  result = (double) ((arg1)->wd);
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_new_orbitparams(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct orbitparams *result = 0 ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "new_orbitparams", 0, 0, 0)) SWIG_fail;
  {
    errno = 0;
    result = (struct orbitparams *)calloc(1, sizeof(struct orbitparams));
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p_orbitparams, SWIG_POINTER_NEW |  0 );
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_delete_orbitparams(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct orbitparams *arg1 = (struct orbitparams *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_orbitparams, SWIG_POINTER_DISOWN |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "delete_orbitparams" "', argument " "1"" of type '" "struct orbitparams *""'"); 
  }
  arg1 = (struct orbitparams *)(argp1);
  {
    errno = 0;
    free((char *) arg1);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *orbitparams_swigregister(PyObject *SWIGUNUSEDPARM(self), PyObject *args) {
  PyObject *obj;
  if (!SWIG_Python_UnpackTuple(args, "swigregister", 1, 1, &obj)) return NULL;
  SWIG_TypeNewClientData(SWIGTYPE_p_orbitparams, SWIG_NewClientData(obj));
  return SWIG_Py_Void();
}

SWIGINTERN PyObject *orbitparams_swiginit(PyObject *SWIGUNUSEDPARM(self), PyObject *args) {
  return SWIG_Python_InitShadowInstance(args);
}

SWIGINTERN PyObject *_wrap_psrparams_jname_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct PSRPARAMS *arg1 = (struct PSRPARAMS *) 0 ;
  char *arg2 = (char *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  int res2 ;
  char *buf2 = 0 ;
  int alloc2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "psrparams_jname_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_PSRPARAMS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "psrparams_jname_set" "', argument " "1"" of type '" "struct PSRPARAMS *""'"); 
  }
  arg1 = (struct PSRPARAMS *)(argp1);
  res2 = SWIG_AsCharPtrAndSize(swig_obj[1], &buf2, NULL, &alloc2);
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "psrparams_jname_set" "', argument " "2"" of type '" "char *""'");
  }
  arg2 = (char *)(buf2);
  PSRPARAMS_jname_set(arg1,arg2);
  resultobj = SWIG_Py_Void();
  if (alloc2 == SWIG_NEWOBJ) free((char*)buf2);
  return resultobj;
fail:
  if (alloc2 == SWIG_NEWOBJ) free((char*)buf2);
  return NULL;
}


SWIGINTERN PyObject *_wrap_psrparams_jname_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct PSRPARAMS *arg1 = (struct PSRPARAMS *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  char *result = 0 ;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_PSRPARAMS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "psrparams_jname_get" "', argument " "1"" of type '" "struct PSRPARAMS *""'"); 
  }
  arg1 = (struct PSRPARAMS *)(argp1);
  result = (char *)PSRPARAMS_jname_get(arg1);
  resultobj = SWIG_FromCharPtr((const char *)result);
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_psrparams_bname_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct PSRPARAMS *arg1 = (struct PSRPARAMS *) 0 ;
  char *arg2 = (char *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  int res2 ;
  char *buf2 = 0 ;
  int alloc2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "psrparams_bname_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_PSRPARAMS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "psrparams_bname_set" "', argument " "1"" of type '" "struct PSRPARAMS *""'"); 
  }
  arg1 = (struct PSRPARAMS *)(argp1);
  res2 = SWIG_AsCharPtrAndSize(swig_obj[1], &buf2, NULL, &alloc2);
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "psrparams_bname_set" "', argument " "2"" of type '" "char *""'");
  }
  arg2 = (char *)(buf2);
  PSRPARAMS_bname_set(arg1,arg2);
  resultobj = SWIG_Py_Void();
  if (alloc2 == SWIG_NEWOBJ) free((char*)buf2);
  return resultobj;
fail:
  if (alloc2 == SWIG_NEWOBJ) free((char*)buf2);
  return NULL;
}


SWIGINTERN PyObject *_wrap_psrparams_bname_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct PSRPARAMS *arg1 = (struct PSRPARAMS *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  char *result = 0 ;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_PSRPARAMS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "psrparams_bname_get" "', argument " "1"" of type '" "struct PSRPARAMS *""'"); 
  }
  arg1 = (struct PSRPARAMS *)(argp1);
  result = (char *)PSRPARAMS_bname_get(arg1);
  resultobj = SWIG_FromCharPtr((const char *)result);
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_psrparams_alias_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct PSRPARAMS *arg1 = (struct PSRPARAMS *) 0 ;
  char *arg2 = (char *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  int res2 ;
  char *buf2 = 0 ;
  int alloc2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "psrparams_alias_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_PSRPARAMS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "psrparams_alias_set" "', argument " "1"" of type '" "struct PSRPARAMS *""'"); 
  }
  arg1 = (struct PSRPARAMS *)(argp1);
  res2 = SWIG_AsCharPtrAndSize(swig_obj[1], &buf2, NULL, &alloc2);
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "psrparams_alias_set" "', argument " "2"" of type '" "char *""'");
  }
  arg2 = (char *)(buf2);
  PSRPARAMS_alias_set(arg1,arg2);
  resultobj = SWIG_Py_Void();
  if (alloc2 == SWIG_NEWOBJ) free((char*)buf2);
  return resultobj;
fail:
  if (alloc2 == SWIG_NEWOBJ) free((char*)buf2);
  return NULL;
}


SWIGINTERN PyObject *_wrap_psrparams_alias_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct PSRPARAMS *arg1 = (struct PSRPARAMS *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  char *result = 0 ;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_PSRPARAMS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "psrparams_alias_get" "', argument " "1"" of type '" "struct PSRPARAMS *""'"); 
  }
  arg1 = (struct PSRPARAMS *)(argp1);
  result = (char *)PSRPARAMS_alias_get(arg1);
  resultobj = SWIG_FromCharPtr((const char *)result);
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_psrparams_ra2000_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct PSRPARAMS *arg1 = (struct PSRPARAMS *) 0 ;
  double arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "psrparams_ra2000_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_PSRPARAMS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "psrparams_ra2000_set" "', argument " "1"" of type '" "struct PSRPARAMS *""'"); 
  }
  arg1 = (struct PSRPARAMS *)(argp1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "psrparams_ra2000_set" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  if (arg1) (arg1)->ra2000 = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_psrparams_ra2000_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct PSRPARAMS *arg1 = (struct PSRPARAMS *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_PSRPARAMS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "psrparams_ra2000_get" "', argument " "1"" of type '" "struct PSRPARAMS *""'"); 
  }
  arg1 = (struct PSRPARAMS *)(argp1);
  result = (double) ((arg1)->ra2000);
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_psrparams_dec2000_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct PSRPARAMS *arg1 = (struct PSRPARAMS *) 0 ;
  double arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "psrparams_dec2000_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_PSRPARAMS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "psrparams_dec2000_set" "', argument " "1"" of type '" "struct PSRPARAMS *""'"); 
  }
  arg1 = (struct PSRPARAMS *)(argp1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "psrparams_dec2000_set" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  if (arg1) (arg1)->dec2000 = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_psrparams_dec2000_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct PSRPARAMS *arg1 = (struct PSRPARAMS *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_PSRPARAMS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "psrparams_dec2000_get" "', argument " "1"" of type '" "struct PSRPARAMS *""'"); 
  }
  arg1 = (struct PSRPARAMS *)(argp1);
  result = (double) ((arg1)->dec2000);
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_psrparams_dm_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct PSRPARAMS *arg1 = (struct PSRPARAMS *) 0 ;
  double arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "psrparams_dm_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_PSRPARAMS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "psrparams_dm_set" "', argument " "1"" of type '" "struct PSRPARAMS *""'"); 
  }
  arg1 = (struct PSRPARAMS *)(argp1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "psrparams_dm_set" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  if (arg1) (arg1)->dm = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_psrparams_dm_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct PSRPARAMS *arg1 = (struct PSRPARAMS *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_PSRPARAMS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "psrparams_dm_get" "', argument " "1"" of type '" "struct PSRPARAMS *""'"); 
  }
  arg1 = (struct PSRPARAMS *)(argp1);
  result = (double) ((arg1)->dm);
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_psrparams_timepoch_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct PSRPARAMS *arg1 = (struct PSRPARAMS *) 0 ;
  double arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "psrparams_timepoch_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_PSRPARAMS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "psrparams_timepoch_set" "', argument " "1"" of type '" "struct PSRPARAMS *""'"); 
  }
  arg1 = (struct PSRPARAMS *)(argp1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "psrparams_timepoch_set" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  if (arg1) (arg1)->timepoch = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_psrparams_timepoch_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct PSRPARAMS *arg1 = (struct PSRPARAMS *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_PSRPARAMS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "psrparams_timepoch_get" "', argument " "1"" of type '" "struct PSRPARAMS *""'"); 
  }
  arg1 = (struct PSRPARAMS *)(argp1);
  result = (double) ((arg1)->timepoch);
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_psrparams_p_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct PSRPARAMS *arg1 = (struct PSRPARAMS *) 0 ;
  double arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "psrparams_p_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_PSRPARAMS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "psrparams_p_set" "', argument " "1"" of type '" "struct PSRPARAMS *""'"); 
  }
  arg1 = (struct PSRPARAMS *)(argp1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "psrparams_p_set" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  if (arg1) (arg1)->p = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_psrparams_p_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct PSRPARAMS *arg1 = (struct PSRPARAMS *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_PSRPARAMS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "psrparams_p_get" "', argument " "1"" of type '" "struct PSRPARAMS *""'"); 
  }
  arg1 = (struct PSRPARAMS *)(argp1);
  result = (double) ((arg1)->p);
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_psrparams_pd_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct PSRPARAMS *arg1 = (struct PSRPARAMS *) 0 ;
  double arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "psrparams_pd_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_PSRPARAMS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "psrparams_pd_set" "', argument " "1"" of type '" "struct PSRPARAMS *""'"); 
  }
  arg1 = (struct PSRPARAMS *)(argp1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "psrparams_pd_set" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  if (arg1) (arg1)->pd = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_psrparams_pd_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct PSRPARAMS *arg1 = (struct PSRPARAMS *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_PSRPARAMS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "psrparams_pd_get" "', argument " "1"" of type '" "struct PSRPARAMS *""'"); 
  }
  arg1 = (struct PSRPARAMS *)(argp1);
  result = (double) ((arg1)->pd);
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_psrparams_pdd_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct PSRPARAMS *arg1 = (struct PSRPARAMS *) 0 ;
  double arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "psrparams_pdd_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_PSRPARAMS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "psrparams_pdd_set" "', argument " "1"" of type '" "struct PSRPARAMS *""'"); 
  }
  arg1 = (struct PSRPARAMS *)(argp1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "psrparams_pdd_set" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  if (arg1) (arg1)->pdd = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_psrparams_pdd_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct PSRPARAMS *arg1 = (struct PSRPARAMS *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_PSRPARAMS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "psrparams_pdd_get" "', argument " "1"" of type '" "struct PSRPARAMS *""'"); 
  }
  arg1 = (struct PSRPARAMS *)(argp1);
  result = (double) ((arg1)->pdd);
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_psrparams_f_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct PSRPARAMS *arg1 = (struct PSRPARAMS *) 0 ;
  double arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "psrparams_f_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_PSRPARAMS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "psrparams_f_set" "', argument " "1"" of type '" "struct PSRPARAMS *""'"); 
  }
  arg1 = (struct PSRPARAMS *)(argp1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "psrparams_f_set" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  if (arg1) (arg1)->f = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_psrparams_f_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct PSRPARAMS *arg1 = (struct PSRPARAMS *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_PSRPARAMS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "psrparams_f_get" "', argument " "1"" of type '" "struct PSRPARAMS *""'"); 
  }
  arg1 = (struct PSRPARAMS *)(argp1);
  result = (double) ((arg1)->f);
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_psrparams_fd_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct PSRPARAMS *arg1 = (struct PSRPARAMS *) 0 ;
  double arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "psrparams_fd_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_PSRPARAMS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "psrparams_fd_set" "', argument " "1"" of type '" "struct PSRPARAMS *""'"); 
  }
  arg1 = (struct PSRPARAMS *)(argp1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "psrparams_fd_set" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  if (arg1) (arg1)->fd = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_psrparams_fd_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct PSRPARAMS *arg1 = (struct PSRPARAMS *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_PSRPARAMS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "psrparams_fd_get" "', argument " "1"" of type '" "struct PSRPARAMS *""'"); 
  }
  arg1 = (struct PSRPARAMS *)(argp1);
  result = (double) ((arg1)->fd);
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_psrparams_fdd_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct PSRPARAMS *arg1 = (struct PSRPARAMS *) 0 ;
  double arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "psrparams_fdd_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_PSRPARAMS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "psrparams_fdd_set" "', argument " "1"" of type '" "struct PSRPARAMS *""'"); 
  }
  arg1 = (struct PSRPARAMS *)(argp1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "psrparams_fdd_set" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  if (arg1) (arg1)->fdd = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_psrparams_fdd_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct PSRPARAMS *arg1 = (struct PSRPARAMS *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_PSRPARAMS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "psrparams_fdd_get" "', argument " "1"" of type '" "struct PSRPARAMS *""'"); 
  }
  arg1 = (struct PSRPARAMS *)(argp1);
  result = (double) ((arg1)->fdd);
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_psrparams_orb_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct PSRPARAMS *arg1 = (struct PSRPARAMS *) 0 ;
  orbitparams *arg2 = (orbitparams *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  void *argp2 = 0 ;
  int res2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "psrparams_orb_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_PSRPARAMS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "psrparams_orb_set" "', argument " "1"" of type '" "struct PSRPARAMS *""'"); 
  }
  arg1 = (struct PSRPARAMS *)(argp1);
  res2 = SWIG_ConvertPtr(swig_obj[1], &argp2,SWIGTYPE_p_orbitparams, 0 |  0 );
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "psrparams_orb_set" "', argument " "2"" of type '" "orbitparams *""'"); 
  }
  arg2 = (orbitparams *)(argp2);
  if (arg1) (arg1)->orb = *arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_psrparams_orb_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct PSRPARAMS *arg1 = (struct PSRPARAMS *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  orbitparams *result = 0 ;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_PSRPARAMS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "psrparams_orb_get" "', argument " "1"" of type '" "struct PSRPARAMS *""'"); 
  }
  arg1 = (struct PSRPARAMS *)(argp1);
  result = (orbitparams *)& ((arg1)->orb);
  resultobj = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p_orbitparams, 0 |  0 );
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_new_psrparams(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct PSRPARAMS *result = 0 ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "new_psrparams", 0, 0, 0)) SWIG_fail;
  {
    errno = 0;
    result = (struct PSRPARAMS *)calloc(1, sizeof(struct PSRPARAMS));
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p_PSRPARAMS, SWIG_POINTER_NEW |  0 );
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_delete_psrparams(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct PSRPARAMS *arg1 = (struct PSRPARAMS *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_PSRPARAMS, SWIG_POINTER_DISOWN |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "delete_psrparams" "', argument " "1"" of type '" "struct PSRPARAMS *""'"); 
  }
  arg1 = (struct PSRPARAMS *)(argp1);
  {
    errno = 0;
    free((char *) arg1);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *psrparams_swigregister(PyObject *SWIGUNUSEDPARM(self), PyObject *args) {
  PyObject *obj;
  if (!SWIG_Python_UnpackTuple(args, "swigregister", 1, 1, &obj)) return NULL;
  SWIG_TypeNewClientData(SWIGTYPE_p_PSRPARAMS, SWIG_NewClientData(obj));
  return SWIG_Py_Void();
}

SWIGINTERN PyObject *psrparams_swiginit(PyObject *SWIGUNUSEDPARM(self), PyObject *args) {
  return SWIG_Python_InitShadowInstance(args);
}

SWIGINTERN PyObject *_wrap_get_psr_at_epoch(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  char *arg1 = (char *) 0 ;
  double arg2 ;
  psrparams *arg3 = (psrparams *) 0 ;
  int res1 ;
  char *buf1 = 0 ;
  int alloc1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  void *argp3 = 0 ;
  int res3 = 0 ;
  PyObject *swig_obj[3] ;
  int result;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "get_psr_at_epoch", 3, 3, swig_obj)) SWIG_fail;
  res1 = SWIG_AsCharPtrAndSize(swig_obj[0], &buf1, NULL, &alloc1);
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "get_psr_at_epoch" "', argument " "1"" of type '" "char *""'");
  }
  arg1 = (char *)(buf1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "get_psr_at_epoch" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  res3 = SWIG_ConvertPtr(swig_obj[2], &argp3,SWIGTYPE_p_PSRPARAMS, 0 |  0 );
  if (!SWIG_IsOK(res3)) {
    SWIG_exception_fail(SWIG_ArgError(res3), "in method '" "get_psr_at_epoch" "', argument " "3"" of type '" "psrparams *""'"); 
  }
  arg3 = (psrparams *)(argp3);
  result = (int)get_psr_at_epoch(arg1,arg2,arg3);
  resultobj = SWIG_From_int((int)(result));
  if (alloc1 == SWIG_NEWOBJ) free((char*)buf1);
  return resultobj;
fail:
  if (alloc1 == SWIG_NEWOBJ) free((char*)buf1);
  return NULL;
}


SWIGINTERN PyObject *_wrap_get_psr_from_parfile(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  char *arg1 = (char *) 0 ;
  double arg2 ;
  psrparams *arg3 = (psrparams *) 0 ;
  int res1 ;
  char *buf1 = 0 ;
  int alloc1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  void *argp3 = 0 ;
  int res3 = 0 ;
  PyObject *swig_obj[3] ;
  int result;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "get_psr_from_parfile", 3, 3, swig_obj)) SWIG_fail;
  res1 = SWIG_AsCharPtrAndSize(swig_obj[0], &buf1, NULL, &alloc1);
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "get_psr_from_parfile" "', argument " "1"" of type '" "char *""'");
  }
  arg1 = (char *)(buf1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "get_psr_from_parfile" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  res3 = SWIG_ConvertPtr(swig_obj[2], &argp3,SWIGTYPE_p_PSRPARAMS, 0 |  0 );
  if (!SWIG_IsOK(res3)) {
    SWIG_exception_fail(SWIG_ArgError(res3), "in method '" "get_psr_from_parfile" "', argument " "3"" of type '" "psrparams *""'"); 
  }
  arg3 = (psrparams *)(argp3);
  result = (int)get_psr_from_parfile(arg1,arg2,arg3);
  resultobj = SWIG_From_int((int)(result));
  if (alloc1 == SWIG_NEWOBJ) free((char*)buf1);
  return resultobj;
fail:
  if (alloc1 == SWIG_NEWOBJ) free((char*)buf1);
  return NULL;
}


SWIGINTERN PyObject *_wrap_mjd_to_datestr(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  double arg1 ;
  char *arg2 = (char *) 0 ;
  double val1 ;
  int ecode1 = 0 ;
  int res2 ;
  char *buf2 = 0 ;
  int alloc2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "mjd_to_datestr", 2, 2, swig_obj)) SWIG_fail;
  ecode1 = SWIG_AsVal_double(swig_obj[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "mjd_to_datestr" "', argument " "1"" of type '" "double""'");
  } 
  arg1 = (double)(val1);
  res2 = SWIG_AsCharPtrAndSize(swig_obj[1], &buf2, NULL, &alloc2);
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "mjd_to_datestr" "', argument " "2"" of type '" "char *""'");
  }
  arg2 = (char *)(buf2);
  mjd_to_datestr(arg1,arg2);
  resultobj = SWIG_Py_Void();
  if (alloc2 == SWIG_NEWOBJ) free((char*)buf2);
  return resultobj;
fail:
  if (alloc2 == SWIG_NEWOBJ) free((char*)buf2);
  return NULL;
}


SWIGINTERN PyObject *_wrap_fresnl(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  double arg1 ;
  double *arg2 = (double *) 0 ;
  double *arg3 = (double *) 0 ;
  double val1 ;
  int ecode1 = 0 ;
  double temp2 ;
  int res2 = SWIG_TMPOBJ ;
  double temp3 ;
  int res3 = SWIG_TMPOBJ ;
  PyObject *swig_obj[1] ;
  int result;
  
  arg2 = &temp2;
  arg3 = &temp3;
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  ecode1 = SWIG_AsVal_double(swig_obj[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "fresnl" "', argument " "1"" of type '" "double""'");
  } 
  arg1 = (double)(val1);
  result = (int)fresnl(arg1,arg2,arg3);
  resultobj = SWIG_From_int((int)(result));
  if (SWIG_IsTmpObj(res2)) {
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_From_double((*arg2)));
  } else {
    int new_flags = SWIG_IsNewObj(res2) ? (SWIG_POINTER_OWN |  0 ) :  0 ;
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_NewPointerObj((void*)(arg2), SWIGTYPE_p_double, new_flags));
  }
  if (SWIG_IsTmpObj(res3)) {
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_From_double((*arg3)));
  } else {
    int new_flags = SWIG_IsNewObj(res3) ? (SWIG_POINTER_OWN |  0 ) :  0 ;
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_NewPointerObj((void*)(arg3), SWIGTYPE_p_double, new_flags));
  }
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_rderivs_pow_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct RDERIVS *arg1 = (struct RDERIVS *) 0 ;
  double arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "rderivs_pow_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_RDERIVS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "rderivs_pow_set" "', argument " "1"" of type '" "struct RDERIVS *""'"); 
  }
  arg1 = (struct RDERIVS *)(argp1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "rderivs_pow_set" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  if (arg1) (arg1)->pow = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_rderivs_pow_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct RDERIVS *arg1 = (struct RDERIVS *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_RDERIVS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "rderivs_pow_get" "', argument " "1"" of type '" "struct RDERIVS *""'"); 
  }
  arg1 = (struct RDERIVS *)(argp1);
  result = (double) ((arg1)->pow);
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_rderivs_phs_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct RDERIVS *arg1 = (struct RDERIVS *) 0 ;
  double arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "rderivs_phs_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_RDERIVS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "rderivs_phs_set" "', argument " "1"" of type '" "struct RDERIVS *""'"); 
  }
  arg1 = (struct RDERIVS *)(argp1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "rderivs_phs_set" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  if (arg1) (arg1)->phs = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_rderivs_phs_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct RDERIVS *arg1 = (struct RDERIVS *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_RDERIVS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "rderivs_phs_get" "', argument " "1"" of type '" "struct RDERIVS *""'"); 
  }
  arg1 = (struct RDERIVS *)(argp1);
  result = (double) ((arg1)->phs);
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_rderivs_dpow_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct RDERIVS *arg1 = (struct RDERIVS *) 0 ;
  double arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "rderivs_dpow_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_RDERIVS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "rderivs_dpow_set" "', argument " "1"" of type '" "struct RDERIVS *""'"); 
  }
  arg1 = (struct RDERIVS *)(argp1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "rderivs_dpow_set" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  if (arg1) (arg1)->dpow = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_rderivs_dpow_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct RDERIVS *arg1 = (struct RDERIVS *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_RDERIVS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "rderivs_dpow_get" "', argument " "1"" of type '" "struct RDERIVS *""'"); 
  }
  arg1 = (struct RDERIVS *)(argp1);
  result = (double) ((arg1)->dpow);
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_rderivs_dphs_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct RDERIVS *arg1 = (struct RDERIVS *) 0 ;
  double arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "rderivs_dphs_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_RDERIVS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "rderivs_dphs_set" "', argument " "1"" of type '" "struct RDERIVS *""'"); 
  }
  arg1 = (struct RDERIVS *)(argp1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "rderivs_dphs_set" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  if (arg1) (arg1)->dphs = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_rderivs_dphs_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct RDERIVS *arg1 = (struct RDERIVS *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_RDERIVS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "rderivs_dphs_get" "', argument " "1"" of type '" "struct RDERIVS *""'"); 
  }
  arg1 = (struct RDERIVS *)(argp1);
  result = (double) ((arg1)->dphs);
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_rderivs_d2pow_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct RDERIVS *arg1 = (struct RDERIVS *) 0 ;
  double arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "rderivs_d2pow_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_RDERIVS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "rderivs_d2pow_set" "', argument " "1"" of type '" "struct RDERIVS *""'"); 
  }
  arg1 = (struct RDERIVS *)(argp1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "rderivs_d2pow_set" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  if (arg1) (arg1)->d2pow = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_rderivs_d2pow_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct RDERIVS *arg1 = (struct RDERIVS *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_RDERIVS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "rderivs_d2pow_get" "', argument " "1"" of type '" "struct RDERIVS *""'"); 
  }
  arg1 = (struct RDERIVS *)(argp1);
  result = (double) ((arg1)->d2pow);
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_rderivs_d2phs_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct RDERIVS *arg1 = (struct RDERIVS *) 0 ;
  double arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "rderivs_d2phs_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_RDERIVS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "rderivs_d2phs_set" "', argument " "1"" of type '" "struct RDERIVS *""'"); 
  }
  arg1 = (struct RDERIVS *)(argp1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "rderivs_d2phs_set" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  if (arg1) (arg1)->d2phs = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_rderivs_d2phs_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct RDERIVS *arg1 = (struct RDERIVS *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_RDERIVS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "rderivs_d2phs_get" "', argument " "1"" of type '" "struct RDERIVS *""'"); 
  }
  arg1 = (struct RDERIVS *)(argp1);
  result = (double) ((arg1)->d2phs);
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_rderivs_locpow_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct RDERIVS *arg1 = (struct RDERIVS *) 0 ;
  double arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "rderivs_locpow_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_RDERIVS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "rderivs_locpow_set" "', argument " "1"" of type '" "struct RDERIVS *""'"); 
  }
  arg1 = (struct RDERIVS *)(argp1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "rderivs_locpow_set" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  if (arg1) (arg1)->locpow = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_rderivs_locpow_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct RDERIVS *arg1 = (struct RDERIVS *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_RDERIVS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "rderivs_locpow_get" "', argument " "1"" of type '" "struct RDERIVS *""'"); 
  }
  arg1 = (struct RDERIVS *)(argp1);
  result = (double) ((arg1)->locpow);
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_new_rderivs(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct RDERIVS *result = 0 ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "new_rderivs", 0, 0, 0)) SWIG_fail;
  {
    errno = 0;
    result = (struct RDERIVS *)calloc(1, sizeof(struct RDERIVS));
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p_RDERIVS, SWIG_POINTER_NEW |  0 );
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_delete_rderivs(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct RDERIVS *arg1 = (struct RDERIVS *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_RDERIVS, SWIG_POINTER_DISOWN |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "delete_rderivs" "', argument " "1"" of type '" "struct RDERIVS *""'"); 
  }
  arg1 = (struct RDERIVS *)(argp1);
  {
    errno = 0;
    free((char *) arg1);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *rderivs_swigregister(PyObject *SWIGUNUSEDPARM(self), PyObject *args) {
  PyObject *obj;
  if (!SWIG_Python_UnpackTuple(args, "swigregister", 1, 1, &obj)) return NULL;
  SWIG_TypeNewClientData(SWIGTYPE_p_RDERIVS, SWIG_NewClientData(obj));
  return SWIG_Py_Void();
}

SWIGINTERN PyObject *rderivs_swiginit(PyObject *SWIGUNUSEDPARM(self), PyObject *args) {
  return SWIG_Python_InitShadowInstance(args);
}

SWIGINTERN PyObject *_wrap_fourierprops_r_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct FOURIERPROPS *arg1 = (struct FOURIERPROPS *) 0 ;
  double arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "fourierprops_r_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_FOURIERPROPS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "fourierprops_r_set" "', argument " "1"" of type '" "struct FOURIERPROPS *""'"); 
  }
  arg1 = (struct FOURIERPROPS *)(argp1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "fourierprops_r_set" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  if (arg1) (arg1)->r = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_fourierprops_r_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct FOURIERPROPS *arg1 = (struct FOURIERPROPS *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_FOURIERPROPS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "fourierprops_r_get" "', argument " "1"" of type '" "struct FOURIERPROPS *""'"); 
  }
  arg1 = (struct FOURIERPROPS *)(argp1);
  result = (double) ((arg1)->r);
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_fourierprops_rerr_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct FOURIERPROPS *arg1 = (struct FOURIERPROPS *) 0 ;
  float arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  float val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "fourierprops_rerr_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_FOURIERPROPS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "fourierprops_rerr_set" "', argument " "1"" of type '" "struct FOURIERPROPS *""'"); 
  }
  arg1 = (struct FOURIERPROPS *)(argp1);
  ecode2 = SWIG_AsVal_float(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "fourierprops_rerr_set" "', argument " "2"" of type '" "float""'");
  } 
  arg2 = (float)(val2);
  if (arg1) (arg1)->rerr = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_fourierprops_rerr_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct FOURIERPROPS *arg1 = (struct FOURIERPROPS *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  float result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_FOURIERPROPS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "fourierprops_rerr_get" "', argument " "1"" of type '" "struct FOURIERPROPS *""'"); 
  }
  arg1 = (struct FOURIERPROPS *)(argp1);
  result = (float) ((arg1)->rerr);
  resultobj = SWIG_From_float((float)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_fourierprops_z_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct FOURIERPROPS *arg1 = (struct FOURIERPROPS *) 0 ;
  double arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "fourierprops_z_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_FOURIERPROPS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "fourierprops_z_set" "', argument " "1"" of type '" "struct FOURIERPROPS *""'"); 
  }
  arg1 = (struct FOURIERPROPS *)(argp1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "fourierprops_z_set" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  if (arg1) (arg1)->z = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_fourierprops_z_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct FOURIERPROPS *arg1 = (struct FOURIERPROPS *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_FOURIERPROPS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "fourierprops_z_get" "', argument " "1"" of type '" "struct FOURIERPROPS *""'"); 
  }
  arg1 = (struct FOURIERPROPS *)(argp1);
  result = (double) ((arg1)->z);
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_fourierprops_zerr_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct FOURIERPROPS *arg1 = (struct FOURIERPROPS *) 0 ;
  float arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  float val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "fourierprops_zerr_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_FOURIERPROPS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "fourierprops_zerr_set" "', argument " "1"" of type '" "struct FOURIERPROPS *""'"); 
  }
  arg1 = (struct FOURIERPROPS *)(argp1);
  ecode2 = SWIG_AsVal_float(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "fourierprops_zerr_set" "', argument " "2"" of type '" "float""'");
  } 
  arg2 = (float)(val2);
  if (arg1) (arg1)->zerr = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_fourierprops_zerr_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct FOURIERPROPS *arg1 = (struct FOURIERPROPS *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  float result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_FOURIERPROPS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "fourierprops_zerr_get" "', argument " "1"" of type '" "struct FOURIERPROPS *""'"); 
  }
  arg1 = (struct FOURIERPROPS *)(argp1);
  result = (float) ((arg1)->zerr);
  resultobj = SWIG_From_float((float)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_fourierprops_w_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct FOURIERPROPS *arg1 = (struct FOURIERPROPS *) 0 ;
  double arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "fourierprops_w_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_FOURIERPROPS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "fourierprops_w_set" "', argument " "1"" of type '" "struct FOURIERPROPS *""'"); 
  }
  arg1 = (struct FOURIERPROPS *)(argp1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "fourierprops_w_set" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  if (arg1) (arg1)->w = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_fourierprops_w_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct FOURIERPROPS *arg1 = (struct FOURIERPROPS *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_FOURIERPROPS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "fourierprops_w_get" "', argument " "1"" of type '" "struct FOURIERPROPS *""'"); 
  }
  arg1 = (struct FOURIERPROPS *)(argp1);
  result = (double) ((arg1)->w);
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_fourierprops_werr_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct FOURIERPROPS *arg1 = (struct FOURIERPROPS *) 0 ;
  float arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  float val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "fourierprops_werr_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_FOURIERPROPS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "fourierprops_werr_set" "', argument " "1"" of type '" "struct FOURIERPROPS *""'"); 
  }
  arg1 = (struct FOURIERPROPS *)(argp1);
  ecode2 = SWIG_AsVal_float(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "fourierprops_werr_set" "', argument " "2"" of type '" "float""'");
  } 
  arg2 = (float)(val2);
  if (arg1) (arg1)->werr = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_fourierprops_werr_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct FOURIERPROPS *arg1 = (struct FOURIERPROPS *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  float result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_FOURIERPROPS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "fourierprops_werr_get" "', argument " "1"" of type '" "struct FOURIERPROPS *""'"); 
  }
  arg1 = (struct FOURIERPROPS *)(argp1);
  result = (float) ((arg1)->werr);
  resultobj = SWIG_From_float((float)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_fourierprops_pow_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct FOURIERPROPS *arg1 = (struct FOURIERPROPS *) 0 ;
  float arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  float val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "fourierprops_pow_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_FOURIERPROPS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "fourierprops_pow_set" "', argument " "1"" of type '" "struct FOURIERPROPS *""'"); 
  }
  arg1 = (struct FOURIERPROPS *)(argp1);
  ecode2 = SWIG_AsVal_float(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "fourierprops_pow_set" "', argument " "2"" of type '" "float""'");
  } 
  arg2 = (float)(val2);
  if (arg1) (arg1)->pow = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_fourierprops_pow_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct FOURIERPROPS *arg1 = (struct FOURIERPROPS *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  float result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_FOURIERPROPS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "fourierprops_pow_get" "', argument " "1"" of type '" "struct FOURIERPROPS *""'"); 
  }
  arg1 = (struct FOURIERPROPS *)(argp1);
  result = (float) ((arg1)->pow);
  resultobj = SWIG_From_float((float)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_fourierprops_powerr_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct FOURIERPROPS *arg1 = (struct FOURIERPROPS *) 0 ;
  float arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  float val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "fourierprops_powerr_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_FOURIERPROPS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "fourierprops_powerr_set" "', argument " "1"" of type '" "struct FOURIERPROPS *""'"); 
  }
  arg1 = (struct FOURIERPROPS *)(argp1);
  ecode2 = SWIG_AsVal_float(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "fourierprops_powerr_set" "', argument " "2"" of type '" "float""'");
  } 
  arg2 = (float)(val2);
  if (arg1) (arg1)->powerr = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_fourierprops_powerr_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct FOURIERPROPS *arg1 = (struct FOURIERPROPS *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  float result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_FOURIERPROPS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "fourierprops_powerr_get" "', argument " "1"" of type '" "struct FOURIERPROPS *""'"); 
  }
  arg1 = (struct FOURIERPROPS *)(argp1);
  result = (float) ((arg1)->powerr);
  resultobj = SWIG_From_float((float)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_fourierprops_sig_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct FOURIERPROPS *arg1 = (struct FOURIERPROPS *) 0 ;
  float arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  float val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "fourierprops_sig_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_FOURIERPROPS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "fourierprops_sig_set" "', argument " "1"" of type '" "struct FOURIERPROPS *""'"); 
  }
  arg1 = (struct FOURIERPROPS *)(argp1);
  ecode2 = SWIG_AsVal_float(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "fourierprops_sig_set" "', argument " "2"" of type '" "float""'");
  } 
  arg2 = (float)(val2);
  if (arg1) (arg1)->sig = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_fourierprops_sig_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct FOURIERPROPS *arg1 = (struct FOURIERPROPS *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  float result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_FOURIERPROPS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "fourierprops_sig_get" "', argument " "1"" of type '" "struct FOURIERPROPS *""'"); 
  }
  arg1 = (struct FOURIERPROPS *)(argp1);
  result = (float) ((arg1)->sig);
  resultobj = SWIG_From_float((float)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_fourierprops_rawpow_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct FOURIERPROPS *arg1 = (struct FOURIERPROPS *) 0 ;
  float arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  float val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "fourierprops_rawpow_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_FOURIERPROPS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "fourierprops_rawpow_set" "', argument " "1"" of type '" "struct FOURIERPROPS *""'"); 
  }
  arg1 = (struct FOURIERPROPS *)(argp1);
  ecode2 = SWIG_AsVal_float(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "fourierprops_rawpow_set" "', argument " "2"" of type '" "float""'");
  } 
  arg2 = (float)(val2);
  if (arg1) (arg1)->rawpow = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_fourierprops_rawpow_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct FOURIERPROPS *arg1 = (struct FOURIERPROPS *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  float result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_FOURIERPROPS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "fourierprops_rawpow_get" "', argument " "1"" of type '" "struct FOURIERPROPS *""'"); 
  }
  arg1 = (struct FOURIERPROPS *)(argp1);
  result = (float) ((arg1)->rawpow);
  resultobj = SWIG_From_float((float)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_fourierprops_phs_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct FOURIERPROPS *arg1 = (struct FOURIERPROPS *) 0 ;
  float arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  float val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "fourierprops_phs_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_FOURIERPROPS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "fourierprops_phs_set" "', argument " "1"" of type '" "struct FOURIERPROPS *""'"); 
  }
  arg1 = (struct FOURIERPROPS *)(argp1);
  ecode2 = SWIG_AsVal_float(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "fourierprops_phs_set" "', argument " "2"" of type '" "float""'");
  } 
  arg2 = (float)(val2);
  if (arg1) (arg1)->phs = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_fourierprops_phs_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct FOURIERPROPS *arg1 = (struct FOURIERPROPS *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  float result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_FOURIERPROPS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "fourierprops_phs_get" "', argument " "1"" of type '" "struct FOURIERPROPS *""'"); 
  }
  arg1 = (struct FOURIERPROPS *)(argp1);
  result = (float) ((arg1)->phs);
  resultobj = SWIG_From_float((float)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_fourierprops_phserr_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct FOURIERPROPS *arg1 = (struct FOURIERPROPS *) 0 ;
  float arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  float val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "fourierprops_phserr_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_FOURIERPROPS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "fourierprops_phserr_set" "', argument " "1"" of type '" "struct FOURIERPROPS *""'"); 
  }
  arg1 = (struct FOURIERPROPS *)(argp1);
  ecode2 = SWIG_AsVal_float(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "fourierprops_phserr_set" "', argument " "2"" of type '" "float""'");
  } 
  arg2 = (float)(val2);
  if (arg1) (arg1)->phserr = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_fourierprops_phserr_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct FOURIERPROPS *arg1 = (struct FOURIERPROPS *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  float result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_FOURIERPROPS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "fourierprops_phserr_get" "', argument " "1"" of type '" "struct FOURIERPROPS *""'"); 
  }
  arg1 = (struct FOURIERPROPS *)(argp1);
  result = (float) ((arg1)->phserr);
  resultobj = SWIG_From_float((float)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_fourierprops_cen_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct FOURIERPROPS *arg1 = (struct FOURIERPROPS *) 0 ;
  float arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  float val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "fourierprops_cen_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_FOURIERPROPS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "fourierprops_cen_set" "', argument " "1"" of type '" "struct FOURIERPROPS *""'"); 
  }
  arg1 = (struct FOURIERPROPS *)(argp1);
  ecode2 = SWIG_AsVal_float(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "fourierprops_cen_set" "', argument " "2"" of type '" "float""'");
  } 
  arg2 = (float)(val2);
  if (arg1) (arg1)->cen = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_fourierprops_cen_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct FOURIERPROPS *arg1 = (struct FOURIERPROPS *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  float result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_FOURIERPROPS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "fourierprops_cen_get" "', argument " "1"" of type '" "struct FOURIERPROPS *""'"); 
  }
  arg1 = (struct FOURIERPROPS *)(argp1);
  result = (float) ((arg1)->cen);
  resultobj = SWIG_From_float((float)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_fourierprops_cenerr_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct FOURIERPROPS *arg1 = (struct FOURIERPROPS *) 0 ;
  float arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  float val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "fourierprops_cenerr_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_FOURIERPROPS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "fourierprops_cenerr_set" "', argument " "1"" of type '" "struct FOURIERPROPS *""'"); 
  }
  arg1 = (struct FOURIERPROPS *)(argp1);
  ecode2 = SWIG_AsVal_float(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "fourierprops_cenerr_set" "', argument " "2"" of type '" "float""'");
  } 
  arg2 = (float)(val2);
  if (arg1) (arg1)->cenerr = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_fourierprops_cenerr_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct FOURIERPROPS *arg1 = (struct FOURIERPROPS *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  float result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_FOURIERPROPS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "fourierprops_cenerr_get" "', argument " "1"" of type '" "struct FOURIERPROPS *""'"); 
  }
  arg1 = (struct FOURIERPROPS *)(argp1);
  result = (float) ((arg1)->cenerr);
  resultobj = SWIG_From_float((float)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_fourierprops_pur_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct FOURIERPROPS *arg1 = (struct FOURIERPROPS *) 0 ;
  float arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  float val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "fourierprops_pur_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_FOURIERPROPS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "fourierprops_pur_set" "', argument " "1"" of type '" "struct FOURIERPROPS *""'"); 
  }
  arg1 = (struct FOURIERPROPS *)(argp1);
  ecode2 = SWIG_AsVal_float(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "fourierprops_pur_set" "', argument " "2"" of type '" "float""'");
  } 
  arg2 = (float)(val2);
  if (arg1) (arg1)->pur = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_fourierprops_pur_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct FOURIERPROPS *arg1 = (struct FOURIERPROPS *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  float result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_FOURIERPROPS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "fourierprops_pur_get" "', argument " "1"" of type '" "struct FOURIERPROPS *""'"); 
  }
  arg1 = (struct FOURIERPROPS *)(argp1);
  result = (float) ((arg1)->pur);
  resultobj = SWIG_From_float((float)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_fourierprops_purerr_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct FOURIERPROPS *arg1 = (struct FOURIERPROPS *) 0 ;
  float arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  float val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "fourierprops_purerr_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_FOURIERPROPS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "fourierprops_purerr_set" "', argument " "1"" of type '" "struct FOURIERPROPS *""'"); 
  }
  arg1 = (struct FOURIERPROPS *)(argp1);
  ecode2 = SWIG_AsVal_float(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "fourierprops_purerr_set" "', argument " "2"" of type '" "float""'");
  } 
  arg2 = (float)(val2);
  if (arg1) (arg1)->purerr = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_fourierprops_purerr_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct FOURIERPROPS *arg1 = (struct FOURIERPROPS *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  float result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_FOURIERPROPS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "fourierprops_purerr_get" "', argument " "1"" of type '" "struct FOURIERPROPS *""'"); 
  }
  arg1 = (struct FOURIERPROPS *)(argp1);
  result = (float) ((arg1)->purerr);
  resultobj = SWIG_From_float((float)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_fourierprops_locpow_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct FOURIERPROPS *arg1 = (struct FOURIERPROPS *) 0 ;
  float arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  float val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "fourierprops_locpow_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_FOURIERPROPS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "fourierprops_locpow_set" "', argument " "1"" of type '" "struct FOURIERPROPS *""'"); 
  }
  arg1 = (struct FOURIERPROPS *)(argp1);
  ecode2 = SWIG_AsVal_float(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "fourierprops_locpow_set" "', argument " "2"" of type '" "float""'");
  } 
  arg2 = (float)(val2);
  if (arg1) (arg1)->locpow = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_fourierprops_locpow_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct FOURIERPROPS *arg1 = (struct FOURIERPROPS *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  float result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_FOURIERPROPS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "fourierprops_locpow_get" "', argument " "1"" of type '" "struct FOURIERPROPS *""'"); 
  }
  arg1 = (struct FOURIERPROPS *)(argp1);
  result = (float) ((arg1)->locpow);
  resultobj = SWIG_From_float((float)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_new_fourierprops(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct FOURIERPROPS *result = 0 ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "new_fourierprops", 0, 0, 0)) SWIG_fail;
  {
    errno = 0;
    result = (struct FOURIERPROPS *)calloc(1, sizeof(struct FOURIERPROPS));
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p_FOURIERPROPS, SWIG_POINTER_NEW |  0 );
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_delete_fourierprops(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct FOURIERPROPS *arg1 = (struct FOURIERPROPS *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_FOURIERPROPS, SWIG_POINTER_DISOWN |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "delete_fourierprops" "', argument " "1"" of type '" "struct FOURIERPROPS *""'"); 
  }
  arg1 = (struct FOURIERPROPS *)(argp1);
  {
    errno = 0;
    free((char *) arg1);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *fourierprops_swigregister(PyObject *SWIGUNUSEDPARM(self), PyObject *args) {
  PyObject *obj;
  if (!SWIG_Python_UnpackTuple(args, "swigregister", 1, 1, &obj)) return NULL;
  SWIG_TypeNewClientData(SWIGTYPE_p_FOURIERPROPS, SWIG_NewClientData(obj));
  return SWIG_Py_Void();
}

SWIGINTERN PyObject *fourierprops_swiginit(PyObject *SWIGUNUSEDPARM(self), PyObject *args) {
  return SWIG_Python_InitShadowInstance(args);
}

SWIGINTERN PyObject *_wrap_foldstats_numdata_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct foldstats *arg1 = (struct foldstats *) 0 ;
  double arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "foldstats_numdata_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_foldstats, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "foldstats_numdata_set" "', argument " "1"" of type '" "struct foldstats *""'"); 
  }
  arg1 = (struct foldstats *)(argp1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "foldstats_numdata_set" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  if (arg1) (arg1)->numdata = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_foldstats_numdata_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct foldstats *arg1 = (struct foldstats *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_foldstats, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "foldstats_numdata_get" "', argument " "1"" of type '" "struct foldstats *""'"); 
  }
  arg1 = (struct foldstats *)(argp1);
  result = (double) ((arg1)->numdata);
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_foldstats_data_avg_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct foldstats *arg1 = (struct foldstats *) 0 ;
  double arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "foldstats_data_avg_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_foldstats, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "foldstats_data_avg_set" "', argument " "1"" of type '" "struct foldstats *""'"); 
  }
  arg1 = (struct foldstats *)(argp1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "foldstats_data_avg_set" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  if (arg1) (arg1)->data_avg = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_foldstats_data_avg_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct foldstats *arg1 = (struct foldstats *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_foldstats, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "foldstats_data_avg_get" "', argument " "1"" of type '" "struct foldstats *""'"); 
  }
  arg1 = (struct foldstats *)(argp1);
  result = (double) ((arg1)->data_avg);
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_foldstats_data_var_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct foldstats *arg1 = (struct foldstats *) 0 ;
  double arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "foldstats_data_var_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_foldstats, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "foldstats_data_var_set" "', argument " "1"" of type '" "struct foldstats *""'"); 
  }
  arg1 = (struct foldstats *)(argp1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "foldstats_data_var_set" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  if (arg1) (arg1)->data_var = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_foldstats_data_var_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct foldstats *arg1 = (struct foldstats *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_foldstats, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "foldstats_data_var_get" "', argument " "1"" of type '" "struct foldstats *""'"); 
  }
  arg1 = (struct foldstats *)(argp1);
  result = (double) ((arg1)->data_var);
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_foldstats_numprof_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct foldstats *arg1 = (struct foldstats *) 0 ;
  double arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "foldstats_numprof_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_foldstats, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "foldstats_numprof_set" "', argument " "1"" of type '" "struct foldstats *""'"); 
  }
  arg1 = (struct foldstats *)(argp1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "foldstats_numprof_set" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  if (arg1) (arg1)->numprof = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_foldstats_numprof_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct foldstats *arg1 = (struct foldstats *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_foldstats, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "foldstats_numprof_get" "', argument " "1"" of type '" "struct foldstats *""'"); 
  }
  arg1 = (struct foldstats *)(argp1);
  result = (double) ((arg1)->numprof);
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_foldstats_prof_avg_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct foldstats *arg1 = (struct foldstats *) 0 ;
  double arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "foldstats_prof_avg_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_foldstats, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "foldstats_prof_avg_set" "', argument " "1"" of type '" "struct foldstats *""'"); 
  }
  arg1 = (struct foldstats *)(argp1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "foldstats_prof_avg_set" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  if (arg1) (arg1)->prof_avg = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_foldstats_prof_avg_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct foldstats *arg1 = (struct foldstats *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_foldstats, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "foldstats_prof_avg_get" "', argument " "1"" of type '" "struct foldstats *""'"); 
  }
  arg1 = (struct foldstats *)(argp1);
  result = (double) ((arg1)->prof_avg);
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_foldstats_prof_var_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct foldstats *arg1 = (struct foldstats *) 0 ;
  double arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "foldstats_prof_var_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_foldstats, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "foldstats_prof_var_set" "', argument " "1"" of type '" "struct foldstats *""'"); 
  }
  arg1 = (struct foldstats *)(argp1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "foldstats_prof_var_set" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  if (arg1) (arg1)->prof_var = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_foldstats_prof_var_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct foldstats *arg1 = (struct foldstats *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_foldstats, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "foldstats_prof_var_get" "', argument " "1"" of type '" "struct foldstats *""'"); 
  }
  arg1 = (struct foldstats *)(argp1);
  result = (double) ((arg1)->prof_var);
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_foldstats_redchi_set(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct foldstats *arg1 = (struct foldstats *) 0 ;
  double arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "foldstats_redchi_set", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_foldstats, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "foldstats_redchi_set" "', argument " "1"" of type '" "struct foldstats *""'"); 
  }
  arg1 = (struct foldstats *)(argp1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "foldstats_redchi_set" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  if (arg1) (arg1)->redchi = arg2;
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_foldstats_redchi_get(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct foldstats *arg1 = (struct foldstats *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_foldstats, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "foldstats_redchi_get" "', argument " "1"" of type '" "struct foldstats *""'"); 
  }
  arg1 = (struct foldstats *)(argp1);
  result = (double) ((arg1)->redchi);
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_new_foldstats(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct foldstats *result = 0 ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "new_foldstats", 0, 0, 0)) SWIG_fail;
  {
    errno = 0;
    result = (struct foldstats *)calloc(1, sizeof(struct foldstats));
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p_foldstats, SWIG_POINTER_NEW |  0 );
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_delete_foldstats(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  struct foldstats *arg1 = (struct foldstats *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_foldstats, SWIG_POINTER_DISOWN |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "delete_foldstats" "', argument " "1"" of type '" "struct foldstats *""'"); 
  }
  arg1 = (struct foldstats *)(argp1);
  {
    errno = 0;
    free((char *) arg1);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *foldstats_swigregister(PyObject *SWIGUNUSEDPARM(self), PyObject *args) {
  PyObject *obj;
  if (!SWIG_Python_UnpackTuple(args, "swigregister", 1, 1, &obj)) return NULL;
  SWIG_TypeNewClientData(SWIGTYPE_p_foldstats, SWIG_NewClientData(obj));
  return SWIG_Py_Void();
}

SWIGINTERN PyObject *foldstats_swiginit(PyObject *SWIGUNUSEDPARM(self), PyObject *args) {
  return SWIG_Python_InitShadowInstance(args);
}

SWIGINTERN PyObject *_wrap_gen_fvect(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  long arg1 ;
  float **arg2 = (float **) 0 ;
  long *arg3 = (long *) 0 ;
  long val1 ;
  int ecode1 = 0 ;
  float *data_temp2 = NULL ;
  long dim_temp2 ;
  PyObject *swig_obj[1] ;
  
  {
    arg2 = &data_temp2;
    arg3 = &dim_temp2;
  }
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  ecode1 = SWIG_AsVal_long(swig_obj[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "gen_fvect" "', argument " "1"" of type '" "long""'");
  } 
  arg1 = (long)(val1);
  {
    errno = 0;
    wrap_gen_fvect(arg1,arg2,arg3);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_Py_Void();
  {
    npy_intp dims[1] = {
      *arg3 
    };
    PyObject* obj = PyArray_SimpleNewFromData(1, dims, NPY_FLOAT, (void*)(*arg2));
    PyArrayObject* array = (PyArrayObject*) obj;
    
    if (!array) SWIG_fail;
    
#ifdef SWIGPY_USE_CAPSULE
    PyObject* cap = PyCapsule_New((void*)(*arg2), SWIGPY_CAPSULE_NAME, free_cap);
#else
    PyObject* cap = PyCObject_FromVoidPtr((void*)(*arg2), free);
#endif
    
#if NPY_API_VERSION < 0x00000007
    PyArray_BASE(array) = cap;
#else
    PyArray_SetBaseObject(array,cap);
#endif
    
    resultobj = SWIG_Python_AppendOutput(resultobj,obj);
  }
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_gen_cvect(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  long arg1 ;
  fcomplex **arg2 = (fcomplex **) 0 ;
  long *arg3 = (long *) 0 ;
  long val1 ;
  int ecode1 = 0 ;
  fcomplex *data_temp2 = NULL ;
  long dim_temp2 ;
  PyObject *swig_obj[1] ;
  
  {
    arg2 = &data_temp2;
    arg3 = &dim_temp2;
  }
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  ecode1 = SWIG_AsVal_long(swig_obj[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "gen_cvect" "', argument " "1"" of type '" "long""'");
  } 
  arg1 = (long)(val1);
  {
    errno = 0;
    wrap_gen_cvect(arg1,arg2,arg3);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_Py_Void();
  {
    npy_intp dims[1] = {
      *arg3 
    };
    PyObject* obj = PyArray_SimpleNewFromData(1, dims, NPY_CFLOAT, (void*)(*arg2));
    PyArrayObject* array = (PyArrayObject*) obj;
    
    if (!array) SWIG_fail;
    
#ifdef SWIGPY_USE_CAPSULE
    PyObject* cap = PyCapsule_New((void*)(*arg2), SWIGPY_CAPSULE_NAME, free_cap);
#else
    PyObject* cap = PyCObject_FromVoidPtr((void*)(*arg2), free);
#endif
    
#if NPY_API_VERSION < 0x00000007
    PyArray_BASE(array) = cap;
#else
    PyArray_SetBaseObject(array,cap);
#endif
    
    resultobj = SWIG_Python_AppendOutput(resultobj,obj);
  }
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_power_arr(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  fcomplex *arg1 = (fcomplex *) 0 ;
  long arg2 ;
  float **arg3 = (float **) 0 ;
  long *arg4 = (long *) 0 ;
  PyArrayObject *array1 = NULL ;
  int i1 = 1 ;
  float *data_temp3 = NULL ;
  long dim_temp3 ;
  PyObject *swig_obj[1] ;
  
  {
    arg3 = &data_temp3;
    arg4 = &dim_temp3;
  }
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  {
    array1 = obj_to_array_no_conversion(swig_obj[0], NPY_CFLOAT);
    if (!array1 || !require_dimensions(array1,1) || !require_contiguous(array1)
      || !require_native(array1)) SWIG_fail;
    arg1 = (fcomplex*) array_data(array1);
    arg2 = 1;
    for (i1=0; i1 < array_numdims(array1); ++i1) arg2 *= array_size(array1,i1);
  }
  {
    errno = 0;
    wrap_power_arr(arg1,arg2,arg3,arg4);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_Py_Void();
  {
    npy_intp dims[1] = {
      *arg4 
    };
    PyObject* obj = PyArray_SimpleNewFromData(1, dims, NPY_FLOAT, (void*)(*arg3));
    PyArrayObject* array = (PyArrayObject*) obj;
    
    if (!array) SWIG_fail;
    
#ifdef SWIGPY_USE_CAPSULE
    PyObject* cap = PyCapsule_New((void*)(*arg3), SWIGPY_CAPSULE_NAME, free_cap);
#else
    PyObject* cap = PyCObject_FromVoidPtr((void*)(*arg3), free);
#endif
    
#if NPY_API_VERSION < 0x00000007
    PyArray_BASE(array) = cap;
#else
    PyArray_SetBaseObject(array,cap);
#endif
    
    resultobj = SWIG_Python_AppendOutput(resultobj,obj);
  }
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_phase_arr(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  fcomplex *arg1 = (fcomplex *) 0 ;
  long arg2 ;
  float **arg3 = (float **) 0 ;
  long *arg4 = (long *) 0 ;
  PyArrayObject *array1 = NULL ;
  int i1 = 1 ;
  float *data_temp3 = NULL ;
  long dim_temp3 ;
  PyObject *swig_obj[1] ;
  
  {
    arg3 = &data_temp3;
    arg4 = &dim_temp3;
  }
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  {
    array1 = obj_to_array_no_conversion(swig_obj[0], NPY_CFLOAT);
    if (!array1 || !require_dimensions(array1,1) || !require_contiguous(array1)
      || !require_native(array1)) SWIG_fail;
    arg1 = (fcomplex*) array_data(array1);
    arg2 = 1;
    for (i1=0; i1 < array_numdims(array1); ++i1) arg2 *= array_size(array1,i1);
  }
  {
    errno = 0;
    wrap_phase_arr(arg1,arg2,arg3,arg4);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_Py_Void();
  {
    npy_intp dims[1] = {
      *arg4 
    };
    PyObject* obj = PyArray_SimpleNewFromData(1, dims, NPY_FLOAT, (void*)(*arg3));
    PyArrayObject* array = (PyArrayObject*) obj;
    
    if (!array) SWIG_fail;
    
#ifdef SWIGPY_USE_CAPSULE
    PyObject* cap = PyCapsule_New((void*)(*arg3), SWIGPY_CAPSULE_NAME, free_cap);
#else
    PyObject* cap = PyCObject_FromVoidPtr((void*)(*arg3), free);
#endif
    
#if NPY_API_VERSION < 0x00000007
    PyArray_BASE(array) = cap;
#else
    PyArray_SetBaseObject(array,cap);
#endif
    
    resultobj = SWIG_Python_AppendOutput(resultobj,obj);
  }
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_frotate(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  float *arg1 = (float *) 0 ;
  long arg2 ;
  float arg3 ;
  PyArrayObject *array1 = NULL ;
  int i1 = 1 ;
  float val3 ;
  int ecode3 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "frotate", 2, 2, swig_obj)) SWIG_fail;
  {
    array1 = obj_to_array_no_conversion(swig_obj[0], NPY_FLOAT);
    if (!array1 || !require_dimensions(array1,1) || !require_contiguous(array1)
      || !require_native(array1)) SWIG_fail;
    arg1 = (float*) array_data(array1);
    arg2 = 1;
    for (i1=0; i1 < array_numdims(array1); ++i1) arg2 *= array_size(array1,i1);
  }
  ecode3 = SWIG_AsVal_float(swig_obj[1], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "frotate" "', argument " "3"" of type '" "float""'");
  } 
  arg3 = (float)(val3);
  {
    errno = 0;
    frotate(arg1,arg2,arg3);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_drotate(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  double *arg1 = (double *) 0 ;
  long arg2 ;
  double arg3 ;
  PyArrayObject *array1 = NULL ;
  int i1 = 1 ;
  double val3 ;
  int ecode3 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "drotate", 2, 2, swig_obj)) SWIG_fail;
  {
    array1 = obj_to_array_no_conversion(swig_obj[0], NPY_DOUBLE);
    if (!array1 || !require_dimensions(array1,1) || !require_contiguous(array1)
      || !require_native(array1)) SWIG_fail;
    arg1 = (double*) array_data(array1);
    arg2 = 1;
    for (i1=0; i1 < array_numdims(array1); ++i1) arg2 *= array_size(array1,i1);
  }
  ecode3 = SWIG_AsVal_double(swig_obj[1], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "drotate" "', argument " "3"" of type '" "double""'");
  } 
  arg3 = (double)(val3);
  {
    errno = 0;
    drotate(arg1,arg2,arg3);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_keplers_eqn(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  double arg1 ;
  double arg2 ;
  double arg3 ;
  double arg4 ;
  double val1 ;
  int ecode1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  double val3 ;
  int ecode3 = 0 ;
  double val4 ;
  int ecode4 = 0 ;
  PyObject *swig_obj[4] ;
  double result;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "keplers_eqn", 4, 4, swig_obj)) SWIG_fail;
  ecode1 = SWIG_AsVal_double(swig_obj[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "keplers_eqn" "', argument " "1"" of type '" "double""'");
  } 
  arg1 = (double)(val1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "keplers_eqn" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  ecode3 = SWIG_AsVal_double(swig_obj[2], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "keplers_eqn" "', argument " "3"" of type '" "double""'");
  } 
  arg3 = (double)(val3);
  ecode4 = SWIG_AsVal_double(swig_obj[3], &val4);
  if (!SWIG_IsOK(ecode4)) {
    SWIG_exception_fail(SWIG_ArgError(ecode4), "in method '" "keplers_eqn" "', argument " "4"" of type '" "double""'");
  } 
  arg4 = (double)(val4);
  {
    errno = 0;
    result = (double)keplers_eqn(arg1,arg2,arg3,arg4);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_E_to_phib(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  double *arg1 = (double *) 0 ;
  long arg2 ;
  orbitparams *arg3 = (orbitparams *) 0 ;
  PyArrayObject *array1 = NULL ;
  int i1 = 1 ;
  void *argp3 = 0 ;
  int res3 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "E_to_phib", 2, 2, swig_obj)) SWIG_fail;
  {
    array1 = obj_to_array_no_conversion(swig_obj[0], NPY_DOUBLE);
    if (!array1 || !require_dimensions(array1,1) || !require_contiguous(array1)
      || !require_native(array1)) SWIG_fail;
    arg1 = (double*) array_data(array1);
    arg2 = 1;
    for (i1=0; i1 < array_numdims(array1); ++i1) arg2 *= array_size(array1,i1);
  }
  res3 = SWIG_ConvertPtr(swig_obj[1], &argp3,SWIGTYPE_p_orbitparams, 0 |  0 );
  if (!SWIG_IsOK(res3)) {
    SWIG_exception_fail(SWIG_ArgError(res3), "in method '" "E_to_phib" "', argument " "3"" of type '" "orbitparams *""'"); 
  }
  arg3 = (orbitparams *)(argp3);
  {
    errno = 0;
    E_to_phib(arg1,arg2,arg3);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_E_to_v(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  double *arg1 = (double *) 0 ;
  long arg2 ;
  orbitparams *arg3 = (orbitparams *) 0 ;
  PyArrayObject *array1 = NULL ;
  int i1 = 1 ;
  void *argp3 = 0 ;
  int res3 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "E_to_v", 2, 2, swig_obj)) SWIG_fail;
  {
    array1 = obj_to_array_no_conversion(swig_obj[0], NPY_DOUBLE);
    if (!array1 || !require_dimensions(array1,1) || !require_contiguous(array1)
      || !require_native(array1)) SWIG_fail;
    arg1 = (double*) array_data(array1);
    arg2 = 1;
    for (i1=0; i1 < array_numdims(array1); ++i1) arg2 *= array_size(array1,i1);
  }
  res3 = SWIG_ConvertPtr(swig_obj[1], &argp3,SWIGTYPE_p_orbitparams, 0 |  0 );
  if (!SWIG_IsOK(res3)) {
    SWIG_exception_fail(SWIG_ArgError(res3), "in method '" "E_to_v" "', argument " "3"" of type '" "orbitparams *""'"); 
  }
  arg3 = (orbitparams *)(argp3);
  {
    errno = 0;
    E_to_v(arg1,arg2,arg3);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_E_to_p(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  double *arg1 = (double *) 0 ;
  long arg2 ;
  double arg3 ;
  orbitparams *arg4 = (orbitparams *) 0 ;
  PyArrayObject *array1 = NULL ;
  int i1 = 1 ;
  double val3 ;
  int ecode3 = 0 ;
  void *argp4 = 0 ;
  int res4 = 0 ;
  PyObject *swig_obj[3] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "E_to_p", 3, 3, swig_obj)) SWIG_fail;
  {
    array1 = obj_to_array_no_conversion(swig_obj[0], NPY_DOUBLE);
    if (!array1 || !require_dimensions(array1,1) || !require_contiguous(array1)
      || !require_native(array1)) SWIG_fail;
    arg1 = (double*) array_data(array1);
    arg2 = 1;
    for (i1=0; i1 < array_numdims(array1); ++i1) arg2 *= array_size(array1,i1);
  }
  ecode3 = SWIG_AsVal_double(swig_obj[1], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "E_to_p" "', argument " "3"" of type '" "double""'");
  } 
  arg3 = (double)(val3);
  res4 = SWIG_ConvertPtr(swig_obj[2], &argp4,SWIGTYPE_p_orbitparams, 0 |  0 );
  if (!SWIG_IsOK(res4)) {
    SWIG_exception_fail(SWIG_ArgError(res4), "in method '" "E_to_p" "', argument " "4"" of type '" "orbitparams *""'"); 
  }
  arg4 = (orbitparams *)(argp4);
  {
    errno = 0;
    E_to_p(arg1,arg2,arg3,arg4);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_E_to_z(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  double *arg1 = (double *) 0 ;
  long arg2 ;
  double arg3 ;
  double arg4 ;
  orbitparams *arg5 = (orbitparams *) 0 ;
  PyArrayObject *array1 = NULL ;
  int i1 = 1 ;
  double val3 ;
  int ecode3 = 0 ;
  double val4 ;
  int ecode4 = 0 ;
  void *argp5 = 0 ;
  int res5 = 0 ;
  PyObject *swig_obj[4] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "E_to_z", 4, 4, swig_obj)) SWIG_fail;
  {
    array1 = obj_to_array_no_conversion(swig_obj[0], NPY_DOUBLE);
    if (!array1 || !require_dimensions(array1,1) || !require_contiguous(array1)
      || !require_native(array1)) SWIG_fail;
    arg1 = (double*) array_data(array1);
    arg2 = 1;
    for (i1=0; i1 < array_numdims(array1); ++i1) arg2 *= array_size(array1,i1);
  }
  ecode3 = SWIG_AsVal_double(swig_obj[1], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "E_to_z" "', argument " "3"" of type '" "double""'");
  } 
  arg3 = (double)(val3);
  ecode4 = SWIG_AsVal_double(swig_obj[2], &val4);
  if (!SWIG_IsOK(ecode4)) {
    SWIG_exception_fail(SWIG_ArgError(ecode4), "in method '" "E_to_z" "', argument " "4"" of type '" "double""'");
  } 
  arg4 = (double)(val4);
  res5 = SWIG_ConvertPtr(swig_obj[3], &argp5,SWIGTYPE_p_orbitparams, 0 |  0 );
  if (!SWIG_IsOK(res5)) {
    SWIG_exception_fail(SWIG_ArgError(res5), "in method '" "E_to_z" "', argument " "5"" of type '" "orbitparams *""'"); 
  }
  arg5 = (orbitparams *)(argp5);
  {
    errno = 0;
    E_to_z(arg1,arg2,arg3,arg4,arg5);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_E_to_phib_BT(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  double *arg1 = (double *) 0 ;
  long arg2 ;
  orbitparams *arg3 = (orbitparams *) 0 ;
  PyArrayObject *array1 = NULL ;
  int i1 = 1 ;
  void *argp3 = 0 ;
  int res3 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "E_to_phib_BT", 2, 2, swig_obj)) SWIG_fail;
  {
    array1 = obj_to_array_no_conversion(swig_obj[0], NPY_DOUBLE);
    if (!array1 || !require_dimensions(array1,1) || !require_contiguous(array1)
      || !require_native(array1)) SWIG_fail;
    arg1 = (double*) array_data(array1);
    arg2 = 1;
    for (i1=0; i1 < array_numdims(array1); ++i1) arg2 *= array_size(array1,i1);
  }
  res3 = SWIG_ConvertPtr(swig_obj[1], &argp3,SWIGTYPE_p_orbitparams, 0 |  0 );
  if (!SWIG_IsOK(res3)) {
    SWIG_exception_fail(SWIG_ArgError(res3), "in method '" "E_to_phib_BT" "', argument " "3"" of type '" "orbitparams *""'"); 
  }
  arg3 = (orbitparams *)(argp3);
  {
    errno = 0;
    E_to_phib_BT(arg1,arg2,arg3);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_dorbint(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  double **arg1 = (double **) 0 ;
  long *arg2 = (long *) 0 ;
  double arg3 ;
  long arg4 ;
  double arg5 ;
  orbitparams *arg6 = (orbitparams *) 0 ;
  double *data_temp1 = NULL ;
  long dim_temp1 ;
  double val3 ;
  int ecode3 = 0 ;
  long val4 ;
  int ecode4 = 0 ;
  double val5 ;
  int ecode5 = 0 ;
  void *argp6 = 0 ;
  int res6 = 0 ;
  PyObject *swig_obj[4] ;
  
  {
    arg1 = &data_temp1;
    arg2 = &dim_temp1;
  }
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "dorbint", 4, 4, swig_obj)) SWIG_fail;
  ecode3 = SWIG_AsVal_double(swig_obj[0], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "dorbint" "', argument " "3"" of type '" "double""'");
  } 
  arg3 = (double)(val3);
  ecode4 = SWIG_AsVal_long(swig_obj[1], &val4);
  if (!SWIG_IsOK(ecode4)) {
    SWIG_exception_fail(SWIG_ArgError(ecode4), "in method '" "dorbint" "', argument " "4"" of type '" "long""'");
  } 
  arg4 = (long)(val4);
  ecode5 = SWIG_AsVal_double(swig_obj[2], &val5);
  if (!SWIG_IsOK(ecode5)) {
    SWIG_exception_fail(SWIG_ArgError(ecode5), "in method '" "dorbint" "', argument " "5"" of type '" "double""'");
  } 
  arg5 = (double)(val5);
  res6 = SWIG_ConvertPtr(swig_obj[3], &argp6,SWIGTYPE_p_orbitparams, 0 |  0 );
  if (!SWIG_IsOK(res6)) {
    SWIG_exception_fail(SWIG_ArgError(res6), "in method '" "dorbint" "', argument " "6"" of type '" "orbitparams *""'"); 
  }
  arg6 = (orbitparams *)(argp6);
  {
    errno = 0;
    wrap_dorbint(arg1,arg2,arg3,arg4,arg5,arg6);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_Py_Void();
  {
    npy_intp dims[1] = {
      *arg2 
    };
    PyObject* obj = PyArray_SimpleNewFromData(1, dims, NPY_DOUBLE, (void*)(*arg1));
    PyArrayObject* array = (PyArrayObject*) obj;
    
    if (!array) SWIG_fail;
    
#ifdef SWIGPY_USE_CAPSULE
    PyObject* cap = PyCapsule_New((void*)(*arg1), SWIGPY_CAPSULE_NAME, free_cap);
#else
    PyObject* cap = PyCObject_FromVoidPtr((void*)(*arg1), free);
#endif
    
#if NPY_API_VERSION < 0x00000007
    PyArray_BASE(array) = cap;
#else
    PyArray_SetBaseObject(array,cap);
#endif
    
    resultobj = SWIG_Python_AppendOutput(resultobj,obj);
  }
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_binary_velocity(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  double arg1 ;
  orbitparams *arg2 = (orbitparams *) 0 ;
  double *arg3 = (double *) 0 ;
  double *arg4 = (double *) 0 ;
  double val1 ;
  int ecode1 = 0 ;
  void *argp2 = 0 ;
  int res2 = 0 ;
  double temp3 ;
  int res3 = SWIG_TMPOBJ ;
  double temp4 ;
  int res4 = SWIG_TMPOBJ ;
  PyObject *swig_obj[2] ;
  
  arg3 = &temp3;
  arg4 = &temp4;
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "binary_velocity", 2, 2, swig_obj)) SWIG_fail;
  ecode1 = SWIG_AsVal_double(swig_obj[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "binary_velocity" "', argument " "1"" of type '" "double""'");
  } 
  arg1 = (double)(val1);
  res2 = SWIG_ConvertPtr(swig_obj[1], &argp2,SWIGTYPE_p_orbitparams, 0 |  0 );
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "binary_velocity" "', argument " "2"" of type '" "orbitparams *""'"); 
  }
  arg2 = (orbitparams *)(argp2);
  {
    errno = 0;
    binary_velocity(arg1,arg2,arg3,arg4);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_Py_Void();
  if (SWIG_IsTmpObj(res3)) {
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_From_double((*arg3)));
  } else {
    int new_flags = SWIG_IsNewObj(res3) ? (SWIG_POINTER_OWN |  0 ) :  0 ;
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_NewPointerObj((void*)(arg3), SWIGTYPE_p_double, new_flags));
  }
  if (SWIG_IsTmpObj(res4)) {
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_From_double((*arg4)));
  } else {
    int new_flags = SWIG_IsNewObj(res4) ? (SWIG_POINTER_OWN |  0 ) :  0 ;
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_NewPointerObj((void*)(arg4), SWIGTYPE_p_double, new_flags));
  }
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_r_resp_halfwidth(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  presto_interp_acc arg1 ;
  int val1 ;
  int ecode1 = 0 ;
  PyObject *swig_obj[1] ;
  int result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  ecode1 = SWIG_AsVal_int(swig_obj[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "r_resp_halfwidth" "', argument " "1"" of type '" "presto_interp_acc""'");
  } 
  arg1 = (presto_interp_acc)(val1);
  {
    errno = 0;
    result = (int)r_resp_halfwidth(arg1);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_From_int((int)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_z_resp_halfwidth(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  double arg1 ;
  presto_interp_acc arg2 ;
  double val1 ;
  int ecode1 = 0 ;
  int val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  int result;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "z_resp_halfwidth", 2, 2, swig_obj)) SWIG_fail;
  ecode1 = SWIG_AsVal_double(swig_obj[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "z_resp_halfwidth" "', argument " "1"" of type '" "double""'");
  } 
  arg1 = (double)(val1);
  ecode2 = SWIG_AsVal_int(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "z_resp_halfwidth" "', argument " "2"" of type '" "presto_interp_acc""'");
  } 
  arg2 = (presto_interp_acc)(val2);
  {
    errno = 0;
    result = (int)z_resp_halfwidth(arg1,arg2);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_From_int((int)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_w_resp_halfwidth(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  double arg1 ;
  double arg2 ;
  presto_interp_acc arg3 ;
  double val1 ;
  int ecode1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  int val3 ;
  int ecode3 = 0 ;
  PyObject *swig_obj[3] ;
  int result;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "w_resp_halfwidth", 3, 3, swig_obj)) SWIG_fail;
  ecode1 = SWIG_AsVal_double(swig_obj[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "w_resp_halfwidth" "', argument " "1"" of type '" "double""'");
  } 
  arg1 = (double)(val1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "w_resp_halfwidth" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  ecode3 = SWIG_AsVal_int(swig_obj[2], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "w_resp_halfwidth" "', argument " "3"" of type '" "presto_interp_acc""'");
  } 
  arg3 = (presto_interp_acc)(val3);
  {
    errno = 0;
    result = (int)w_resp_halfwidth(arg1,arg2,arg3);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_From_int((int)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_bin_resp_halfwidth(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  double arg1 ;
  double arg2 ;
  orbitparams *arg3 = (orbitparams *) 0 ;
  double val1 ;
  int ecode1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  void *argp3 = 0 ;
  int res3 = 0 ;
  PyObject *swig_obj[3] ;
  int result;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "bin_resp_halfwidth", 3, 3, swig_obj)) SWIG_fail;
  ecode1 = SWIG_AsVal_double(swig_obj[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "bin_resp_halfwidth" "', argument " "1"" of type '" "double""'");
  } 
  arg1 = (double)(val1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "bin_resp_halfwidth" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  res3 = SWIG_ConvertPtr(swig_obj[2], &argp3,SWIGTYPE_p_orbitparams, 0 |  0 );
  if (!SWIG_IsOK(res3)) {
    SWIG_exception_fail(SWIG_ArgError(res3), "in method '" "bin_resp_halfwidth" "', argument " "3"" of type '" "orbitparams *""'"); 
  }
  arg3 = (orbitparams *)(argp3);
  {
    errno = 0;
    result = (int)bin_resp_halfwidth(arg1,arg2,arg3);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_From_int((int)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_gen_r_response(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  double arg1 ;
  int arg2 ;
  int arg3 ;
  fcomplex **arg4 = (fcomplex **) 0 ;
  long *arg5 = (long *) 0 ;
  double val1 ;
  int ecode1 = 0 ;
  int val2 ;
  int ecode2 = 0 ;
  int val3 ;
  int ecode3 = 0 ;
  fcomplex *data_temp4 = NULL ;
  long dim_temp4 ;
  PyObject *swig_obj[3] ;
  
  {
    arg4 = &data_temp4;
    arg5 = &dim_temp4;
  }
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "gen_r_response", 3, 3, swig_obj)) SWIG_fail;
  ecode1 = SWIG_AsVal_double(swig_obj[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "gen_r_response" "', argument " "1"" of type '" "double""'");
  } 
  arg1 = (double)(val1);
  ecode2 = SWIG_AsVal_int(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "gen_r_response" "', argument " "2"" of type '" "int""'");
  } 
  arg2 = (int)(val2);
  ecode3 = SWIG_AsVal_int(swig_obj[2], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "gen_r_response" "', argument " "3"" of type '" "int""'");
  } 
  arg3 = (int)(val3);
  {
    errno = 0;
    wrap_gen_r_response(arg1,arg2,arg3,arg4,arg5);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_Py_Void();
  {
    npy_intp dims[1] = {
      *arg5 
    };
    PyObject* obj = PyArray_SimpleNewFromData(1, dims, NPY_CFLOAT, (void*)(*arg4));
    PyArrayObject* array = (PyArrayObject*) obj;
    
    if (!array) SWIG_fail;
    
#ifdef SWIGPY_USE_CAPSULE
    PyObject* cap = PyCapsule_New((void*)(*arg4), SWIGPY_CAPSULE_NAME, free_cap);
#else
    PyObject* cap = PyCObject_FromVoidPtr((void*)(*arg4), free);
#endif
    
#if NPY_API_VERSION < 0x00000007
    PyArray_BASE(array) = cap;
#else
    PyArray_SetBaseObject(array,cap);
#endif
    
    resultobj = SWIG_Python_AppendOutput(resultobj,obj);
  }
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_gen_z_response(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  double arg1 ;
  int arg2 ;
  int arg3 ;
  double arg4 ;
  fcomplex **arg5 = (fcomplex **) 0 ;
  long *arg6 = (long *) 0 ;
  double val1 ;
  int ecode1 = 0 ;
  int val2 ;
  int ecode2 = 0 ;
  int val3 ;
  int ecode3 = 0 ;
  double val4 ;
  int ecode4 = 0 ;
  fcomplex *data_temp5 = NULL ;
  long dim_temp5 ;
  PyObject *swig_obj[4] ;
  
  {
    arg5 = &data_temp5;
    arg6 = &dim_temp5;
  }
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "gen_z_response", 4, 4, swig_obj)) SWIG_fail;
  ecode1 = SWIG_AsVal_double(swig_obj[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "gen_z_response" "', argument " "1"" of type '" "double""'");
  } 
  arg1 = (double)(val1);
  ecode2 = SWIG_AsVal_int(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "gen_z_response" "', argument " "2"" of type '" "int""'");
  } 
  arg2 = (int)(val2);
  ecode3 = SWIG_AsVal_int(swig_obj[2], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "gen_z_response" "', argument " "3"" of type '" "int""'");
  } 
  arg3 = (int)(val3);
  ecode4 = SWIG_AsVal_double(swig_obj[3], &val4);
  if (!SWIG_IsOK(ecode4)) {
    SWIG_exception_fail(SWIG_ArgError(ecode4), "in method '" "gen_z_response" "', argument " "4"" of type '" "double""'");
  } 
  arg4 = (double)(val4);
  {
    errno = 0;
    wrap_gen_z_response(arg1,arg2,arg3,arg4,arg5,arg6);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_Py_Void();
  {
    npy_intp dims[1] = {
      *arg6 
    };
    PyObject* obj = PyArray_SimpleNewFromData(1, dims, NPY_CFLOAT, (void*)(*arg5));
    PyArrayObject* array = (PyArrayObject*) obj;
    
    if (!array) SWIG_fail;
    
#ifdef SWIGPY_USE_CAPSULE
    PyObject* cap = PyCapsule_New((void*)(*arg5), SWIGPY_CAPSULE_NAME, free_cap);
#else
    PyObject* cap = PyCObject_FromVoidPtr((void*)(*arg5), free);
#endif
    
#if NPY_API_VERSION < 0x00000007
    PyArray_BASE(array) = cap;
#else
    PyArray_SetBaseObject(array,cap);
#endif
    
    resultobj = SWIG_Python_AppendOutput(resultobj,obj);
  }
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_gen_w_response(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  double arg1 ;
  int arg2 ;
  int arg3 ;
  double arg4 ;
  double arg5 ;
  fcomplex **arg6 = (fcomplex **) 0 ;
  long *arg7 = (long *) 0 ;
  double val1 ;
  int ecode1 = 0 ;
  int val2 ;
  int ecode2 = 0 ;
  int val3 ;
  int ecode3 = 0 ;
  double val4 ;
  int ecode4 = 0 ;
  double val5 ;
  int ecode5 = 0 ;
  fcomplex *data_temp6 = NULL ;
  long dim_temp6 ;
  PyObject *swig_obj[5] ;
  
  {
    arg6 = &data_temp6;
    arg7 = &dim_temp6;
  }
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "gen_w_response", 5, 5, swig_obj)) SWIG_fail;
  ecode1 = SWIG_AsVal_double(swig_obj[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "gen_w_response" "', argument " "1"" of type '" "double""'");
  } 
  arg1 = (double)(val1);
  ecode2 = SWIG_AsVal_int(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "gen_w_response" "', argument " "2"" of type '" "int""'");
  } 
  arg2 = (int)(val2);
  ecode3 = SWIG_AsVal_int(swig_obj[2], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "gen_w_response" "', argument " "3"" of type '" "int""'");
  } 
  arg3 = (int)(val3);
  ecode4 = SWIG_AsVal_double(swig_obj[3], &val4);
  if (!SWIG_IsOK(ecode4)) {
    SWIG_exception_fail(SWIG_ArgError(ecode4), "in method '" "gen_w_response" "', argument " "4"" of type '" "double""'");
  } 
  arg4 = (double)(val4);
  ecode5 = SWIG_AsVal_double(swig_obj[4], &val5);
  if (!SWIG_IsOK(ecode5)) {
    SWIG_exception_fail(SWIG_ArgError(ecode5), "in method '" "gen_w_response" "', argument " "5"" of type '" "double""'");
  } 
  arg5 = (double)(val5);
  {
    errno = 0;
    wrap_gen_w_response(arg1,arg2,arg3,arg4,arg5,arg6,arg7);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_Py_Void();
  {
    npy_intp dims[1] = {
      *arg7 
    };
    PyObject* obj = PyArray_SimpleNewFromData(1, dims, NPY_CFLOAT, (void*)(*arg6));
    PyArrayObject* array = (PyArrayObject*) obj;
    
    if (!array) SWIG_fail;
    
#ifdef SWIGPY_USE_CAPSULE
    PyObject* cap = PyCapsule_New((void*)(*arg6), SWIGPY_CAPSULE_NAME, free_cap);
#else
    PyObject* cap = PyCObject_FromVoidPtr((void*)(*arg6), free);
#endif
    
#if NPY_API_VERSION < 0x00000007
    PyArray_BASE(array) = cap;
#else
    PyArray_SetBaseObject(array,cap);
#endif
    
    resultobj = SWIG_Python_AppendOutput(resultobj,obj);
  }
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_gen_w_response2(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  double arg1 ;
  int arg2 ;
  int arg3 ;
  double arg4 ;
  double arg5 ;
  fcomplex **arg6 = (fcomplex **) 0 ;
  long *arg7 = (long *) 0 ;
  double val1 ;
  int ecode1 = 0 ;
  int val2 ;
  int ecode2 = 0 ;
  int val3 ;
  int ecode3 = 0 ;
  double val4 ;
  int ecode4 = 0 ;
  double val5 ;
  int ecode5 = 0 ;
  fcomplex *data_temp6 = NULL ;
  long dim_temp6 ;
  PyObject *swig_obj[5] ;
  
  {
    arg6 = &data_temp6;
    arg7 = &dim_temp6;
  }
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "gen_w_response2", 5, 5, swig_obj)) SWIG_fail;
  ecode1 = SWIG_AsVal_double(swig_obj[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "gen_w_response2" "', argument " "1"" of type '" "double""'");
  } 
  arg1 = (double)(val1);
  ecode2 = SWIG_AsVal_int(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "gen_w_response2" "', argument " "2"" of type '" "int""'");
  } 
  arg2 = (int)(val2);
  ecode3 = SWIG_AsVal_int(swig_obj[2], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "gen_w_response2" "', argument " "3"" of type '" "int""'");
  } 
  arg3 = (int)(val3);
  ecode4 = SWIG_AsVal_double(swig_obj[3], &val4);
  if (!SWIG_IsOK(ecode4)) {
    SWIG_exception_fail(SWIG_ArgError(ecode4), "in method '" "gen_w_response2" "', argument " "4"" of type '" "double""'");
  } 
  arg4 = (double)(val4);
  ecode5 = SWIG_AsVal_double(swig_obj[4], &val5);
  if (!SWIG_IsOK(ecode5)) {
    SWIG_exception_fail(SWIG_ArgError(ecode5), "in method '" "gen_w_response2" "', argument " "5"" of type '" "double""'");
  } 
  arg5 = (double)(val5);
  {
    errno = 0;
    wrap_gen_w_response2(arg1,arg2,arg3,arg4,arg5,arg6,arg7);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_Py_Void();
  {
    npy_intp dims[1] = {
      *arg7 
    };
    PyObject* obj = PyArray_SimpleNewFromData(1, dims, NPY_CFLOAT, (void*)(*arg6));
    PyArrayObject* array = (PyArrayObject*) obj;
    
    if (!array) SWIG_fail;
    
#ifdef SWIGPY_USE_CAPSULE
    PyObject* cap = PyCapsule_New((void*)(*arg6), SWIGPY_CAPSULE_NAME, free_cap);
#else
    PyObject* cap = PyCObject_FromVoidPtr((void*)(*arg6), free);
#endif
    
#if NPY_API_VERSION < 0x00000007
    PyArray_BASE(array) = cap;
#else
    PyArray_SetBaseObject(array,cap);
#endif
    
    resultobj = SWIG_Python_AppendOutput(resultobj,obj);
  }
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_gen_bin_response(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  double arg1 ;
  int arg2 ;
  int arg3 ;
  double arg4 ;
  double arg5 ;
  orbitparams *arg6 = (orbitparams *) 0 ;
  fcomplex **arg7 = (fcomplex **) 0 ;
  long *arg8 = (long *) 0 ;
  double val1 ;
  int ecode1 = 0 ;
  int val2 ;
  int ecode2 = 0 ;
  int val3 ;
  int ecode3 = 0 ;
  double val4 ;
  int ecode4 = 0 ;
  double val5 ;
  int ecode5 = 0 ;
  void *argp6 = 0 ;
  int res6 = 0 ;
  fcomplex *data_temp7 = NULL ;
  long dim_temp7 ;
  PyObject *swig_obj[6] ;
  
  {
    arg7 = &data_temp7;
    arg8 = &dim_temp7;
  }
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "gen_bin_response", 6, 6, swig_obj)) SWIG_fail;
  ecode1 = SWIG_AsVal_double(swig_obj[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "gen_bin_response" "', argument " "1"" of type '" "double""'");
  } 
  arg1 = (double)(val1);
  ecode2 = SWIG_AsVal_int(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "gen_bin_response" "', argument " "2"" of type '" "int""'");
  } 
  arg2 = (int)(val2);
  ecode3 = SWIG_AsVal_int(swig_obj[2], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "gen_bin_response" "', argument " "3"" of type '" "int""'");
  } 
  arg3 = (int)(val3);
  ecode4 = SWIG_AsVal_double(swig_obj[3], &val4);
  if (!SWIG_IsOK(ecode4)) {
    SWIG_exception_fail(SWIG_ArgError(ecode4), "in method '" "gen_bin_response" "', argument " "4"" of type '" "double""'");
  } 
  arg4 = (double)(val4);
  ecode5 = SWIG_AsVal_double(swig_obj[4], &val5);
  if (!SWIG_IsOK(ecode5)) {
    SWIG_exception_fail(SWIG_ArgError(ecode5), "in method '" "gen_bin_response" "', argument " "5"" of type '" "double""'");
  } 
  arg5 = (double)(val5);
  res6 = SWIG_ConvertPtr(swig_obj[5], &argp6,SWIGTYPE_p_orbitparams, 0 |  0 );
  if (!SWIG_IsOK(res6)) {
    SWIG_exception_fail(SWIG_ArgError(res6), "in method '" "gen_bin_response" "', argument " "6"" of type '" "orbitparams *""'"); 
  }
  arg6 = (orbitparams *)(argp6);
  {
    errno = 0;
    wrap_gen_bin_response(arg1,arg2,arg3,arg4,arg5,arg6,arg7,arg8);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_Py_Void();
  {
    npy_intp dims[1] = {
      *arg8 
    };
    PyObject* obj = PyArray_SimpleNewFromData(1, dims, NPY_CFLOAT, (void*)(*arg7));
    PyArrayObject* array = (PyArrayObject*) obj;
    
    if (!array) SWIG_fail;
    
#ifdef SWIGPY_USE_CAPSULE
    PyObject* cap = PyCapsule_New((void*)(*arg7), SWIGPY_CAPSULE_NAME, free_cap);
#else
    PyObject* cap = PyCObject_FromVoidPtr((void*)(*arg7), free);
#endif
    
#if NPY_API_VERSION < 0x00000007
    PyArray_BASE(array) = cap;
#else
    PyArray_SetBaseObject(array,cap);
#endif
    
    resultobj = SWIG_Python_AppendOutput(resultobj,obj);
  }
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_get_localpower(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  fcomplex *arg1 = (fcomplex *) 0 ;
  long arg2 ;
  double arg3 ;
  PyArrayObject *array1 = NULL ;
  int is_new_object1 = 0 ;
  double val3 ;
  int ecode3 = 0 ;
  PyObject *swig_obj[2] ;
  float result;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "get_localpower", 2, 2, swig_obj)) SWIG_fail;
  {
    npy_intp size[1] = {
      -1 
    };
    array1 = obj_to_array_contiguous_allow_conversion(swig_obj[0],
      NPY_CFLOAT,
      &is_new_object1);
    if (!array1 || !require_dimensions(array1, 1) ||
      !require_size(array1, size, 1)) SWIG_fail;
    arg1 = (fcomplex*) array_data(array1);
    arg2 = (long) array_size(array1,0);
  }
  ecode3 = SWIG_AsVal_double(swig_obj[1], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "get_localpower" "', argument " "3"" of type '" "double""'");
  } 
  arg3 = (double)(val3);
  {
    errno = 0;
    result = (float)get_localpower(arg1,arg2,arg3);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_From_float((float)(result));
  {
    if (is_new_object1 && array1)
    {
      Py_DECREF(array1); 
    }
  }
  return resultobj;
fail:
  {
    if (is_new_object1 && array1)
    {
      Py_DECREF(array1); 
    }
  }
  return NULL;
}


SWIGINTERN PyObject *_wrap_get_localpower3d(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  fcomplex *arg1 = (fcomplex *) 0 ;
  long arg2 ;
  double arg3 ;
  double arg4 ;
  double arg5 ;
  PyArrayObject *array1 = NULL ;
  int is_new_object1 = 0 ;
  double val3 ;
  int ecode3 = 0 ;
  double val4 ;
  int ecode4 = 0 ;
  double val5 ;
  int ecode5 = 0 ;
  PyObject *swig_obj[4] ;
  float result;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "get_localpower3d", 4, 4, swig_obj)) SWIG_fail;
  {
    npy_intp size[1] = {
      -1 
    };
    array1 = obj_to_array_contiguous_allow_conversion(swig_obj[0],
      NPY_CFLOAT,
      &is_new_object1);
    if (!array1 || !require_dimensions(array1, 1) ||
      !require_size(array1, size, 1)) SWIG_fail;
    arg1 = (fcomplex*) array_data(array1);
    arg2 = (long) array_size(array1,0);
  }
  ecode3 = SWIG_AsVal_double(swig_obj[1], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "get_localpower3d" "', argument " "3"" of type '" "double""'");
  } 
  arg3 = (double)(val3);
  ecode4 = SWIG_AsVal_double(swig_obj[2], &val4);
  if (!SWIG_IsOK(ecode4)) {
    SWIG_exception_fail(SWIG_ArgError(ecode4), "in method '" "get_localpower3d" "', argument " "4"" of type '" "double""'");
  } 
  arg4 = (double)(val4);
  ecode5 = SWIG_AsVal_double(swig_obj[3], &val5);
  if (!SWIG_IsOK(ecode5)) {
    SWIG_exception_fail(SWIG_ArgError(ecode5), "in method '" "get_localpower3d" "', argument " "5"" of type '" "double""'");
  } 
  arg5 = (double)(val5);
  {
    errno = 0;
    result = (float)get_localpower3d(arg1,arg2,arg3,arg4,arg5);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_From_float((float)(result));
  {
    if (is_new_object1 && array1)
    {
      Py_DECREF(array1); 
    }
  }
  return resultobj;
fail:
  {
    if (is_new_object1 && array1)
    {
      Py_DECREF(array1); 
    }
  }
  return NULL;
}


SWIGINTERN PyObject *_wrap_get_derivs3d(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  fcomplex *arg1 = (fcomplex *) 0 ;
  long arg2 ;
  double arg3 ;
  double arg4 ;
  double arg5 ;
  float arg6 ;
  rderivs *arg7 = (rderivs *) 0 ;
  PyArrayObject *array1 = NULL ;
  int is_new_object1 = 0 ;
  double val3 ;
  int ecode3 = 0 ;
  double val4 ;
  int ecode4 = 0 ;
  double val5 ;
  int ecode5 = 0 ;
  float val6 ;
  int ecode6 = 0 ;
  void *argp7 = 0 ;
  int res7 = 0 ;
  PyObject *swig_obj[6] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "get_derivs3d", 6, 6, swig_obj)) SWIG_fail;
  {
    npy_intp size[1] = {
      -1 
    };
    array1 = obj_to_array_contiguous_allow_conversion(swig_obj[0],
      NPY_CFLOAT,
      &is_new_object1);
    if (!array1 || !require_dimensions(array1, 1) ||
      !require_size(array1, size, 1)) SWIG_fail;
    arg1 = (fcomplex*) array_data(array1);
    arg2 = (long) array_size(array1,0);
  }
  ecode3 = SWIG_AsVal_double(swig_obj[1], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "get_derivs3d" "', argument " "3"" of type '" "double""'");
  } 
  arg3 = (double)(val3);
  ecode4 = SWIG_AsVal_double(swig_obj[2], &val4);
  if (!SWIG_IsOK(ecode4)) {
    SWIG_exception_fail(SWIG_ArgError(ecode4), "in method '" "get_derivs3d" "', argument " "4"" of type '" "double""'");
  } 
  arg4 = (double)(val4);
  ecode5 = SWIG_AsVal_double(swig_obj[3], &val5);
  if (!SWIG_IsOK(ecode5)) {
    SWIG_exception_fail(SWIG_ArgError(ecode5), "in method '" "get_derivs3d" "', argument " "5"" of type '" "double""'");
  } 
  arg5 = (double)(val5);
  ecode6 = SWIG_AsVal_float(swig_obj[4], &val6);
  if (!SWIG_IsOK(ecode6)) {
    SWIG_exception_fail(SWIG_ArgError(ecode6), "in method '" "get_derivs3d" "', argument " "6"" of type '" "float""'");
  } 
  arg6 = (float)(val6);
  res7 = SWIG_ConvertPtr(swig_obj[5], &argp7,SWIGTYPE_p_RDERIVS, 0 |  0 );
  if (!SWIG_IsOK(res7)) {
    SWIG_exception_fail(SWIG_ArgError(res7), "in method '" "get_derivs3d" "', argument " "7"" of type '" "rderivs *""'"); 
  }
  arg7 = (rderivs *)(argp7);
  {
    errno = 0;
    get_derivs3d(arg1,arg2,arg3,arg4,arg5,arg6,arg7);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_Py_Void();
  {
    if (is_new_object1 && array1)
    {
      Py_DECREF(array1); 
    }
  }
  return resultobj;
fail:
  {
    if (is_new_object1 && array1)
    {
      Py_DECREF(array1); 
    }
  }
  return NULL;
}


SWIGINTERN PyObject *_wrap_calc_props(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  rderivs arg1 ;
  double arg2 ;
  double arg3 ;
  double arg4 ;
  fourierprops *arg5 = (fourierprops *) 0 ;
  void *argp1 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  double val3 ;
  int ecode3 = 0 ;
  double val4 ;
  int ecode4 = 0 ;
  void *argp5 = 0 ;
  int res5 = 0 ;
  PyObject *swig_obj[5] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "calc_props", 5, 5, swig_obj)) SWIG_fail;
  {
    res1 = SWIG_ConvertPtr(swig_obj[0], &argp1, SWIGTYPE_p_RDERIVS,  0 );
    if (!SWIG_IsOK(res1)) {
      SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "calc_props" "', argument " "1"" of type '" "rderivs""'"); 
    }  
    if (!argp1) {
      SWIG_exception_fail(SWIG_ValueError, "invalid null reference " "in method '" "calc_props" "', argument " "1"" of type '" "rderivs""'");
    } else {
      arg1 = *((rderivs *)(argp1));
    }
  }
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "calc_props" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  ecode3 = SWIG_AsVal_double(swig_obj[2], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "calc_props" "', argument " "3"" of type '" "double""'");
  } 
  arg3 = (double)(val3);
  ecode4 = SWIG_AsVal_double(swig_obj[3], &val4);
  if (!SWIG_IsOK(ecode4)) {
    SWIG_exception_fail(SWIG_ArgError(ecode4), "in method '" "calc_props" "', argument " "4"" of type '" "double""'");
  } 
  arg4 = (double)(val4);
  res5 = SWIG_ConvertPtr(swig_obj[4], &argp5,SWIGTYPE_p_FOURIERPROPS, 0 |  0 );
  if (!SWIG_IsOK(res5)) {
    SWIG_exception_fail(SWIG_ArgError(res5), "in method '" "calc_props" "', argument " "5"" of type '" "fourierprops *""'"); 
  }
  arg5 = (fourierprops *)(argp5);
  {
    errno = 0;
    calc_props(arg1,arg2,arg3,arg4,arg5);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_calc_binprops(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  fourierprops *arg1 = (fourierprops *) 0 ;
  double arg2 ;
  int arg3 ;
  int arg4 ;
  binaryprops *arg5 = (binaryprops *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  int val3 ;
  int ecode3 = 0 ;
  int val4 ;
  int ecode4 = 0 ;
  void *argp5 = 0 ;
  int res5 = 0 ;
  PyObject *swig_obj[5] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "calc_binprops", 5, 5, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_FOURIERPROPS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "calc_binprops" "', argument " "1"" of type '" "fourierprops *""'"); 
  }
  arg1 = (fourierprops *)(argp1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "calc_binprops" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  ecode3 = SWIG_AsVal_int(swig_obj[2], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "calc_binprops" "', argument " "3"" of type '" "int""'");
  } 
  arg3 = (int)(val3);
  ecode4 = SWIG_AsVal_int(swig_obj[3], &val4);
  if (!SWIG_IsOK(ecode4)) {
    SWIG_exception_fail(SWIG_ArgError(ecode4), "in method '" "calc_binprops" "', argument " "4"" of type '" "int""'");
  } 
  arg4 = (int)(val4);
  res5 = SWIG_ConvertPtr(swig_obj[4], &argp5,SWIGTYPE_p_binaryprops, 0 |  0 );
  if (!SWIG_IsOK(res5)) {
    SWIG_exception_fail(SWIG_ArgError(res5), "in method '" "calc_binprops" "', argument " "5"" of type '" "binaryprops *""'"); 
  }
  arg5 = (binaryprops *)(argp5);
  {
    errno = 0;
    calc_binprops(arg1,arg2,arg3,arg4,arg5);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_calc_rzwerrs(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  fourierprops *arg1 = (fourierprops *) 0 ;
  double arg2 ;
  rzwerrs *arg3 = (rzwerrs *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  void *argp3 = 0 ;
  int res3 = 0 ;
  PyObject *swig_obj[3] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "calc_rzwerrs", 3, 3, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_FOURIERPROPS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "calc_rzwerrs" "', argument " "1"" of type '" "fourierprops *""'"); 
  }
  arg1 = (fourierprops *)(argp1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "calc_rzwerrs" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  res3 = SWIG_ConvertPtr(swig_obj[2], &argp3,SWIGTYPE_p_rzwerrs, 0 |  0 );
  if (!SWIG_IsOK(res3)) {
    SWIG_exception_fail(SWIG_ArgError(res3), "in method '" "calc_rzwerrs" "', argument " "3"" of type '" "rzwerrs *""'"); 
  }
  arg3 = (rzwerrs *)(argp3);
  {
    errno = 0;
    calc_rzwerrs(arg1,arg2,arg3);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_extended_equiv_gaussian_sigma(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  double arg1 ;
  double val1 ;
  int ecode1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  ecode1 = SWIG_AsVal_double(swig_obj[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "extended_equiv_gaussian_sigma" "', argument " "1"" of type '" "double""'");
  } 
  arg1 = (double)(val1);
  {
    errno = 0;
    result = (double)extended_equiv_gaussian_sigma(arg1);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_log_asymtotic_incomplete_gamma(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  double arg1 ;
  double arg2 ;
  double val1 ;
  int ecode1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  double result;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "log_asymtotic_incomplete_gamma", 2, 2, swig_obj)) SWIG_fail;
  ecode1 = SWIG_AsVal_double(swig_obj[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "log_asymtotic_incomplete_gamma" "', argument " "1"" of type '" "double""'");
  } 
  arg1 = (double)(val1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "log_asymtotic_incomplete_gamma" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  {
    errno = 0;
    result = (double)log_asymtotic_incomplete_gamma(arg1,arg2);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_log_asymtotic_gamma(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  double arg1 ;
  double val1 ;
  int ecode1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  ecode1 = SWIG_AsVal_double(swig_obj[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "log_asymtotic_gamma" "', argument " "1"" of type '" "double""'");
  } 
  arg1 = (double)(val1);
  {
    errno = 0;
    result = (double)log_asymtotic_gamma(arg1);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_equivalent_gaussian_sigma(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  double arg1 ;
  double val1 ;
  int ecode1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  ecode1 = SWIG_AsVal_double(swig_obj[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "equivalent_gaussian_sigma" "', argument " "1"" of type '" "double""'");
  } 
  arg1 = (double)(val1);
  {
    errno = 0;
    result = (double)equivalent_gaussian_sigma(arg1);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_chi2_logp(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  double arg1 ;
  double arg2 ;
  double val1 ;
  int ecode1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  double result;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "chi2_logp", 2, 2, swig_obj)) SWIG_fail;
  ecode1 = SWIG_AsVal_double(swig_obj[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "chi2_logp" "', argument " "1"" of type '" "double""'");
  } 
  arg1 = (double)(val1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "chi2_logp" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  {
    errno = 0;
    result = (double)chi2_logp(arg1,arg2);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_chi2_sigma(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  double arg1 ;
  double arg2 ;
  double val1 ;
  int ecode1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  double result;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "chi2_sigma", 2, 2, swig_obj)) SWIG_fail;
  ecode1 = SWIG_AsVal_double(swig_obj[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "chi2_sigma" "', argument " "1"" of type '" "double""'");
  } 
  arg1 = (double)(val1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "chi2_sigma" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  {
    errno = 0;
    result = (double)chi2_sigma(arg1,arg2);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_candidate_sigma(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  double arg1 ;
  int arg2 ;
  double arg3 ;
  double val1 ;
  int ecode1 = 0 ;
  int val2 ;
  int ecode2 = 0 ;
  double val3 ;
  int ecode3 = 0 ;
  PyObject *swig_obj[3] ;
  double result;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "candidate_sigma", 3, 3, swig_obj)) SWIG_fail;
  ecode1 = SWIG_AsVal_double(swig_obj[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "candidate_sigma" "', argument " "1"" of type '" "double""'");
  } 
  arg1 = (double)(val1);
  ecode2 = SWIG_AsVal_int(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "candidate_sigma" "', argument " "2"" of type '" "int""'");
  } 
  arg2 = (int)(val2);
  ecode3 = SWIG_AsVal_double(swig_obj[2], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "candidate_sigma" "', argument " "3"" of type '" "double""'");
  } 
  arg3 = (double)(val3);
  {
    errno = 0;
    result = (double)candidate_sigma(arg1,arg2,arg3);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_power_for_sigma(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  double arg1 ;
  int arg2 ;
  double arg3 ;
  double val1 ;
  int ecode1 = 0 ;
  int val2 ;
  int ecode2 = 0 ;
  double val3 ;
  int ecode3 = 0 ;
  PyObject *swig_obj[3] ;
  double result;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "power_for_sigma", 3, 3, swig_obj)) SWIG_fail;
  ecode1 = SWIG_AsVal_double(swig_obj[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "power_for_sigma" "', argument " "1"" of type '" "double""'");
  } 
  arg1 = (double)(val1);
  ecode2 = SWIG_AsVal_int(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "power_for_sigma" "', argument " "2"" of type '" "int""'");
  } 
  arg2 = (int)(val2);
  ecode3 = SWIG_AsVal_double(swig_obj[2], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "power_for_sigma" "', argument " "3"" of type '" "double""'");
  } 
  arg3 = (double)(val3);
  {
    errno = 0;
    result = (double)power_for_sigma(arg1,arg2,arg3);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_switch_f_and_p(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  double arg1 ;
  double arg2 ;
  double arg3 ;
  double *arg4 = (double *) 0 ;
  double *arg5 = (double *) 0 ;
  double *arg6 = (double *) 0 ;
  double val1 ;
  int ecode1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  double val3 ;
  int ecode3 = 0 ;
  double temp4 ;
  int res4 = SWIG_TMPOBJ ;
  double temp5 ;
  int res5 = SWIG_TMPOBJ ;
  double temp6 ;
  int res6 = SWIG_TMPOBJ ;
  PyObject *swig_obj[3] ;
  
  arg4 = &temp4;
  arg5 = &temp5;
  arg6 = &temp6;
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "switch_f_and_p", 3, 3, swig_obj)) SWIG_fail;
  ecode1 = SWIG_AsVal_double(swig_obj[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "switch_f_and_p" "', argument " "1"" of type '" "double""'");
  } 
  arg1 = (double)(val1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "switch_f_and_p" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  ecode3 = SWIG_AsVal_double(swig_obj[2], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "switch_f_and_p" "', argument " "3"" of type '" "double""'");
  } 
  arg3 = (double)(val3);
  {
    errno = 0;
    switch_f_and_p(arg1,arg2,arg3,arg4,arg5,arg6);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_Py_Void();
  if (SWIG_IsTmpObj(res4)) {
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_From_double((*arg4)));
  } else {
    int new_flags = SWIG_IsNewObj(res4) ? (SWIG_POINTER_OWN |  0 ) :  0 ;
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_NewPointerObj((void*)(arg4), SWIGTYPE_p_double, new_flags));
  }
  if (SWIG_IsTmpObj(res5)) {
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_From_double((*arg5)));
  } else {
    int new_flags = SWIG_IsNewObj(res5) ? (SWIG_POINTER_OWN |  0 ) :  0 ;
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_NewPointerObj((void*)(arg5), SWIGTYPE_p_double, new_flags));
  }
  if (SWIG_IsTmpObj(res6)) {
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_From_double((*arg6)));
  } else {
    int new_flags = SWIG_IsNewObj(res6) ? (SWIG_POINTER_OWN |  0 ) :  0 ;
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_NewPointerObj((void*)(arg6), SWIGTYPE_p_double, new_flags));
  }
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_chisqr(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  double *arg1 = (double *) 0 ;
  int arg2 ;
  double arg3 ;
  double arg4 ;
  PyArrayObject *array1 = NULL ;
  int is_new_object1 = 0 ;
  double val3 ;
  int ecode3 = 0 ;
  double val4 ;
  int ecode4 = 0 ;
  PyObject *swig_obj[3] ;
  double result;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "chisqr", 3, 3, swig_obj)) SWIG_fail;
  {
    npy_intp size[1] = {
      -1 
    };
    array1 = obj_to_array_contiguous_allow_conversion(swig_obj[0],
      NPY_DOUBLE,
      &is_new_object1);
    if (!array1 || !require_dimensions(array1, 1) ||
      !require_size(array1, size, 1)) SWIG_fail;
    arg1 = (double*) array_data(array1);
    arg2 = (int) array_size(array1,0);
  }
  ecode3 = SWIG_AsVal_double(swig_obj[1], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "chisqr" "', argument " "3"" of type '" "double""'");
  } 
  arg3 = (double)(val3);
  ecode4 = SWIG_AsVal_double(swig_obj[2], &val4);
  if (!SWIG_IsOK(ecode4)) {
    SWIG_exception_fail(SWIG_ArgError(ecode4), "in method '" "chisqr" "', argument " "4"" of type '" "double""'");
  } 
  arg4 = (double)(val4);
  {
    errno = 0;
    result = (double)chisqr(arg1,arg2,arg3,arg4);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_From_double((double)(result));
  {
    if (is_new_object1 && array1)
    {
      Py_DECREF(array1); 
    }
  }
  return resultobj;
fail:
  {
    if (is_new_object1 && array1)
    {
      Py_DECREF(array1); 
    }
  }
  return NULL;
}


SWIGINTERN PyObject *_wrap_z2n(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  double *arg1 = (double *) 0 ;
  int arg2 ;
  double arg3 ;
  int arg4 ;
  PyArrayObject *array1 = NULL ;
  int is_new_object1 = 0 ;
  double val3 ;
  int ecode3 = 0 ;
  int val4 ;
  int ecode4 = 0 ;
  PyObject *swig_obj[3] ;
  double result;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "z2n", 3, 3, swig_obj)) SWIG_fail;
  {
    npy_intp size[1] = {
      -1 
    };
    array1 = obj_to_array_contiguous_allow_conversion(swig_obj[0],
      NPY_DOUBLE,
      &is_new_object1);
    if (!array1 || !require_dimensions(array1, 1) ||
      !require_size(array1, size, 1)) SWIG_fail;
    arg1 = (double*) array_data(array1);
    arg2 = (int) array_size(array1,0);
  }
  ecode3 = SWIG_AsVal_double(swig_obj[1], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "z2n" "', argument " "3"" of type '" "double""'");
  } 
  arg3 = (double)(val3);
  ecode4 = SWIG_AsVal_int(swig_obj[2], &val4);
  if (!SWIG_IsOK(ecode4)) {
    SWIG_exception_fail(SWIG_ArgError(ecode4), "in method '" "z2n" "', argument " "4"" of type '" "int""'");
  } 
  arg4 = (int)(val4);
  {
    errno = 0;
    result = (double)z2n(arg1,arg2,arg3,arg4);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_From_double((double)(result));
  {
    if (is_new_object1 && array1)
    {
      Py_DECREF(array1); 
    }
  }
  return resultobj;
fail:
  {
    if (is_new_object1 && array1)
    {
      Py_DECREF(array1); 
    }
  }
  return NULL;
}


SWIGINTERN PyObject *_wrap_print_candidate(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  fourierprops *arg1 = (fourierprops *) 0 ;
  double arg2 ;
  unsigned long arg3 ;
  float arg4 ;
  int arg5 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  unsigned long val3 ;
  int ecode3 = 0 ;
  float val4 ;
  int ecode4 = 0 ;
  int val5 ;
  int ecode5 = 0 ;
  PyObject *swig_obj[5] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "print_candidate", 5, 5, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_FOURIERPROPS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "print_candidate" "', argument " "1"" of type '" "fourierprops *""'"); 
  }
  arg1 = (fourierprops *)(argp1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "print_candidate" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  ecode3 = SWIG_AsVal_unsigned_SS_long(swig_obj[2], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "print_candidate" "', argument " "3"" of type '" "unsigned long""'");
  } 
  arg3 = (unsigned long)(val3);
  ecode4 = SWIG_AsVal_float(swig_obj[3], &val4);
  if (!SWIG_IsOK(ecode4)) {
    SWIG_exception_fail(SWIG_ArgError(ecode4), "in method '" "print_candidate" "', argument " "4"" of type '" "float""'");
  } 
  arg4 = (float)(val4);
  ecode5 = SWIG_AsVal_int(swig_obj[4], &val5);
  if (!SWIG_IsOK(ecode5)) {
    SWIG_exception_fail(SWIG_ArgError(ecode5), "in method '" "print_candidate" "', argument " "5"" of type '" "int""'");
  } 
  arg5 = (int)(val5);
  {
    errno = 0;
    print_candidate(arg1,arg2,arg3,arg4,arg5);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_print_bin_candidate(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  binaryprops *arg1 = (binaryprops *) 0 ;
  int arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  int val2 ;
  int ecode2 = 0 ;
  PyObject *swig_obj[2] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "print_bin_candidate", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_binaryprops, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "print_bin_candidate" "', argument " "1"" of type '" "binaryprops *""'"); 
  }
  arg1 = (binaryprops *)(argp1);
  ecode2 = SWIG_AsVal_int(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "print_bin_candidate" "', argument " "2"" of type '" "int""'");
  } 
  arg2 = (int)(val2);
  {
    errno = 0;
    print_bin_candidate(arg1,arg2);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_Py_Void();
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_fopen(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  char *arg1 = (char *) 0 ;
  char *arg2 = (char *) 0 ;
  int res1 ;
  char *buf1 = 0 ;
  int alloc1 = 0 ;
  int res2 ;
  char *buf2 = 0 ;
  int alloc2 = 0 ;
  PyObject *swig_obj[2] ;
  FILE *result = 0 ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "fopen", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_AsCharPtrAndSize(swig_obj[0], &buf1, NULL, &alloc1);
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "fopen" "', argument " "1"" of type '" "char const *""'");
  }
  arg1 = (char *)(buf1);
  res2 = SWIG_AsCharPtrAndSize(swig_obj[1], &buf2, NULL, &alloc2);
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "fopen" "', argument " "2"" of type '" "char const *""'");
  }
  arg2 = (char *)(buf2);
  {
    errno = 0;
    result = (FILE *)fopen((char const *)arg1,(char const *)arg2);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p_FILE, 0 |  0 );
  if (alloc1 == SWIG_NEWOBJ) free((char*)buf1);
  if (alloc2 == SWIG_NEWOBJ) free((char*)buf2);
  return resultobj;
fail:
  if (alloc1 == SWIG_NEWOBJ) free((char*)buf1);
  if (alloc2 == SWIG_NEWOBJ) free((char*)buf2);
  return NULL;
}


SWIGINTERN PyObject *_wrap_fputs(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  char *arg1 = (char *) 0 ;
  FILE *arg2 = (FILE *) 0 ;
  int res1 ;
  char *buf1 = 0 ;
  int alloc1 = 0 ;
  void *argp2 = 0 ;
  int res2 = 0 ;
  PyObject *swig_obj[2] ;
  int result;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "fputs", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_AsCharPtrAndSize(swig_obj[0], &buf1, NULL, &alloc1);
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "fputs" "', argument " "1"" of type '" "char const *""'");
  }
  arg1 = (char *)(buf1);
  res2 = SWIG_ConvertPtr(swig_obj[1], &argp2,SWIGTYPE_p_FILE, 0 |  0 );
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "fputs" "', argument " "2"" of type '" "FILE *""'"); 
  }
  arg2 = (FILE *)(argp2);
  {
    errno = 0;
    result = (int)fputs((char const *)arg1,arg2);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_From_int((int)(result));
  if (alloc1 == SWIG_NEWOBJ) free((char*)buf1);
  return resultobj;
fail:
  if (alloc1 == SWIG_NEWOBJ) free((char*)buf1);
  return NULL;
}


SWIGINTERN PyObject *_wrap_fclose(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  FILE *arg1 = (FILE *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  PyObject *swig_obj[1] ;
  int result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_FILE, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "fclose" "', argument " "1"" of type '" "FILE *""'"); 
  }
  arg1 = (FILE *)(argp1);
  {
    errno = 0;
    result = (int)fclose(arg1);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_From_int((int)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_fseek(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  FILE *arg1 = (FILE *) 0 ;
  long arg2 ;
  int arg3 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  long val2 ;
  int ecode2 = 0 ;
  int val3 ;
  int ecode3 = 0 ;
  PyObject *swig_obj[3] ;
  int result;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "fseek", 3, 3, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_FILE, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "fseek" "', argument " "1"" of type '" "FILE *""'"); 
  }
  arg1 = (FILE *)(argp1);
  ecode2 = SWIG_AsVal_long(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "fseek" "', argument " "2"" of type '" "long""'");
  } 
  arg2 = (long)(val2);
  ecode3 = SWIG_AsVal_int(swig_obj[2], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "fseek" "', argument " "3"" of type '" "int""'");
  } 
  arg3 = (int)(val3);
  {
    errno = 0;
    result = (int)fseek(arg1,arg2,arg3);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_From_int((int)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_read_rzw_cand(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  FILE *arg1 = (FILE *) 0 ;
  fourierprops *arg2 = (fourierprops *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  void *argp2 = 0 ;
  int res2 = 0 ;
  PyObject *swig_obj[2] ;
  int result;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "read_rzw_cand", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_FILE, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "read_rzw_cand" "', argument " "1"" of type '" "FILE *""'"); 
  }
  arg1 = (FILE *)(argp1);
  res2 = SWIG_ConvertPtr(swig_obj[1], &argp2,SWIGTYPE_p_FOURIERPROPS, 0 |  0 );
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "read_rzw_cand" "', argument " "2"" of type '" "fourierprops *""'"); 
  }
  arg2 = (fourierprops *)(argp2);
  {
    errno = 0;
    result = (int)read_rzw_cand(arg1,arg2);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_From_int((int)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_get_rzw_cand(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  char *arg1 = (char *) 0 ;
  int arg2 ;
  fourierprops *arg3 = (fourierprops *) 0 ;
  int res1 ;
  char *buf1 = 0 ;
  int alloc1 = 0 ;
  int val2 ;
  int ecode2 = 0 ;
  void *argp3 = 0 ;
  int res3 = 0 ;
  PyObject *swig_obj[3] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "get_rzw_cand", 3, 3, swig_obj)) SWIG_fail;
  res1 = SWIG_AsCharPtrAndSize(swig_obj[0], &buf1, NULL, &alloc1);
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "get_rzw_cand" "', argument " "1"" of type '" "char *""'");
  }
  arg1 = (char *)(buf1);
  ecode2 = SWIG_AsVal_int(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "get_rzw_cand" "', argument " "2"" of type '" "int""'");
  } 
  arg2 = (int)(val2);
  res3 = SWIG_ConvertPtr(swig_obj[2], &argp3,SWIGTYPE_p_FOURIERPROPS, 0 |  0 );
  if (!SWIG_IsOK(res3)) {
    SWIG_exception_fail(SWIG_ArgError(res3), "in method '" "get_rzw_cand" "', argument " "3"" of type '" "fourierprops *""'"); 
  }
  arg3 = (fourierprops *)(argp3);
  {
    errno = 0;
    get_rzw_cand(arg1,arg2,arg3);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_Py_Void();
  if (alloc1 == SWIG_NEWOBJ) free((char*)buf1);
  return resultobj;
fail:
  if (alloc1 == SWIG_NEWOBJ) free((char*)buf1);
  return NULL;
}


SWIGINTERN PyObject *_wrap_read_bin_cand(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  FILE *arg1 = (FILE *) 0 ;
  binaryprops *arg2 = (binaryprops *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  void *argp2 = 0 ;
  int res2 = 0 ;
  PyObject *swig_obj[2] ;
  int result;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "read_bin_cand", 2, 2, swig_obj)) SWIG_fail;
  res1 = SWIG_ConvertPtr(swig_obj[0], &argp1,SWIGTYPE_p_FILE, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "read_bin_cand" "', argument " "1"" of type '" "FILE *""'"); 
  }
  arg1 = (FILE *)(argp1);
  res2 = SWIG_ConvertPtr(swig_obj[1], &argp2,SWIGTYPE_p_binaryprops, 0 |  0 );
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "read_bin_cand" "', argument " "2"" of type '" "binaryprops *""'"); 
  }
  arg2 = (binaryprops *)(argp2);
  {
    errno = 0;
    result = (int)read_bin_cand(arg1,arg2);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_From_int((int)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_get_bin_cand(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  char *arg1 = (char *) 0 ;
  int arg2 ;
  binaryprops *arg3 = (binaryprops *) 0 ;
  int res1 ;
  char *buf1 = 0 ;
  int alloc1 = 0 ;
  int val2 ;
  int ecode2 = 0 ;
  void *argp3 = 0 ;
  int res3 = 0 ;
  PyObject *swig_obj[3] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "get_bin_cand", 3, 3, swig_obj)) SWIG_fail;
  res1 = SWIG_AsCharPtrAndSize(swig_obj[0], &buf1, NULL, &alloc1);
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "get_bin_cand" "', argument " "1"" of type '" "char *""'");
  }
  arg1 = (char *)(buf1);
  ecode2 = SWIG_AsVal_int(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "get_bin_cand" "', argument " "2"" of type '" "int""'");
  } 
  arg2 = (int)(val2);
  res3 = SWIG_ConvertPtr(swig_obj[2], &argp3,SWIGTYPE_p_binaryprops, 0 |  0 );
  if (!SWIG_IsOK(res3)) {
    SWIG_exception_fail(SWIG_ArgError(res3), "in method '" "get_bin_cand" "', argument " "3"" of type '" "binaryprops *""'"); 
  }
  arg3 = (binaryprops *)(argp3);
  {
    errno = 0;
    get_bin_cand(arg1,arg2,arg3);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_Py_Void();
  if (alloc1 == SWIG_NEWOBJ) free((char*)buf1);
  return resultobj;
fail:
  if (alloc1 == SWIG_NEWOBJ) free((char*)buf1);
  return NULL;
}


SWIGINTERN PyObject *_wrap_next2_to_n(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  long long arg1 ;
  long long val1 ;
  int ecode1 = 0 ;
  PyObject *swig_obj[1] ;
  long long result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  ecode1 = SWIG_AsVal_long_SS_long(swig_obj[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "next2_to_n" "', argument " "1"" of type '" "long long""'");
  } 
  arg1 = (long long)(val1);
  {
    errno = 0;
    result = (long long)next2_to_n(arg1);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_From_long_SS_long((long long)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_is_power_of_10(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  long long arg1 ;
  long long val1 ;
  int ecode1 = 0 ;
  PyObject *swig_obj[1] ;
  int result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  ecode1 = SWIG_AsVal_long_SS_long(swig_obj[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "is_power_of_10" "', argument " "1"" of type '" "long long""'");
  } 
  arg1 = (long long)(val1);
  {
    errno = 0;
    result = (int)is_power_of_10(arg1);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_From_int((int)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_choose_good_N(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  long long arg1 ;
  long long val1 ;
  int ecode1 = 0 ;
  PyObject *swig_obj[1] ;
  long long result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  ecode1 = SWIG_AsVal_long_SS_long(swig_obj[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "choose_good_N" "', argument " "1"" of type '" "long long""'");
  } 
  arg1 = (long long)(val1);
  {
    errno = 0;
    result = (long long)choose_good_N(arg1);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_From_long_SS_long((long long)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_dms2rad(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  int arg1 ;
  int arg2 ;
  double arg3 ;
  int val1 ;
  int ecode1 = 0 ;
  int val2 ;
  int ecode2 = 0 ;
  double val3 ;
  int ecode3 = 0 ;
  PyObject *swig_obj[3] ;
  double result;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "dms2rad", 3, 3, swig_obj)) SWIG_fail;
  ecode1 = SWIG_AsVal_int(swig_obj[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "dms2rad" "', argument " "1"" of type '" "int""'");
  } 
  arg1 = (int)(val1);
  ecode2 = SWIG_AsVal_int(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "dms2rad" "', argument " "2"" of type '" "int""'");
  } 
  arg2 = (int)(val2);
  ecode3 = SWIG_AsVal_double(swig_obj[2], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "dms2rad" "', argument " "3"" of type '" "double""'");
  } 
  arg3 = (double)(val3);
  {
    errno = 0;
    result = (double)dms2rad(arg1,arg2,arg3);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_hms2rad(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  int arg1 ;
  int arg2 ;
  double arg3 ;
  int val1 ;
  int ecode1 = 0 ;
  int val2 ;
  int ecode2 = 0 ;
  double val3 ;
  int ecode3 = 0 ;
  PyObject *swig_obj[3] ;
  double result;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "hms2rad", 3, 3, swig_obj)) SWIG_fail;
  ecode1 = SWIG_AsVal_int(swig_obj[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "hms2rad" "', argument " "1"" of type '" "int""'");
  } 
  arg1 = (int)(val1);
  ecode2 = SWIG_AsVal_int(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "hms2rad" "', argument " "2"" of type '" "int""'");
  } 
  arg2 = (int)(val2);
  ecode3 = SWIG_AsVal_double(swig_obj[2], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "hms2rad" "', argument " "3"" of type '" "double""'");
  } 
  arg3 = (double)(val3);
  {
    errno = 0;
    result = (double)hms2rad(arg1,arg2,arg3);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_hours2hms(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  double arg1 ;
  int *arg2 = (int *) 0 ;
  int *arg3 = (int *) 0 ;
  double *arg4 = (double *) 0 ;
  double val1 ;
  int ecode1 = 0 ;
  int temp2 ;
  int res2 = SWIG_TMPOBJ ;
  int temp3 ;
  int res3 = SWIG_TMPOBJ ;
  double temp4 ;
  int res4 = SWIG_TMPOBJ ;
  PyObject *swig_obj[1] ;
  
  arg2 = &temp2;
  arg3 = &temp3;
  arg4 = &temp4;
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  ecode1 = SWIG_AsVal_double(swig_obj[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "hours2hms" "', argument " "1"" of type '" "double""'");
  } 
  arg1 = (double)(val1);
  {
    errno = 0;
    hours2hms(arg1,arg2,arg3,arg4);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_Py_Void();
  if (SWIG_IsTmpObj(res2)) {
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_From_int((*arg2)));
  } else {
    int new_flags = SWIG_IsNewObj(res2) ? (SWIG_POINTER_OWN |  0 ) :  0 ;
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_NewPointerObj((void*)(arg2), SWIGTYPE_p_int, new_flags));
  }
  if (SWIG_IsTmpObj(res3)) {
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_From_int((*arg3)));
  } else {
    int new_flags = SWIG_IsNewObj(res3) ? (SWIG_POINTER_OWN |  0 ) :  0 ;
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_NewPointerObj((void*)(arg3), SWIGTYPE_p_int, new_flags));
  }
  if (SWIG_IsTmpObj(res4)) {
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_From_double((*arg4)));
  } else {
    int new_flags = SWIG_IsNewObj(res4) ? (SWIG_POINTER_OWN |  0 ) :  0 ;
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_NewPointerObj((void*)(arg4), SWIGTYPE_p_double, new_flags));
  }
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_deg2dms(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  double arg1 ;
  int *arg2 = (int *) 0 ;
  int *arg3 = (int *) 0 ;
  double *arg4 = (double *) 0 ;
  double val1 ;
  int ecode1 = 0 ;
  int temp2 ;
  int res2 = SWIG_TMPOBJ ;
  int temp3 ;
  int res3 = SWIG_TMPOBJ ;
  double temp4 ;
  int res4 = SWIG_TMPOBJ ;
  PyObject *swig_obj[1] ;
  
  arg2 = &temp2;
  arg3 = &temp3;
  arg4 = &temp4;
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  ecode1 = SWIG_AsVal_double(swig_obj[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "deg2dms" "', argument " "1"" of type '" "double""'");
  } 
  arg1 = (double)(val1);
  {
    errno = 0;
    deg2dms(arg1,arg2,arg3,arg4);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_Py_Void();
  if (SWIG_IsTmpObj(res2)) {
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_From_int((*arg2)));
  } else {
    int new_flags = SWIG_IsNewObj(res2) ? (SWIG_POINTER_OWN |  0 ) :  0 ;
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_NewPointerObj((void*)(arg2), SWIGTYPE_p_int, new_flags));
  }
  if (SWIG_IsTmpObj(res3)) {
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_From_int((*arg3)));
  } else {
    int new_flags = SWIG_IsNewObj(res3) ? (SWIG_POINTER_OWN |  0 ) :  0 ;
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_NewPointerObj((void*)(arg3), SWIGTYPE_p_int, new_flags));
  }
  if (SWIG_IsTmpObj(res4)) {
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_From_double((*arg4)));
  } else {
    int new_flags = SWIG_IsNewObj(res4) ? (SWIG_POINTER_OWN |  0 ) :  0 ;
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_NewPointerObj((void*)(arg4), SWIGTYPE_p_double, new_flags));
  }
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_sphere_ang_diff(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  double arg1 ;
  double arg2 ;
  double arg3 ;
  double arg4 ;
  double val1 ;
  int ecode1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  double val3 ;
  int ecode3 = 0 ;
  double val4 ;
  int ecode4 = 0 ;
  PyObject *swig_obj[4] ;
  double result;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "sphere_ang_diff", 4, 4, swig_obj)) SWIG_fail;
  ecode1 = SWIG_AsVal_double(swig_obj[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "sphere_ang_diff" "', argument " "1"" of type '" "double""'");
  } 
  arg1 = (double)(val1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "sphere_ang_diff" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  ecode3 = SWIG_AsVal_double(swig_obj[2], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "sphere_ang_diff" "', argument " "3"" of type '" "double""'");
  } 
  arg3 = (double)(val3);
  ecode4 = SWIG_AsVal_double(swig_obj[3], &val4);
  if (!SWIG_IsOK(ecode4)) {
    SWIG_exception_fail(SWIG_ArgError(ecode4), "in method '" "sphere_ang_diff" "', argument " "4"" of type '" "double""'");
  } 
  arg4 = (double)(val4);
  {
    errno = 0;
    result = (double)sphere_ang_diff(arg1,arg2,arg3,arg4);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_rz_interp(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  fcomplex *arg1 = (fcomplex *) 0 ;
  long arg2 ;
  double arg3 ;
  double arg4 ;
  int arg5 ;
  float *arg6 = (float *) 0 ;
  float *arg7 = (float *) 0 ;
  PyArrayObject *array1 = NULL ;
  int i1 = 1 ;
  double val3 ;
  int ecode3 = 0 ;
  double val4 ;
  int ecode4 = 0 ;
  int val5 ;
  int ecode5 = 0 ;
  float temp6 ;
  int res6 = SWIG_TMPOBJ ;
  float temp7 ;
  int res7 = SWIG_TMPOBJ ;
  PyObject *swig_obj[4] ;
  
  arg6 = &temp6;
  arg7 = &temp7;
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "rz_interp", 4, 4, swig_obj)) SWIG_fail;
  {
    array1 = obj_to_array_no_conversion(swig_obj[0], NPY_CFLOAT);
    if (!array1 || !require_dimensions(array1,1) || !require_contiguous(array1)
      || !require_native(array1)) SWIG_fail;
    arg1 = (fcomplex*) array_data(array1);
    arg2 = 1;
    for (i1=0; i1 < array_numdims(array1); ++i1) arg2 *= array_size(array1,i1);
  }
  ecode3 = SWIG_AsVal_double(swig_obj[1], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "rz_interp" "', argument " "3"" of type '" "double""'");
  } 
  arg3 = (double)(val3);
  ecode4 = SWIG_AsVal_double(swig_obj[2], &val4);
  if (!SWIG_IsOK(ecode4)) {
    SWIG_exception_fail(SWIG_ArgError(ecode4), "in method '" "rz_interp" "', argument " "4"" of type '" "double""'");
  } 
  arg4 = (double)(val4);
  ecode5 = SWIG_AsVal_int(swig_obj[3], &val5);
  if (!SWIG_IsOK(ecode5)) {
    SWIG_exception_fail(SWIG_ArgError(ecode5), "in method '" "rz_interp" "', argument " "5"" of type '" "int""'");
  } 
  arg5 = (int)(val5);
  {
    errno = 0;
    wrap_rz_interp(arg1,arg2,arg3,arg4,arg5,arg6,arg7);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_Py_Void();
  if (SWIG_IsTmpObj(res6)) {
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_From_float((*arg6)));
  } else {
    int new_flags = SWIG_IsNewObj(res6) ? (SWIG_POINTER_OWN |  0 ) :  0 ;
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_NewPointerObj((void*)(arg6), SWIGTYPE_p_float, new_flags));
  }
  if (SWIG_IsTmpObj(res7)) {
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_From_float((*arg7)));
  } else {
    int new_flags = SWIG_IsNewObj(res7) ? (SWIG_POINTER_OWN |  0 ) :  0 ;
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_NewPointerObj((void*)(arg7), SWIGTYPE_p_float, new_flags));
  }
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_corr_rz_plane(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  fcomplex *arg1 = (fcomplex *) 0 ;
  long arg2 ;
  int arg3 ;
  int arg4 ;
  double arg5 ;
  double arg6 ;
  int arg7 ;
  int arg8 ;
  presto_interp_acc arg9 ;
  fcomplex **arg10 = (fcomplex **) 0 ;
  long *arg11 = (long *) 0 ;
  long *arg12 = (long *) 0 ;
  PyArrayObject *array1 = NULL ;
  int i1 = 1 ;
  int val3 ;
  int ecode3 = 0 ;
  int val4 ;
  int ecode4 = 0 ;
  double val5 ;
  int ecode5 = 0 ;
  double val6 ;
  int ecode6 = 0 ;
  int val7 ;
  int ecode7 = 0 ;
  int val8 ;
  int ecode8 = 0 ;
  int val9 ;
  int ecode9 = 0 ;
  fcomplex *data_temp10 = NULL ;
  long dim1_temp10 ;
  long dim2_temp10 ;
  PyObject *swig_obj[8] ;
  
  {
    arg10 = &data_temp10;
    arg11 = &dim1_temp10;
    arg12 = &dim2_temp10;
  }
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "corr_rz_plane", 8, 8, swig_obj)) SWIG_fail;
  {
    array1 = obj_to_array_no_conversion(swig_obj[0], NPY_CFLOAT);
    if (!array1 || !require_dimensions(array1,1) || !require_contiguous(array1)
      || !require_native(array1)) SWIG_fail;
    arg1 = (fcomplex*) array_data(array1);
    arg2 = 1;
    for (i1=0; i1 < array_numdims(array1); ++i1) arg2 *= array_size(array1,i1);
  }
  ecode3 = SWIG_AsVal_int(swig_obj[1], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "corr_rz_plane" "', argument " "3"" of type '" "int""'");
  } 
  arg3 = (int)(val3);
  ecode4 = SWIG_AsVal_int(swig_obj[2], &val4);
  if (!SWIG_IsOK(ecode4)) {
    SWIG_exception_fail(SWIG_ArgError(ecode4), "in method '" "corr_rz_plane" "', argument " "4"" of type '" "int""'");
  } 
  arg4 = (int)(val4);
  ecode5 = SWIG_AsVal_double(swig_obj[3], &val5);
  if (!SWIG_IsOK(ecode5)) {
    SWIG_exception_fail(SWIG_ArgError(ecode5), "in method '" "corr_rz_plane" "', argument " "5"" of type '" "double""'");
  } 
  arg5 = (double)(val5);
  ecode6 = SWIG_AsVal_double(swig_obj[4], &val6);
  if (!SWIG_IsOK(ecode6)) {
    SWIG_exception_fail(SWIG_ArgError(ecode6), "in method '" "corr_rz_plane" "', argument " "6"" of type '" "double""'");
  } 
  arg6 = (double)(val6);
  ecode7 = SWIG_AsVal_int(swig_obj[5], &val7);
  if (!SWIG_IsOK(ecode7)) {
    SWIG_exception_fail(SWIG_ArgError(ecode7), "in method '" "corr_rz_plane" "', argument " "7"" of type '" "int""'");
  } 
  arg7 = (int)(val7);
  ecode8 = SWIG_AsVal_int(swig_obj[6], &val8);
  if (!SWIG_IsOK(ecode8)) {
    SWIG_exception_fail(SWIG_ArgError(ecode8), "in method '" "corr_rz_plane" "', argument " "8"" of type '" "int""'");
  } 
  arg8 = (int)(val8);
  ecode9 = SWIG_AsVal_int(swig_obj[7], &val9);
  if (!SWIG_IsOK(ecode9)) {
    SWIG_exception_fail(SWIG_ArgError(ecode9), "in method '" "corr_rz_plane" "', argument " "9"" of type '" "presto_interp_acc""'");
  } 
  arg9 = (presto_interp_acc)(val9);
  {
    errno = 0;
    wrap_corr_rz_plane(arg1,arg2,arg3,arg4,arg5,arg6,arg7,arg8,arg9,arg10,arg11,arg12);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_Py_Void();
  {
    npy_intp dims[2] = {
      *arg11, *arg12 
    };
    PyObject* obj = PyArray_SimpleNewFromData(2, dims, NPY_CFLOAT, (void*)(*arg10));
    PyArrayObject* array = (PyArrayObject*) obj;
    
    if (!array) SWIG_fail;
    
#ifdef SWIGPY_USE_CAPSULE
    PyObject* cap = PyCapsule_New((void*)(*arg10), SWIGPY_CAPSULE_NAME, free_cap);
#else
    PyObject* cap = PyCObject_FromVoidPtr((void*)(*arg10), free);
#endif
    
#if NPY_API_VERSION < 0x00000007
    PyArray_BASE(array) = cap;
#else
    PyArray_SetBaseObject(array,cap);
#endif
    
    resultobj = SWIG_Python_AppendOutput(resultobj,obj);
  }
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_corr_rzw_vol(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  fcomplex *arg1 = (fcomplex *) 0 ;
  long arg2 ;
  int arg3 ;
  int arg4 ;
  double arg5 ;
  double arg6 ;
  int arg7 ;
  double arg8 ;
  double arg9 ;
  int arg10 ;
  int arg11 ;
  presto_interp_acc arg12 ;
  fcomplex **arg13 = (fcomplex **) 0 ;
  long *arg14 = (long *) 0 ;
  long *arg15 = (long *) 0 ;
  long *arg16 = (long *) 0 ;
  PyArrayObject *array1 = NULL ;
  int i1 = 1 ;
  int val3 ;
  int ecode3 = 0 ;
  int val4 ;
  int ecode4 = 0 ;
  double val5 ;
  int ecode5 = 0 ;
  double val6 ;
  int ecode6 = 0 ;
  int val7 ;
  int ecode7 = 0 ;
  double val8 ;
  int ecode8 = 0 ;
  double val9 ;
  int ecode9 = 0 ;
  int val10 ;
  int ecode10 = 0 ;
  int val11 ;
  int ecode11 = 0 ;
  int val12 ;
  int ecode12 = 0 ;
  fcomplex *data_temp13 = NULL ;
  long dim1_temp13 ;
  long dim2_temp13 ;
  long dim3_temp13 ;
  PyObject *swig_obj[11] ;
  
  {
    arg13 = &data_temp13;
    arg14 = &dim1_temp13;
    arg15 = &dim2_temp13;
    arg16 = &dim3_temp13;
  }
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "corr_rzw_vol", 11, 11, swig_obj)) SWIG_fail;
  {
    array1 = obj_to_array_no_conversion(swig_obj[0], NPY_CFLOAT);
    if (!array1 || !require_dimensions(array1,1) || !require_contiguous(array1)
      || !require_native(array1)) SWIG_fail;
    arg1 = (fcomplex*) array_data(array1);
    arg2 = 1;
    for (i1=0; i1 < array_numdims(array1); ++i1) arg2 *= array_size(array1,i1);
  }
  ecode3 = SWIG_AsVal_int(swig_obj[1], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "corr_rzw_vol" "', argument " "3"" of type '" "int""'");
  } 
  arg3 = (int)(val3);
  ecode4 = SWIG_AsVal_int(swig_obj[2], &val4);
  if (!SWIG_IsOK(ecode4)) {
    SWIG_exception_fail(SWIG_ArgError(ecode4), "in method '" "corr_rzw_vol" "', argument " "4"" of type '" "int""'");
  } 
  arg4 = (int)(val4);
  ecode5 = SWIG_AsVal_double(swig_obj[3], &val5);
  if (!SWIG_IsOK(ecode5)) {
    SWIG_exception_fail(SWIG_ArgError(ecode5), "in method '" "corr_rzw_vol" "', argument " "5"" of type '" "double""'");
  } 
  arg5 = (double)(val5);
  ecode6 = SWIG_AsVal_double(swig_obj[4], &val6);
  if (!SWIG_IsOK(ecode6)) {
    SWIG_exception_fail(SWIG_ArgError(ecode6), "in method '" "corr_rzw_vol" "', argument " "6"" of type '" "double""'");
  } 
  arg6 = (double)(val6);
  ecode7 = SWIG_AsVal_int(swig_obj[5], &val7);
  if (!SWIG_IsOK(ecode7)) {
    SWIG_exception_fail(SWIG_ArgError(ecode7), "in method '" "corr_rzw_vol" "', argument " "7"" of type '" "int""'");
  } 
  arg7 = (int)(val7);
  ecode8 = SWIG_AsVal_double(swig_obj[6], &val8);
  if (!SWIG_IsOK(ecode8)) {
    SWIG_exception_fail(SWIG_ArgError(ecode8), "in method '" "corr_rzw_vol" "', argument " "8"" of type '" "double""'");
  } 
  arg8 = (double)(val8);
  ecode9 = SWIG_AsVal_double(swig_obj[7], &val9);
  if (!SWIG_IsOK(ecode9)) {
    SWIG_exception_fail(SWIG_ArgError(ecode9), "in method '" "corr_rzw_vol" "', argument " "9"" of type '" "double""'");
  } 
  arg9 = (double)(val9);
  ecode10 = SWIG_AsVal_int(swig_obj[8], &val10);
  if (!SWIG_IsOK(ecode10)) {
    SWIG_exception_fail(SWIG_ArgError(ecode10), "in method '" "corr_rzw_vol" "', argument " "10"" of type '" "int""'");
  } 
  arg10 = (int)(val10);
  ecode11 = SWIG_AsVal_int(swig_obj[9], &val11);
  if (!SWIG_IsOK(ecode11)) {
    SWIG_exception_fail(SWIG_ArgError(ecode11), "in method '" "corr_rzw_vol" "', argument " "11"" of type '" "int""'");
  } 
  arg11 = (int)(val11);
  ecode12 = SWIG_AsVal_int(swig_obj[10], &val12);
  if (!SWIG_IsOK(ecode12)) {
    SWIG_exception_fail(SWIG_ArgError(ecode12), "in method '" "corr_rzw_vol" "', argument " "12"" of type '" "presto_interp_acc""'");
  } 
  arg12 = (presto_interp_acc)(val12);
  {
    errno = 0;
    wrap_corr_rzw_vol(arg1,arg2,arg3,arg4,arg5,arg6,arg7,arg8,arg9,arg10,arg11,arg12,arg13,arg14,arg15,arg16);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_Py_Void();
  {
    npy_intp dims[3] = {
      *arg14, *arg15, *arg16 
    };
    PyObject* obj = PyArray_SimpleNewFromData(3, dims, NPY_CFLOAT, (void*)(*arg13));
    PyArrayObject* array = (PyArrayObject*) obj;
    
    if (!array) SWIG_fail;
    
#ifdef SWIGPY_USE_CAPSULE
    PyObject* cap = PyCapsule_New((void*)(*arg13), SWIGPY_CAPSULE_NAME, free_cap);
#else
    PyObject* cap = PyCObject_FromVoidPtr((void*)(*arg13), free);
#endif
    
#if NPY_API_VERSION < 0x00000007
    PyArray_BASE(array) = cap;
#else
    PyArray_SetBaseObject(array,cap);
#endif
    
    resultobj = SWIG_Python_AppendOutput(resultobj,obj);
  }
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_max_r_arr(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  fcomplex *arg1 = (fcomplex *) 0 ;
  long arg2 ;
  double arg3 ;
  rderivs *arg4 = (rderivs *) 0 ;
  double *arg5 = (double *) 0 ;
  double *arg6 = (double *) 0 ;
  PyArrayObject *array1 = NULL ;
  int i1 = 1 ;
  double val3 ;
  int ecode3 = 0 ;
  void *argp4 = 0 ;
  int res4 = 0 ;
  double temp5 ;
  int res5 = SWIG_TMPOBJ ;
  double temp6 ;
  int res6 = SWIG_TMPOBJ ;
  PyObject *swig_obj[3] ;
  
  arg5 = &temp5;
  arg6 = &temp6;
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "max_r_arr", 3, 3, swig_obj)) SWIG_fail;
  {
    array1 = obj_to_array_no_conversion(swig_obj[0], NPY_CFLOAT);
    if (!array1 || !require_dimensions(array1,1) || !require_contiguous(array1)
      || !require_native(array1)) SWIG_fail;
    arg1 = (fcomplex*) array_data(array1);
    arg2 = 1;
    for (i1=0; i1 < array_numdims(array1); ++i1) arg2 *= array_size(array1,i1);
  }
  ecode3 = SWIG_AsVal_double(swig_obj[1], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "max_r_arr" "', argument " "3"" of type '" "double""'");
  } 
  arg3 = (double)(val3);
  res4 = SWIG_ConvertPtr(swig_obj[2], &argp4,SWIGTYPE_p_RDERIVS, 0 |  0 );
  if (!SWIG_IsOK(res4)) {
    SWIG_exception_fail(SWIG_ArgError(res4), "in method '" "max_r_arr" "', argument " "4"" of type '" "rderivs *""'"); 
  }
  arg4 = (rderivs *)(argp4);
  {
    errno = 0;
    wrap_max_r_arr(arg1,arg2,arg3,arg4,arg5,arg6);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_Py_Void();
  if (SWIG_IsTmpObj(res5)) {
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_From_double((*arg5)));
  } else {
    int new_flags = SWIG_IsNewObj(res5) ? (SWIG_POINTER_OWN |  0 ) :  0 ;
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_NewPointerObj((void*)(arg5), SWIGTYPE_p_double, new_flags));
  }
  if (SWIG_IsTmpObj(res6)) {
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_From_double((*arg6)));
  } else {
    int new_flags = SWIG_IsNewObj(res6) ? (SWIG_POINTER_OWN |  0 ) :  0 ;
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_NewPointerObj((void*)(arg6), SWIGTYPE_p_double, new_flags));
  }
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_max_rz_arr(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  fcomplex *arg1 = (fcomplex *) 0 ;
  long arg2 ;
  double arg3 ;
  double arg4 ;
  rderivs *arg5 = (rderivs *) 0 ;
  double *arg6 = (double *) 0 ;
  double *arg7 = (double *) 0 ;
  double *arg8 = (double *) 0 ;
  PyArrayObject *array1 = NULL ;
  int i1 = 1 ;
  double val3 ;
  int ecode3 = 0 ;
  double val4 ;
  int ecode4 = 0 ;
  void *argp5 = 0 ;
  int res5 = 0 ;
  double temp6 ;
  int res6 = SWIG_TMPOBJ ;
  double temp7 ;
  int res7 = SWIG_TMPOBJ ;
  double temp8 ;
  int res8 = SWIG_TMPOBJ ;
  PyObject *swig_obj[4] ;
  
  arg6 = &temp6;
  arg7 = &temp7;
  arg8 = &temp8;
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "max_rz_arr", 4, 4, swig_obj)) SWIG_fail;
  {
    array1 = obj_to_array_no_conversion(swig_obj[0], NPY_CFLOAT);
    if (!array1 || !require_dimensions(array1,1) || !require_contiguous(array1)
      || !require_native(array1)) SWIG_fail;
    arg1 = (fcomplex*) array_data(array1);
    arg2 = 1;
    for (i1=0; i1 < array_numdims(array1); ++i1) arg2 *= array_size(array1,i1);
  }
  ecode3 = SWIG_AsVal_double(swig_obj[1], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "max_rz_arr" "', argument " "3"" of type '" "double""'");
  } 
  arg3 = (double)(val3);
  ecode4 = SWIG_AsVal_double(swig_obj[2], &val4);
  if (!SWIG_IsOK(ecode4)) {
    SWIG_exception_fail(SWIG_ArgError(ecode4), "in method '" "max_rz_arr" "', argument " "4"" of type '" "double""'");
  } 
  arg4 = (double)(val4);
  res5 = SWIG_ConvertPtr(swig_obj[3], &argp5,SWIGTYPE_p_RDERIVS, 0 |  0 );
  if (!SWIG_IsOK(res5)) {
    SWIG_exception_fail(SWIG_ArgError(res5), "in method '" "max_rz_arr" "', argument " "5"" of type '" "rderivs *""'"); 
  }
  arg5 = (rderivs *)(argp5);
  {
    errno = 0;
    wrap_max_rz_arr(arg1,arg2,arg3,arg4,arg5,arg6,arg7,arg8);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_Py_Void();
  if (SWIG_IsTmpObj(res6)) {
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_From_double((*arg6)));
  } else {
    int new_flags = SWIG_IsNewObj(res6) ? (SWIG_POINTER_OWN |  0 ) :  0 ;
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_NewPointerObj((void*)(arg6), SWIGTYPE_p_double, new_flags));
  }
  if (SWIG_IsTmpObj(res7)) {
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_From_double((*arg7)));
  } else {
    int new_flags = SWIG_IsNewObj(res7) ? (SWIG_POINTER_OWN |  0 ) :  0 ;
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_NewPointerObj((void*)(arg7), SWIGTYPE_p_double, new_flags));
  }
  if (SWIG_IsTmpObj(res8)) {
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_From_double((*arg8)));
  } else {
    int new_flags = SWIG_IsNewObj(res8) ? (SWIG_POINTER_OWN |  0 ) :  0 ;
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_NewPointerObj((void*)(arg8), SWIGTYPE_p_double, new_flags));
  }
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_max_rz_arr_harmonics(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  fcomplex *arg1 = (fcomplex *) 0 ;
  long arg2 ;
  double arg3 ;
  double arg4 ;
  double *arg5 = (double *) 0 ;
  int arg6 ;
  double *arg7 = (double *) 0 ;
  double *arg8 = (double *) 0 ;
  PyArrayObject *array1 = NULL ;
  int i1 = 1 ;
  double val3 ;
  int ecode3 = 0 ;
  double val4 ;
  int ecode4 = 0 ;
  PyArrayObject *array5 = NULL ;
  int i5 = 1 ;
  double temp7 ;
  int res7 = SWIG_TMPOBJ ;
  double temp8 ;
  int res8 = SWIG_TMPOBJ ;
  PyObject *swig_obj[4] ;
  
  arg7 = &temp7;
  arg8 = &temp8;
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "max_rz_arr_harmonics", 4, 4, swig_obj)) SWIG_fail;
  {
    array1 = obj_to_array_no_conversion(swig_obj[0], NPY_CFLOAT);
    if (!array1 || !require_dimensions(array1,1) || !require_contiguous(array1)
      || !require_native(array1)) SWIG_fail;
    arg1 = (fcomplex*) array_data(array1);
    arg2 = 1;
    for (i1=0; i1 < array_numdims(array1); ++i1) arg2 *= array_size(array1,i1);
  }
  ecode3 = SWIG_AsVal_double(swig_obj[1], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "max_rz_arr_harmonics" "', argument " "3"" of type '" "double""'");
  } 
  arg3 = (double)(val3);
  ecode4 = SWIG_AsVal_double(swig_obj[2], &val4);
  if (!SWIG_IsOK(ecode4)) {
    SWIG_exception_fail(SWIG_ArgError(ecode4), "in method '" "max_rz_arr_harmonics" "', argument " "4"" of type '" "double""'");
  } 
  arg4 = (double)(val4);
  {
    array5 = obj_to_array_no_conversion(swig_obj[3], NPY_DOUBLE);
    if (!array5 || !require_dimensions(array5,1) || !require_contiguous(array5)
      || !require_native(array5)) SWIG_fail;
    arg5 = (double*) array_data(array5);
    arg6 = 1;
    for (i5=0; i5 < array_numdims(array5); ++i5) arg6 *= array_size(array5,i5);
  }
  {
    errno = 0;
    wrap_max_rz_arr_harmonics(arg1,arg2,arg3,arg4,arg5,arg6,arg7,arg8);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_Py_Void();
  if (SWIG_IsTmpObj(res7)) {
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_From_double((*arg7)));
  } else {
    int new_flags = SWIG_IsNewObj(res7) ? (SWIG_POINTER_OWN |  0 ) :  0 ;
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_NewPointerObj((void*)(arg7), SWIGTYPE_p_double, new_flags));
  }
  if (SWIG_IsTmpObj(res8)) {
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_From_double((*arg8)));
  } else {
    int new_flags = SWIG_IsNewObj(res8) ? (SWIG_POINTER_OWN |  0 ) :  0 ;
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_NewPointerObj((void*)(arg8), SWIGTYPE_p_double, new_flags));
  }
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_max_rzw_arr_harmonics(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  fcomplex *arg1 = (fcomplex *) 0 ;
  long arg2 ;
  double arg3 ;
  double arg4 ;
  double arg5 ;
  double *arg6 = (double *) 0 ;
  int arg7 ;
  double *arg8 = (double *) 0 ;
  double *arg9 = (double *) 0 ;
  double *arg10 = (double *) 0 ;
  PyArrayObject *array1 = NULL ;
  int i1 = 1 ;
  double val3 ;
  int ecode3 = 0 ;
  double val4 ;
  int ecode4 = 0 ;
  double val5 ;
  int ecode5 = 0 ;
  PyArrayObject *array6 = NULL ;
  int i6 = 1 ;
  double temp8 ;
  int res8 = SWIG_TMPOBJ ;
  double temp9 ;
  int res9 = SWIG_TMPOBJ ;
  double temp10 ;
  int res10 = SWIG_TMPOBJ ;
  PyObject *swig_obj[5] ;
  
  arg8 = &temp8;
  arg9 = &temp9;
  arg10 = &temp10;
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "max_rzw_arr_harmonics", 5, 5, swig_obj)) SWIG_fail;
  {
    array1 = obj_to_array_no_conversion(swig_obj[0], NPY_CFLOAT);
    if (!array1 || !require_dimensions(array1,1) || !require_contiguous(array1)
      || !require_native(array1)) SWIG_fail;
    arg1 = (fcomplex*) array_data(array1);
    arg2 = 1;
    for (i1=0; i1 < array_numdims(array1); ++i1) arg2 *= array_size(array1,i1);
  }
  ecode3 = SWIG_AsVal_double(swig_obj[1], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "max_rzw_arr_harmonics" "', argument " "3"" of type '" "double""'");
  } 
  arg3 = (double)(val3);
  ecode4 = SWIG_AsVal_double(swig_obj[2], &val4);
  if (!SWIG_IsOK(ecode4)) {
    SWIG_exception_fail(SWIG_ArgError(ecode4), "in method '" "max_rzw_arr_harmonics" "', argument " "4"" of type '" "double""'");
  } 
  arg4 = (double)(val4);
  ecode5 = SWIG_AsVal_double(swig_obj[3], &val5);
  if (!SWIG_IsOK(ecode5)) {
    SWIG_exception_fail(SWIG_ArgError(ecode5), "in method '" "max_rzw_arr_harmonics" "', argument " "5"" of type '" "double""'");
  } 
  arg5 = (double)(val5);
  {
    array6 = obj_to_array_no_conversion(swig_obj[4], NPY_DOUBLE);
    if (!array6 || !require_dimensions(array6,1) || !require_contiguous(array6)
      || !require_native(array6)) SWIG_fail;
    arg6 = (double*) array_data(array6);
    arg7 = 1;
    for (i6=0; i6 < array_numdims(array6); ++i6) arg7 *= array_size(array6,i6);
  }
  {
    errno = 0;
    wrap_max_rzw_arr_harmonics(arg1,arg2,arg3,arg4,arg5,arg6,arg7,arg8,arg9,arg10);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_Py_Void();
  if (SWIG_IsTmpObj(res8)) {
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_From_double((*arg8)));
  } else {
    int new_flags = SWIG_IsNewObj(res8) ? (SWIG_POINTER_OWN |  0 ) :  0 ;
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_NewPointerObj((void*)(arg8), SWIGTYPE_p_double, new_flags));
  }
  if (SWIG_IsTmpObj(res9)) {
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_From_double((*arg9)));
  } else {
    int new_flags = SWIG_IsNewObj(res9) ? (SWIG_POINTER_OWN |  0 ) :  0 ;
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_NewPointerObj((void*)(arg9), SWIGTYPE_p_double, new_flags));
  }
  if (SWIG_IsTmpObj(res10)) {
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_From_double((*arg10)));
  } else {
    int new_flags = SWIG_IsNewObj(res10) ? (SWIG_POINTER_OWN |  0 ) :  0 ;
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_NewPointerObj((void*)(arg10), SWIGTYPE_p_double, new_flags));
  }
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_max_rzw_arr(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  fcomplex *arg1 = (fcomplex *) 0 ;
  long arg2 ;
  double arg3 ;
  double arg4 ;
  double arg5 ;
  rderivs *arg6 = (rderivs *) 0 ;
  double *arg7 = (double *) 0 ;
  double *arg8 = (double *) 0 ;
  double *arg9 = (double *) 0 ;
  double *arg10 = (double *) 0 ;
  PyArrayObject *array1 = NULL ;
  int i1 = 1 ;
  double val3 ;
  int ecode3 = 0 ;
  double val4 ;
  int ecode4 = 0 ;
  double val5 ;
  int ecode5 = 0 ;
  void *argp6 = 0 ;
  int res6 = 0 ;
  double temp7 ;
  int res7 = SWIG_TMPOBJ ;
  double temp8 ;
  int res8 = SWIG_TMPOBJ ;
  double temp9 ;
  int res9 = SWIG_TMPOBJ ;
  double temp10 ;
  int res10 = SWIG_TMPOBJ ;
  PyObject *swig_obj[5] ;
  
  arg7 = &temp7;
  arg8 = &temp8;
  arg9 = &temp9;
  arg10 = &temp10;
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "max_rzw_arr", 5, 5, swig_obj)) SWIG_fail;
  {
    array1 = obj_to_array_no_conversion(swig_obj[0], NPY_CFLOAT);
    if (!array1 || !require_dimensions(array1,1) || !require_contiguous(array1)
      || !require_native(array1)) SWIG_fail;
    arg1 = (fcomplex*) array_data(array1);
    arg2 = 1;
    for (i1=0; i1 < array_numdims(array1); ++i1) arg2 *= array_size(array1,i1);
  }
  ecode3 = SWIG_AsVal_double(swig_obj[1], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "max_rzw_arr" "', argument " "3"" of type '" "double""'");
  } 
  arg3 = (double)(val3);
  ecode4 = SWIG_AsVal_double(swig_obj[2], &val4);
  if (!SWIG_IsOK(ecode4)) {
    SWIG_exception_fail(SWIG_ArgError(ecode4), "in method '" "max_rzw_arr" "', argument " "4"" of type '" "double""'");
  } 
  arg4 = (double)(val4);
  ecode5 = SWIG_AsVal_double(swig_obj[3], &val5);
  if (!SWIG_IsOK(ecode5)) {
    SWIG_exception_fail(SWIG_ArgError(ecode5), "in method '" "max_rzw_arr" "', argument " "5"" of type '" "double""'");
  } 
  arg5 = (double)(val5);
  res6 = SWIG_ConvertPtr(swig_obj[4], &argp6,SWIGTYPE_p_RDERIVS, 0 |  0 );
  if (!SWIG_IsOK(res6)) {
    SWIG_exception_fail(SWIG_ArgError(res6), "in method '" "max_rzw_arr" "', argument " "6"" of type '" "rderivs *""'"); 
  }
  arg6 = (rderivs *)(argp6);
  {
    errno = 0;
    wrap_max_rzw_arr(arg1,arg2,arg3,arg4,arg5,arg6,arg7,arg8,arg9,arg10);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_Py_Void();
  if (SWIG_IsTmpObj(res7)) {
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_From_double((*arg7)));
  } else {
    int new_flags = SWIG_IsNewObj(res7) ? (SWIG_POINTER_OWN |  0 ) :  0 ;
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_NewPointerObj((void*)(arg7), SWIGTYPE_p_double, new_flags));
  }
  if (SWIG_IsTmpObj(res8)) {
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_From_double((*arg8)));
  } else {
    int new_flags = SWIG_IsNewObj(res8) ? (SWIG_POINTER_OWN |  0 ) :  0 ;
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_NewPointerObj((void*)(arg8), SWIGTYPE_p_double, new_flags));
  }
  if (SWIG_IsTmpObj(res9)) {
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_From_double((*arg9)));
  } else {
    int new_flags = SWIG_IsNewObj(res9) ? (SWIG_POINTER_OWN |  0 ) :  0 ;
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_NewPointerObj((void*)(arg9), SWIGTYPE_p_double, new_flags));
  }
  if (SWIG_IsTmpObj(res10)) {
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_From_double((*arg10)));
  } else {
    int new_flags = SWIG_IsNewObj(res10) ? (SWIG_POINTER_OWN |  0 ) :  0 ;
    resultobj = SWIG_Python_AppendOutput(resultobj, SWIG_NewPointerObj((void*)(arg10), SWIGTYPE_p_double, new_flags));
  }
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_barycenter(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  double *arg1 = (double *) 0 ;
  long arg2 ;
  double *arg3 = (double *) 0 ;
  long arg4 ;
  double *arg5 = (double *) 0 ;
  long arg6 ;
  char *arg7 = (char *) 0 ;
  char *arg8 = (char *) 0 ;
  char *arg9 = (char *) 0 ;
  char *arg10 = (char *) 0 ;
  PyArrayObject *array1 = NULL ;
  int i1 = 1 ;
  PyArrayObject *array3 = NULL ;
  int i3 = 1 ;
  PyArrayObject *array5 = NULL ;
  int i5 = 1 ;
  int res7 ;
  char *buf7 = 0 ;
  int alloc7 = 0 ;
  int res8 ;
  char *buf8 = 0 ;
  int alloc8 = 0 ;
  int res9 ;
  char *buf9 = 0 ;
  int alloc9 = 0 ;
  int res10 ;
  char *buf10 = 0 ;
  int alloc10 = 0 ;
  PyObject *swig_obj[7] ;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "barycenter", 7, 7, swig_obj)) SWIG_fail;
  {
    array1 = obj_to_array_no_conversion(swig_obj[0], NPY_DOUBLE);
    if (!array1 || !require_dimensions(array1,1) || !require_contiguous(array1)
      || !require_native(array1)) SWIG_fail;
    arg1 = (double*) array_data(array1);
    arg2 = 1;
    for (i1=0; i1 < array_numdims(array1); ++i1) arg2 *= array_size(array1,i1);
  }
  {
    array3 = obj_to_array_no_conversion(swig_obj[1], NPY_DOUBLE);
    if (!array3 || !require_dimensions(array3,1) || !require_contiguous(array3)
      || !require_native(array3)) SWIG_fail;
    arg3 = (double*) array_data(array3);
    arg4 = 1;
    for (i3=0; i3 < array_numdims(array3); ++i3) arg4 *= array_size(array3,i3);
  }
  {
    array5 = obj_to_array_no_conversion(swig_obj[2], NPY_DOUBLE);
    if (!array5 || !require_dimensions(array5,1) || !require_contiguous(array5)
      || !require_native(array5)) SWIG_fail;
    arg5 = (double*) array_data(array5);
    arg6 = 1;
    for (i5=0; i5 < array_numdims(array5); ++i5) arg6 *= array_size(array5,i5);
  }
  res7 = SWIG_AsCharPtrAndSize(swig_obj[3], &buf7, NULL, &alloc7);
  if (!SWIG_IsOK(res7)) {
    SWIG_exception_fail(SWIG_ArgError(res7), "in method '" "barycenter" "', argument " "7"" of type '" "char *""'");
  }
  arg7 = (char *)(buf7);
  res8 = SWIG_AsCharPtrAndSize(swig_obj[4], &buf8, NULL, &alloc8);
  if (!SWIG_IsOK(res8)) {
    SWIG_exception_fail(SWIG_ArgError(res8), "in method '" "barycenter" "', argument " "8"" of type '" "char *""'");
  }
  arg8 = (char *)(buf8);
  res9 = SWIG_AsCharPtrAndSize(swig_obj[5], &buf9, NULL, &alloc9);
  if (!SWIG_IsOK(res9)) {
    SWIG_exception_fail(SWIG_ArgError(res9), "in method '" "barycenter" "', argument " "9"" of type '" "char *""'");
  }
  arg9 = (char *)(buf9);
  res10 = SWIG_AsCharPtrAndSize(swig_obj[6], &buf10, NULL, &alloc10);
  if (!SWIG_IsOK(res10)) {
    SWIG_exception_fail(SWIG_ArgError(res10), "in method '" "barycenter" "', argument " "10"" of type '" "char *""'");
  }
  arg10 = (char *)(buf10);
  {
    errno = 0;
    wrap_barycenter(arg1,arg2,arg3,arg4,arg5,arg6,arg7,arg8,arg9,arg10);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_Py_Void();
  if (alloc7 == SWIG_NEWOBJ) free((char*)buf7);
  if (alloc8 == SWIG_NEWOBJ) free((char*)buf8);
  if (alloc9 == SWIG_NEWOBJ) free((char*)buf9);
  if (alloc10 == SWIG_NEWOBJ) free((char*)buf10);
  return resultobj;
fail:
  if (alloc7 == SWIG_NEWOBJ) free((char*)buf7);
  if (alloc8 == SWIG_NEWOBJ) free((char*)buf8);
  if (alloc9 == SWIG_NEWOBJ) free((char*)buf9);
  if (alloc10 == SWIG_NEWOBJ) free((char*)buf10);
  return NULL;
}


SWIGINTERN PyObject *_wrap_DOF_corr(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  double arg1 ;
  double val1 ;
  int ecode1 = 0 ;
  PyObject *swig_obj[1] ;
  double result;
  
  (void)self;
  if (!args) SWIG_fail;
  swig_obj[0] = args;
  ecode1 = SWIG_AsVal_double(swig_obj[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "DOF_corr" "', argument " "1"" of type '" "double""'");
  } 
  arg1 = (double)(val1);
  {
    errno = 0;
    result = (double)DOF_corr(arg1);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_simplefold(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  float *arg1 = (float *) 0 ;
  long arg2 ;
  double arg3 ;
  double arg4 ;
  double *arg5 = (double *) 0 ;
  long arg6 ;
  double arg7 ;
  double arg8 ;
  double arg9 ;
  double arg10 ;
  int arg11 ;
  PyArrayObject *array1 = NULL ;
  int i1 = 1 ;
  double val3 ;
  int ecode3 = 0 ;
  double val4 ;
  int ecode4 = 0 ;
  PyArrayObject *array5 = NULL ;
  int i5 = 1 ;
  double val7 ;
  int ecode7 = 0 ;
  double val8 ;
  int ecode8 = 0 ;
  double val9 ;
  int ecode9 = 0 ;
  double val10 ;
  int ecode10 = 0 ;
  int val11 ;
  int ecode11 = 0 ;
  PyObject *swig_obj[9] ;
  double result;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "simplefold", 9, 9, swig_obj)) SWIG_fail;
  {
    array1 = obj_to_array_no_conversion(swig_obj[0], NPY_FLOAT);
    if (!array1 || !require_dimensions(array1,1) || !require_contiguous(array1)
      || !require_native(array1)) SWIG_fail;
    arg1 = (float*) array_data(array1);
    arg2 = 1;
    for (i1=0; i1 < array_numdims(array1); ++i1) arg2 *= array_size(array1,i1);
  }
  ecode3 = SWIG_AsVal_double(swig_obj[1], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "simplefold" "', argument " "3"" of type '" "double""'");
  } 
  arg3 = (double)(val3);
  ecode4 = SWIG_AsVal_double(swig_obj[2], &val4);
  if (!SWIG_IsOK(ecode4)) {
    SWIG_exception_fail(SWIG_ArgError(ecode4), "in method '" "simplefold" "', argument " "4"" of type '" "double""'");
  } 
  arg4 = (double)(val4);
  {
    array5 = obj_to_array_no_conversion(swig_obj[3], NPY_DOUBLE);
    if (!array5 || !require_dimensions(array5,1) || !require_contiguous(array5)
      || !require_native(array5)) SWIG_fail;
    arg5 = (double*) array_data(array5);
    arg6 = 1;
    for (i5=0; i5 < array_numdims(array5); ++i5) arg6 *= array_size(array5,i5);
  }
  ecode7 = SWIG_AsVal_double(swig_obj[4], &val7);
  if (!SWIG_IsOK(ecode7)) {
    SWIG_exception_fail(SWIG_ArgError(ecode7), "in method '" "simplefold" "', argument " "7"" of type '" "double""'");
  } 
  arg7 = (double)(val7);
  ecode8 = SWIG_AsVal_double(swig_obj[5], &val8);
  if (!SWIG_IsOK(ecode8)) {
    SWIG_exception_fail(SWIG_ArgError(ecode8), "in method '" "simplefold" "', argument " "8"" of type '" "double""'");
  } 
  arg8 = (double)(val8);
  ecode9 = SWIG_AsVal_double(swig_obj[6], &val9);
  if (!SWIG_IsOK(ecode9)) {
    SWIG_exception_fail(SWIG_ArgError(ecode9), "in method '" "simplefold" "', argument " "9"" of type '" "double""'");
  } 
  arg9 = (double)(val9);
  ecode10 = SWIG_AsVal_double(swig_obj[7], &val10);
  if (!SWIG_IsOK(ecode10)) {
    SWIG_exception_fail(SWIG_ArgError(ecode10), "in method '" "simplefold" "', argument " "10"" of type '" "double""'");
  } 
  arg10 = (double)(val10);
  ecode11 = SWIG_AsVal_int(swig_obj[8], &val11);
  if (!SWIG_IsOK(ecode11)) {
    SWIG_exception_fail(SWIG_ArgError(ecode11), "in method '" "simplefold" "', argument " "11"" of type '" "int""'");
  } 
  arg11 = (int)(val11);
  {
    errno = 0;
    result = (double)wrap_simplefold(arg1,arg2,arg3,arg4,arg5,arg6,arg7,arg8,arg9,arg10,arg11);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_From_double((double)(result));
  return resultobj;
fail:
  return NULL;
}


SWIGINTERN PyObject *_wrap_nice_output_1(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  char *arg1 = (char *) 0 ;
  double arg2 ;
  double arg3 ;
  int arg4 ;
  int res1 ;
  char *buf1 = 0 ;
  int alloc1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  double val3 ;
  int ecode3 = 0 ;
  int val4 ;
  int ecode4 = 0 ;
  PyObject *swig_obj[4] ;
  int result;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "nice_output_1", 4, 4, swig_obj)) SWIG_fail;
  res1 = SWIG_AsCharPtrAndSize(swig_obj[0], &buf1, NULL, &alloc1);
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "nice_output_1" "', argument " "1"" of type '" "char *""'");
  }
  arg1 = (char *)(buf1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "nice_output_1" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  ecode3 = SWIG_AsVal_double(swig_obj[2], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "nice_output_1" "', argument " "3"" of type '" "double""'");
  } 
  arg3 = (double)(val3);
  ecode4 = SWIG_AsVal_int(swig_obj[3], &val4);
  if (!SWIG_IsOK(ecode4)) {
    SWIG_exception_fail(SWIG_ArgError(ecode4), "in method '" "nice_output_1" "', argument " "4"" of type '" "int""'");
  } 
  arg4 = (int)(val4);
  {
    errno = 0;
    result = (int)nice_output_1(arg1,arg2,arg3,arg4);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_From_int((int)(result));
  if (alloc1 == SWIG_NEWOBJ) free((char*)buf1);
  return resultobj;
fail:
  if (alloc1 == SWIG_NEWOBJ) free((char*)buf1);
  return NULL;
}


SWIGINTERN PyObject *_wrap_nice_output_2(PyObject *self, PyObject *args) {
  PyObject *resultobj = 0;
  char *arg1 = (char *) 0 ;
  double arg2 ;
  double arg3 ;
  int arg4 ;
  int res1 ;
  char *buf1 = 0 ;
  int alloc1 = 0 ;
  double val2 ;
  int ecode2 = 0 ;
  double val3 ;
  int ecode3 = 0 ;
  int val4 ;
  int ecode4 = 0 ;
  PyObject *swig_obj[4] ;
  int result;
  
  (void)self;
  if (!SWIG_Python_UnpackTuple(args, "nice_output_2", 4, 4, swig_obj)) SWIG_fail;
  res1 = SWIG_AsCharPtrAndSize(swig_obj[0], &buf1, NULL, &alloc1);
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "nice_output_2" "', argument " "1"" of type '" "char *""'");
  }
  arg1 = (char *)(buf1);
  ecode2 = SWIG_AsVal_double(swig_obj[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "nice_output_2" "', argument " "2"" of type '" "double""'");
  } 
  arg2 = (double)(val2);
  ecode3 = SWIG_AsVal_double(swig_obj[2], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "nice_output_2" "', argument " "3"" of type '" "double""'");
  } 
  arg3 = (double)(val3);
  ecode4 = SWIG_AsVal_int(swig_obj[3], &val4);
  if (!SWIG_IsOK(ecode4)) {
    SWIG_exception_fail(SWIG_ArgError(ecode4), "in method '" "nice_output_2" "', argument " "4"" of type '" "int""'");
  } 
  arg4 = (int)(val4);
  {
    errno = 0;
    result = (int)nice_output_2(arg1,arg2,arg3,arg4);
    
    if (errno != 0)
    {
      switch(errno)
      {
      case ENOMEM:
        PyErr_Format(PyExc_MemoryError, "Failed malloc()");
        break;
      default:
        PyErr_Format(PyExc_Exception, "Unknown exception");
      }
      SWIG_fail;
    }
  }
  resultobj = SWIG_From_int((int)(result));
  if (alloc1 == SWIG_NEWOBJ) free((char*)buf1);
  return resultobj;
fail:
  if (alloc1 == SWIG_NEWOBJ) free((char*)buf1);
  return NULL;
}


static PyMethodDef SwigMethods[] = {
	 { "fcomplex_r_set", _wrap_fcomplex_r_set, METH_VARARGS, NULL},
	 { "fcomplex_r_get", _wrap_fcomplex_r_get, METH_O, NULL},
	 { "fcomplex_i_set", _wrap_fcomplex_i_set, METH_VARARGS, NULL},
	 { "fcomplex_i_get", _wrap_fcomplex_i_get, METH_O, NULL},
	 { "new_fcomplex", _wrap_new_fcomplex, METH_NOARGS, NULL},
	 { "delete_fcomplex", _wrap_delete_fcomplex, METH_O, NULL},
	 { "fcomplex_swigregister", fcomplex_swigregister, METH_O, NULL},
	 { "fcomplex_swiginit", fcomplex_swiginit, METH_VARARGS, NULL},
	 { "read_wisdom", _wrap_read_wisdom, METH_NOARGS, NULL},
	 { "good_factor", _wrap_good_factor, METH_O, NULL},
	 { "fftwcall", _wrap_fftwcall, METH_VARARGS, NULL},
	 { "tablesixstepfft", _wrap_tablesixstepfft, METH_VARARGS, NULL},
	 { "realfft", _wrap_realfft, METH_VARARGS, NULL},
	 { "infodata_ra_s_set", _wrap_infodata_ra_s_set, METH_VARARGS, NULL},
	 { "infodata_ra_s_get", _wrap_infodata_ra_s_get, METH_O, NULL},
	 { "infodata_dec_s_set", _wrap_infodata_dec_s_set, METH_VARARGS, NULL},
	 { "infodata_dec_s_get", _wrap_infodata_dec_s_get, METH_O, NULL},
	 { "infodata_N_set", _wrap_infodata_N_set, METH_VARARGS, NULL},
	 { "infodata_N_get", _wrap_infodata_N_get, METH_O, NULL},
	 { "infodata_dt_set", _wrap_infodata_dt_set, METH_VARARGS, NULL},
	 { "infodata_dt_get", _wrap_infodata_dt_get, METH_O, NULL},
	 { "infodata_fov_set", _wrap_infodata_fov_set, METH_VARARGS, NULL},
	 { "infodata_fov_get", _wrap_infodata_fov_get, METH_O, NULL},
	 { "infodata_mjd_f_set", _wrap_infodata_mjd_f_set, METH_VARARGS, NULL},
	 { "infodata_mjd_f_get", _wrap_infodata_mjd_f_get, METH_O, NULL},
	 { "infodata_dm_set", _wrap_infodata_dm_set, METH_VARARGS, NULL},
	 { "infodata_dm_get", _wrap_infodata_dm_get, METH_O, NULL},
	 { "infodata_freq_set", _wrap_infodata_freq_set, METH_VARARGS, NULL},
	 { "infodata_freq_get", _wrap_infodata_freq_get, METH_O, NULL},
	 { "infodata_freqband_set", _wrap_infodata_freqband_set, METH_VARARGS, NULL},
	 { "infodata_freqband_get", _wrap_infodata_freqband_get, METH_O, NULL},
	 { "infodata_chan_wid_set", _wrap_infodata_chan_wid_set, METH_VARARGS, NULL},
	 { "infodata_chan_wid_get", _wrap_infodata_chan_wid_get, METH_O, NULL},
	 { "infodata_wavelen_set", _wrap_infodata_wavelen_set, METH_VARARGS, NULL},
	 { "infodata_wavelen_get", _wrap_infodata_wavelen_get, METH_O, NULL},
	 { "infodata_waveband_set", _wrap_infodata_waveband_set, METH_VARARGS, NULL},
	 { "infodata_waveband_get", _wrap_infodata_waveband_get, METH_O, NULL},
	 { "infodata_energy_set", _wrap_infodata_energy_set, METH_VARARGS, NULL},
	 { "infodata_energy_get", _wrap_infodata_energy_get, METH_O, NULL},
	 { "infodata_energyband_set", _wrap_infodata_energyband_set, METH_VARARGS, NULL},
	 { "infodata_energyband_get", _wrap_infodata_energyband_get, METH_O, NULL},
	 { "infodata_num_chan_set", _wrap_infodata_num_chan_set, METH_VARARGS, NULL},
	 { "infodata_num_chan_get", _wrap_infodata_num_chan_get, METH_O, NULL},
	 { "infodata_mjd_i_set", _wrap_infodata_mjd_i_set, METH_VARARGS, NULL},
	 { "infodata_mjd_i_get", _wrap_infodata_mjd_i_get, METH_O, NULL},
	 { "infodata_ra_h_set", _wrap_infodata_ra_h_set, METH_VARARGS, NULL},
	 { "infodata_ra_h_get", _wrap_infodata_ra_h_get, METH_O, NULL},
	 { "infodata_ra_m_set", _wrap_infodata_ra_m_set, METH_VARARGS, NULL},
	 { "infodata_ra_m_get", _wrap_infodata_ra_m_get, METH_O, NULL},
	 { "infodata_dec_d_set", _wrap_infodata_dec_d_set, METH_VARARGS, NULL},
	 { "infodata_dec_d_get", _wrap_infodata_dec_d_get, METH_O, NULL},
	 { "infodata_dec_m_set", _wrap_infodata_dec_m_set, METH_VARARGS, NULL},
	 { "infodata_dec_m_get", _wrap_infodata_dec_m_get, METH_O, NULL},
	 { "infodata_bary_set", _wrap_infodata_bary_set, METH_VARARGS, NULL},
	 { "infodata_bary_get", _wrap_infodata_bary_get, METH_O, NULL},
	 { "infodata_numonoff_set", _wrap_infodata_numonoff_set, METH_VARARGS, NULL},
	 { "infodata_numonoff_get", _wrap_infodata_numonoff_get, METH_O, NULL},
	 { "infodata_notes_set", _wrap_infodata_notes_set, METH_VARARGS, NULL},
	 { "infodata_notes_get", _wrap_infodata_notes_get, METH_O, NULL},
	 { "infodata_name_set", _wrap_infodata_name_set, METH_VARARGS, NULL},
	 { "infodata_name_get", _wrap_infodata_name_get, METH_O, NULL},
	 { "infodata_object_set", _wrap_infodata_object_set, METH_VARARGS, NULL},
	 { "infodata_object_get", _wrap_infodata_object_get, METH_O, NULL},
	 { "infodata_instrument_set", _wrap_infodata_instrument_set, METH_VARARGS, NULL},
	 { "infodata_instrument_get", _wrap_infodata_instrument_get, METH_O, NULL},
	 { "infodata_observer_set", _wrap_infodata_observer_set, METH_VARARGS, NULL},
	 { "infodata_observer_get", _wrap_infodata_observer_get, METH_O, NULL},
	 { "infodata_analyzer_set", _wrap_infodata_analyzer_set, METH_VARARGS, NULL},
	 { "infodata_analyzer_get", _wrap_infodata_analyzer_get, METH_O, NULL},
	 { "infodata_telescope_set", _wrap_infodata_telescope_set, METH_VARARGS, NULL},
	 { "infodata_telescope_get", _wrap_infodata_telescope_get, METH_O, NULL},
	 { "infodata_band_set", _wrap_infodata_band_set, METH_VARARGS, NULL},
	 { "infodata_band_get", _wrap_infodata_band_get, METH_O, NULL},
	 { "infodata_filt_set", _wrap_infodata_filt_set, METH_VARARGS, NULL},
	 { "infodata_filt_get", _wrap_infodata_filt_get, METH_O, NULL},
	 { "new_infodata", _wrap_new_infodata, METH_NOARGS, NULL},
	 { "delete_infodata", _wrap_delete_infodata, METH_O, NULL},
	 { "infodata_swigregister", infodata_swigregister, METH_O, NULL},
	 { "infodata_swiginit", infodata_swiginit, METH_VARARGS, NULL},
	 { "readinf", _wrap_readinf, METH_VARARGS, NULL},
	 { "writeinf", _wrap_writeinf, METH_O, NULL},
	 { "orbitparams_p_set", _wrap_orbitparams_p_set, METH_VARARGS, NULL},
	 { "orbitparams_p_get", _wrap_orbitparams_p_get, METH_O, NULL},
	 { "orbitparams_e_set", _wrap_orbitparams_e_set, METH_VARARGS, NULL},
	 { "orbitparams_e_get", _wrap_orbitparams_e_get, METH_O, NULL},
	 { "orbitparams_x_set", _wrap_orbitparams_x_set, METH_VARARGS, NULL},
	 { "orbitparams_x_get", _wrap_orbitparams_x_get, METH_O, NULL},
	 { "orbitparams_w_set", _wrap_orbitparams_w_set, METH_VARARGS, NULL},
	 { "orbitparams_w_get", _wrap_orbitparams_w_get, METH_O, NULL},
	 { "orbitparams_t_set", _wrap_orbitparams_t_set, METH_VARARGS, NULL},
	 { "orbitparams_t_get", _wrap_orbitparams_t_get, METH_O, NULL},
	 { "orbitparams_pd_set", _wrap_orbitparams_pd_set, METH_VARARGS, NULL},
	 { "orbitparams_pd_get", _wrap_orbitparams_pd_get, METH_O, NULL},
	 { "orbitparams_wd_set", _wrap_orbitparams_wd_set, METH_VARARGS, NULL},
	 { "orbitparams_wd_get", _wrap_orbitparams_wd_get, METH_O, NULL},
	 { "new_orbitparams", _wrap_new_orbitparams, METH_NOARGS, NULL},
	 { "delete_orbitparams", _wrap_delete_orbitparams, METH_O, NULL},
	 { "orbitparams_swigregister", orbitparams_swigregister, METH_O, NULL},
	 { "orbitparams_swiginit", orbitparams_swiginit, METH_VARARGS, NULL},
	 { "psrparams_jname_set", _wrap_psrparams_jname_set, METH_VARARGS, NULL},
	 { "psrparams_jname_get", _wrap_psrparams_jname_get, METH_O, NULL},
	 { "psrparams_bname_set", _wrap_psrparams_bname_set, METH_VARARGS, NULL},
	 { "psrparams_bname_get", _wrap_psrparams_bname_get, METH_O, NULL},
	 { "psrparams_alias_set", _wrap_psrparams_alias_set, METH_VARARGS, NULL},
	 { "psrparams_alias_get", _wrap_psrparams_alias_get, METH_O, NULL},
	 { "psrparams_ra2000_set", _wrap_psrparams_ra2000_set, METH_VARARGS, NULL},
	 { "psrparams_ra2000_get", _wrap_psrparams_ra2000_get, METH_O, NULL},
	 { "psrparams_dec2000_set", _wrap_psrparams_dec2000_set, METH_VARARGS, NULL},
	 { "psrparams_dec2000_get", _wrap_psrparams_dec2000_get, METH_O, NULL},
	 { "psrparams_dm_set", _wrap_psrparams_dm_set, METH_VARARGS, NULL},
	 { "psrparams_dm_get", _wrap_psrparams_dm_get, METH_O, NULL},
	 { "psrparams_timepoch_set", _wrap_psrparams_timepoch_set, METH_VARARGS, NULL},
	 { "psrparams_timepoch_get", _wrap_psrparams_timepoch_get, METH_O, NULL},
	 { "psrparams_p_set", _wrap_psrparams_p_set, METH_VARARGS, NULL},
	 { "psrparams_p_get", _wrap_psrparams_p_get, METH_O, NULL},
	 { "psrparams_pd_set", _wrap_psrparams_pd_set, METH_VARARGS, NULL},
	 { "psrparams_pd_get", _wrap_psrparams_pd_get, METH_O, NULL},
	 { "psrparams_pdd_set", _wrap_psrparams_pdd_set, METH_VARARGS, NULL},
	 { "psrparams_pdd_get", _wrap_psrparams_pdd_get, METH_O, NULL},
	 { "psrparams_f_set", _wrap_psrparams_f_set, METH_VARARGS, NULL},
	 { "psrparams_f_get", _wrap_psrparams_f_get, METH_O, NULL},
	 { "psrparams_fd_set", _wrap_psrparams_fd_set, METH_VARARGS, NULL},
	 { "psrparams_fd_get", _wrap_psrparams_fd_get, METH_O, NULL},
	 { "psrparams_fdd_set", _wrap_psrparams_fdd_set, METH_VARARGS, NULL},
	 { "psrparams_fdd_get", _wrap_psrparams_fdd_get, METH_O, NULL},
	 { "psrparams_orb_set", _wrap_psrparams_orb_set, METH_VARARGS, NULL},
	 { "psrparams_orb_get", _wrap_psrparams_orb_get, METH_O, NULL},
	 { "new_psrparams", _wrap_new_psrparams, METH_NOARGS, NULL},
	 { "delete_psrparams", _wrap_delete_psrparams, METH_O, NULL},
	 { "psrparams_swigregister", psrparams_swigregister, METH_O, NULL},
	 { "psrparams_swiginit", psrparams_swiginit, METH_VARARGS, NULL},
	 { "get_psr_at_epoch", _wrap_get_psr_at_epoch, METH_VARARGS, NULL},
	 { "get_psr_from_parfile", _wrap_get_psr_from_parfile, METH_VARARGS, NULL},
	 { "mjd_to_datestr", _wrap_mjd_to_datestr, METH_VARARGS, NULL},
	 { "fresnl", _wrap_fresnl, METH_O, NULL},
	 { "rderivs_pow_set", _wrap_rderivs_pow_set, METH_VARARGS, NULL},
	 { "rderivs_pow_get", _wrap_rderivs_pow_get, METH_O, NULL},
	 { "rderivs_phs_set", _wrap_rderivs_phs_set, METH_VARARGS, NULL},
	 { "rderivs_phs_get", _wrap_rderivs_phs_get, METH_O, NULL},
	 { "rderivs_dpow_set", _wrap_rderivs_dpow_set, METH_VARARGS, NULL},
	 { "rderivs_dpow_get", _wrap_rderivs_dpow_get, METH_O, NULL},
	 { "rderivs_dphs_set", _wrap_rderivs_dphs_set, METH_VARARGS, NULL},
	 { "rderivs_dphs_get", _wrap_rderivs_dphs_get, METH_O, NULL},
	 { "rderivs_d2pow_set", _wrap_rderivs_d2pow_set, METH_VARARGS, NULL},
	 { "rderivs_d2pow_get", _wrap_rderivs_d2pow_get, METH_O, NULL},
	 { "rderivs_d2phs_set", _wrap_rderivs_d2phs_set, METH_VARARGS, NULL},
	 { "rderivs_d2phs_get", _wrap_rderivs_d2phs_get, METH_O, NULL},
	 { "rderivs_locpow_set", _wrap_rderivs_locpow_set, METH_VARARGS, NULL},
	 { "rderivs_locpow_get", _wrap_rderivs_locpow_get, METH_O, NULL},
	 { "new_rderivs", _wrap_new_rderivs, METH_NOARGS, NULL},
	 { "delete_rderivs", _wrap_delete_rderivs, METH_O, NULL},
	 { "rderivs_swigregister", rderivs_swigregister, METH_O, NULL},
	 { "rderivs_swiginit", rderivs_swiginit, METH_VARARGS, NULL},
	 { "fourierprops_r_set", _wrap_fourierprops_r_set, METH_VARARGS, NULL},
	 { "fourierprops_r_get", _wrap_fourierprops_r_get, METH_O, NULL},
	 { "fourierprops_rerr_set", _wrap_fourierprops_rerr_set, METH_VARARGS, NULL},
	 { "fourierprops_rerr_get", _wrap_fourierprops_rerr_get, METH_O, NULL},
	 { "fourierprops_z_set", _wrap_fourierprops_z_set, METH_VARARGS, NULL},
	 { "fourierprops_z_get", _wrap_fourierprops_z_get, METH_O, NULL},
	 { "fourierprops_zerr_set", _wrap_fourierprops_zerr_set, METH_VARARGS, NULL},
	 { "fourierprops_zerr_get", _wrap_fourierprops_zerr_get, METH_O, NULL},
	 { "fourierprops_w_set", _wrap_fourierprops_w_set, METH_VARARGS, NULL},
	 { "fourierprops_w_get", _wrap_fourierprops_w_get, METH_O, NULL},
	 { "fourierprops_werr_set", _wrap_fourierprops_werr_set, METH_VARARGS, NULL},
	 { "fourierprops_werr_get", _wrap_fourierprops_werr_get, METH_O, NULL},
	 { "fourierprops_pow_set", _wrap_fourierprops_pow_set, METH_VARARGS, NULL},
	 { "fourierprops_pow_get", _wrap_fourierprops_pow_get, METH_O, NULL},
	 { "fourierprops_powerr_set", _wrap_fourierprops_powerr_set, METH_VARARGS, NULL},
	 { "fourierprops_powerr_get", _wrap_fourierprops_powerr_get, METH_O, NULL},
	 { "fourierprops_sig_set", _wrap_fourierprops_sig_set, METH_VARARGS, NULL},
	 { "fourierprops_sig_get", _wrap_fourierprops_sig_get, METH_O, NULL},
	 { "fourierprops_rawpow_set", _wrap_fourierprops_rawpow_set, METH_VARARGS, NULL},
	 { "fourierprops_rawpow_get", _wrap_fourierprops_rawpow_get, METH_O, NULL},
	 { "fourierprops_phs_set", _wrap_fourierprops_phs_set, METH_VARARGS, NULL},
	 { "fourierprops_phs_get", _wrap_fourierprops_phs_get, METH_O, NULL},
	 { "fourierprops_phserr_set", _wrap_fourierprops_phserr_set, METH_VARARGS, NULL},
	 { "fourierprops_phserr_get", _wrap_fourierprops_phserr_get, METH_O, NULL},
	 { "fourierprops_cen_set", _wrap_fourierprops_cen_set, METH_VARARGS, NULL},
	 { "fourierprops_cen_get", _wrap_fourierprops_cen_get, METH_O, NULL},
	 { "fourierprops_cenerr_set", _wrap_fourierprops_cenerr_set, METH_VARARGS, NULL},
	 { "fourierprops_cenerr_get", _wrap_fourierprops_cenerr_get, METH_O, NULL},
	 { "fourierprops_pur_set", _wrap_fourierprops_pur_set, METH_VARARGS, NULL},
	 { "fourierprops_pur_get", _wrap_fourierprops_pur_get, METH_O, NULL},
	 { "fourierprops_purerr_set", _wrap_fourierprops_purerr_set, METH_VARARGS, NULL},
	 { "fourierprops_purerr_get", _wrap_fourierprops_purerr_get, METH_O, NULL},
	 { "fourierprops_locpow_set", _wrap_fourierprops_locpow_set, METH_VARARGS, NULL},
	 { "fourierprops_locpow_get", _wrap_fourierprops_locpow_get, METH_O, NULL},
	 { "new_fourierprops", _wrap_new_fourierprops, METH_NOARGS, NULL},
	 { "delete_fourierprops", _wrap_delete_fourierprops, METH_O, NULL},
	 { "fourierprops_swigregister", fourierprops_swigregister, METH_O, NULL},
	 { "fourierprops_swiginit", fourierprops_swiginit, METH_VARARGS, NULL},
	 { "foldstats_numdata_set", _wrap_foldstats_numdata_set, METH_VARARGS, NULL},
	 { "foldstats_numdata_get", _wrap_foldstats_numdata_get, METH_O, NULL},
	 { "foldstats_data_avg_set", _wrap_foldstats_data_avg_set, METH_VARARGS, NULL},
	 { "foldstats_data_avg_get", _wrap_foldstats_data_avg_get, METH_O, NULL},
	 { "foldstats_data_var_set", _wrap_foldstats_data_var_set, METH_VARARGS, NULL},
	 { "foldstats_data_var_get", _wrap_foldstats_data_var_get, METH_O, NULL},
	 { "foldstats_numprof_set", _wrap_foldstats_numprof_set, METH_VARARGS, NULL},
	 { "foldstats_numprof_get", _wrap_foldstats_numprof_get, METH_O, NULL},
	 { "foldstats_prof_avg_set", _wrap_foldstats_prof_avg_set, METH_VARARGS, NULL},
	 { "foldstats_prof_avg_get", _wrap_foldstats_prof_avg_get, METH_O, NULL},
	 { "foldstats_prof_var_set", _wrap_foldstats_prof_var_set, METH_VARARGS, NULL},
	 { "foldstats_prof_var_get", _wrap_foldstats_prof_var_get, METH_O, NULL},
	 { "foldstats_redchi_set", _wrap_foldstats_redchi_set, METH_VARARGS, NULL},
	 { "foldstats_redchi_get", _wrap_foldstats_redchi_get, METH_O, NULL},
	 { "new_foldstats", _wrap_new_foldstats, METH_NOARGS, NULL},
	 { "delete_foldstats", _wrap_delete_foldstats, METH_O, NULL},
	 { "foldstats_swigregister", foldstats_swigregister, METH_O, NULL},
	 { "foldstats_swiginit", foldstats_swiginit, METH_VARARGS, NULL},
	 { "gen_fvect", _wrap_gen_fvect, METH_O, NULL},
	 { "gen_cvect", _wrap_gen_cvect, METH_O, NULL},
	 { "power_arr", _wrap_power_arr, METH_O, NULL},
	 { "phase_arr", _wrap_phase_arr, METH_O, NULL},
	 { "frotate", _wrap_frotate, METH_VARARGS, NULL},
	 { "drotate", _wrap_drotate, METH_VARARGS, NULL},
	 { "keplers_eqn", _wrap_keplers_eqn, METH_VARARGS, NULL},
	 { "E_to_phib", _wrap_E_to_phib, METH_VARARGS, NULL},
	 { "E_to_v", _wrap_E_to_v, METH_VARARGS, NULL},
	 { "E_to_p", _wrap_E_to_p, METH_VARARGS, NULL},
	 { "E_to_z", _wrap_E_to_z, METH_VARARGS, NULL},
	 { "E_to_phib_BT", _wrap_E_to_phib_BT, METH_VARARGS, NULL},
	 { "dorbint", _wrap_dorbint, METH_VARARGS, NULL},
	 { "binary_velocity", _wrap_binary_velocity, METH_VARARGS, NULL},
	 { "r_resp_halfwidth", _wrap_r_resp_halfwidth, METH_O, NULL},
	 { "z_resp_halfwidth", _wrap_z_resp_halfwidth, METH_VARARGS, NULL},
	 { "w_resp_halfwidth", _wrap_w_resp_halfwidth, METH_VARARGS, NULL},
	 { "bin_resp_halfwidth", _wrap_bin_resp_halfwidth, METH_VARARGS, NULL},
	 { "gen_r_response", _wrap_gen_r_response, METH_VARARGS, NULL},
	 { "gen_z_response", _wrap_gen_z_response, METH_VARARGS, NULL},
	 { "gen_w_response", _wrap_gen_w_response, METH_VARARGS, NULL},
	 { "gen_w_response2", _wrap_gen_w_response2, METH_VARARGS, NULL},
	 { "gen_bin_response", _wrap_gen_bin_response, METH_VARARGS, NULL},
	 { "get_localpower", _wrap_get_localpower, METH_VARARGS, NULL},
	 { "get_localpower3d", _wrap_get_localpower3d, METH_VARARGS, NULL},
	 { "get_derivs3d", _wrap_get_derivs3d, METH_VARARGS, NULL},
	 { "calc_props", _wrap_calc_props, METH_VARARGS, NULL},
	 { "calc_binprops", _wrap_calc_binprops, METH_VARARGS, NULL},
	 { "calc_rzwerrs", _wrap_calc_rzwerrs, METH_VARARGS, NULL},
	 { "extended_equiv_gaussian_sigma", _wrap_extended_equiv_gaussian_sigma, METH_O, NULL},
	 { "log_asymtotic_incomplete_gamma", _wrap_log_asymtotic_incomplete_gamma, METH_VARARGS, NULL},
	 { "log_asymtotic_gamma", _wrap_log_asymtotic_gamma, METH_O, NULL},
	 { "equivalent_gaussian_sigma", _wrap_equivalent_gaussian_sigma, METH_O, NULL},
	 { "chi2_logp", _wrap_chi2_logp, METH_VARARGS, NULL},
	 { "chi2_sigma", _wrap_chi2_sigma, METH_VARARGS, NULL},
	 { "candidate_sigma", _wrap_candidate_sigma, METH_VARARGS, NULL},
	 { "power_for_sigma", _wrap_power_for_sigma, METH_VARARGS, NULL},
	 { "switch_f_and_p", _wrap_switch_f_and_p, METH_VARARGS, NULL},
	 { "chisqr", _wrap_chisqr, METH_VARARGS, NULL},
	 { "z2n", _wrap_z2n, METH_VARARGS, NULL},
	 { "print_candidate", _wrap_print_candidate, METH_VARARGS, NULL},
	 { "print_bin_candidate", _wrap_print_bin_candidate, METH_VARARGS, NULL},
	 { "fopen", _wrap_fopen, METH_VARARGS, NULL},
	 { "fputs", _wrap_fputs, METH_VARARGS, NULL},
	 { "fclose", _wrap_fclose, METH_O, NULL},
	 { "fseek", _wrap_fseek, METH_VARARGS, NULL},
	 { "read_rzw_cand", _wrap_read_rzw_cand, METH_VARARGS, NULL},
	 { "get_rzw_cand", _wrap_get_rzw_cand, METH_VARARGS, NULL},
	 { "read_bin_cand", _wrap_read_bin_cand, METH_VARARGS, NULL},
	 { "get_bin_cand", _wrap_get_bin_cand, METH_VARARGS, NULL},
	 { "next2_to_n", _wrap_next2_to_n, METH_O, NULL},
	 { "is_power_of_10", _wrap_is_power_of_10, METH_O, NULL},
	 { "choose_good_N", _wrap_choose_good_N, METH_O, NULL},
	 { "dms2rad", _wrap_dms2rad, METH_VARARGS, NULL},
	 { "hms2rad", _wrap_hms2rad, METH_VARARGS, NULL},
	 { "hours2hms", _wrap_hours2hms, METH_O, NULL},
	 { "deg2dms", _wrap_deg2dms, METH_O, NULL},
	 { "sphere_ang_diff", _wrap_sphere_ang_diff, METH_VARARGS, NULL},
	 { "rz_interp", _wrap_rz_interp, METH_VARARGS, NULL},
	 { "corr_rz_plane", _wrap_corr_rz_plane, METH_VARARGS, NULL},
	 { "corr_rzw_vol", _wrap_corr_rzw_vol, METH_VARARGS, NULL},
	 { "max_r_arr", _wrap_max_r_arr, METH_VARARGS, NULL},
	 { "max_rz_arr", _wrap_max_rz_arr, METH_VARARGS, NULL},
	 { "max_rz_arr_harmonics", _wrap_max_rz_arr_harmonics, METH_VARARGS, NULL},
	 { "max_rzw_arr_harmonics", _wrap_max_rzw_arr_harmonics, METH_VARARGS, NULL},
	 { "max_rzw_arr", _wrap_max_rzw_arr, METH_VARARGS, NULL},
	 { "barycenter", _wrap_barycenter, METH_VARARGS, NULL},
	 { "DOF_corr", _wrap_DOF_corr, METH_O, NULL},
	 { "simplefold", _wrap_simplefold, METH_VARARGS, NULL},
	 { "nice_output_1", _wrap_nice_output_1, METH_VARARGS, NULL},
	 { "nice_output_2", _wrap_nice_output_2, METH_VARARGS, NULL},
	 { NULL, NULL, 0, NULL }
};


/* -------- TYPE CONVERSION AND EQUIVALENCE RULES (BEGIN) -------- */

static swig_type_info _swigt__p_FCOMPLEX = {"_p_FCOMPLEX", "fcomplex *|struct FCOMPLEX *|FCOMPLEX *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_FILE = {"_p_FILE", "FILE *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_FOURIERPROPS = {"_p_FOURIERPROPS", "fourierprops *|struct FOURIERPROPS *|FOURIERPROPS *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_INFODATA = {"_p_INFODATA", "infodata *|struct INFODATA *|INFODATA *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_PSRPARAMS = {"_p_PSRPARAMS", "psrparams *|struct PSRPARAMS *|PSRPARAMS *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_RDERIVS = {"_p_RDERIVS", "rderivs *|struct RDERIVS *|RDERIVS *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_binaryprops = {"_p_binaryprops", "binaryprops *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_char = {"_p_char", "char *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_double = {"_p_double", "double *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_float = {"_p_float", "float *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_foldstats = {"_p_foldstats", "foldstats *|struct foldstats *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_int = {"_p_int", "int *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_long = {"_p_long", "long *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_orbitparams = {"_p_orbitparams", "orbitparams *|struct orbitparams *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_p_FCOMPLEX = {"_p_p_FCOMPLEX", "fcomplex **|struct FCOMPLEX **", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_p_double = {"_p_p_double", "double **", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_p_float = {"_p_p_float", "float **", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_presto_checkaliased = {"_p_presto_checkaliased", "presto_checkaliased *|enum presto_checkaliased *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_presto_datainf = {"_p_presto_datainf", "presto_datainf *|enum presto_datainf *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_presto_ffts = {"_p_presto_ffts", "presto_ffts *|enum presto_ffts *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_presto_interp_acc = {"_p_presto_interp_acc", "presto_interp_acc *|enum presto_interp_acc *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_presto_interptype = {"_p_presto_interptype", "presto_interptype *|enum presto_interptype *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_presto_optype = {"_p_presto_optype", "presto_optype *|enum presto_optype *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_rzwerrs = {"_p_rzwerrs", "rzwerrs *", 0, 0, (void*)0, 0};

static swig_type_info *swig_type_initial[] = {
  &_swigt__p_FCOMPLEX,
  &_swigt__p_FILE,
  &_swigt__p_FOURIERPROPS,
  &_swigt__p_INFODATA,
  &_swigt__p_PSRPARAMS,
  &_swigt__p_RDERIVS,
  &_swigt__p_binaryprops,
  &_swigt__p_char,
  &_swigt__p_double,
  &_swigt__p_float,
  &_swigt__p_foldstats,
  &_swigt__p_int,
  &_swigt__p_long,
  &_swigt__p_orbitparams,
  &_swigt__p_p_FCOMPLEX,
  &_swigt__p_p_double,
  &_swigt__p_p_float,
  &_swigt__p_presto_checkaliased,
  &_swigt__p_presto_datainf,
  &_swigt__p_presto_ffts,
  &_swigt__p_presto_interp_acc,
  &_swigt__p_presto_interptype,
  &_swigt__p_presto_optype,
  &_swigt__p_rzwerrs,
};

static swig_cast_info _swigc__p_FCOMPLEX[] = {  {&_swigt__p_FCOMPLEX, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_FILE[] = {  {&_swigt__p_FILE, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_FOURIERPROPS[] = {  {&_swigt__p_FOURIERPROPS, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_INFODATA[] = {  {&_swigt__p_INFODATA, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_PSRPARAMS[] = {  {&_swigt__p_PSRPARAMS, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_RDERIVS[] = {  {&_swigt__p_RDERIVS, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_binaryprops[] = {  {&_swigt__p_binaryprops, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_char[] = {  {&_swigt__p_char, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_double[] = {  {&_swigt__p_double, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_float[] = {  {&_swigt__p_float, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_foldstats[] = {  {&_swigt__p_foldstats, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_int[] = {  {&_swigt__p_int, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_long[] = {  {&_swigt__p_long, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_orbitparams[] = {  {&_swigt__p_orbitparams, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_p_FCOMPLEX[] = {  {&_swigt__p_p_FCOMPLEX, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_p_double[] = {  {&_swigt__p_p_double, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_p_float[] = {  {&_swigt__p_p_float, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_presto_checkaliased[] = {  {&_swigt__p_presto_checkaliased, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_presto_datainf[] = {  {&_swigt__p_presto_datainf, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_presto_ffts[] = {  {&_swigt__p_presto_ffts, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_presto_interp_acc[] = {  {&_swigt__p_presto_interp_acc, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_presto_interptype[] = {  {&_swigt__p_presto_interptype, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_presto_optype[] = {  {&_swigt__p_presto_optype, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_rzwerrs[] = {  {&_swigt__p_rzwerrs, 0, 0, 0},{0, 0, 0, 0}};

static swig_cast_info *swig_cast_initial[] = {
  _swigc__p_FCOMPLEX,
  _swigc__p_FILE,
  _swigc__p_FOURIERPROPS,
  _swigc__p_INFODATA,
  _swigc__p_PSRPARAMS,
  _swigc__p_RDERIVS,
  _swigc__p_binaryprops,
  _swigc__p_char,
  _swigc__p_double,
  _swigc__p_float,
  _swigc__p_foldstats,
  _swigc__p_int,
  _swigc__p_long,
  _swigc__p_orbitparams,
  _swigc__p_p_FCOMPLEX,
  _swigc__p_p_double,
  _swigc__p_p_float,
  _swigc__p_presto_checkaliased,
  _swigc__p_presto_datainf,
  _swigc__p_presto_ffts,
  _swigc__p_presto_interp_acc,
  _swigc__p_presto_interptype,
  _swigc__p_presto_optype,
  _swigc__p_rzwerrs,
};


/* -------- TYPE CONVERSION AND EQUIVALENCE RULES (END) -------- */

static swig_const_info swig_const_table[] = {
{0, 0, 0, 0.0, 0, 0}};

#ifdef __cplusplus
}
#endif
/* -----------------------------------------------------------------------------
 * Type initialization:
 * This problem is tough by the requirement that no dynamic
 * memory is used. Also, since swig_type_info structures store pointers to
 * swig_cast_info structures and swig_cast_info structures store pointers back
 * to swig_type_info structures, we need some lookup code at initialization.
 * The idea is that swig generates all the structures that are needed.
 * The runtime then collects these partially filled structures.
 * The SWIG_InitializeModule function takes these initial arrays out of
 * swig_module, and does all the lookup, filling in the swig_module.types
 * array with the correct data and linking the correct swig_cast_info
 * structures together.
 *
 * The generated swig_type_info structures are assigned statically to an initial
 * array. We just loop through that array, and handle each type individually.
 * First we lookup if this type has been already loaded, and if so, use the
 * loaded structure instead of the generated one. Then we have to fill in the
 * cast linked list. The cast data is initially stored in something like a
 * two-dimensional array. Each row corresponds to a type (there are the same
 * number of rows as there are in the swig_type_initial array). Each entry in
 * a column is one of the swig_cast_info structures for that type.
 * The cast_initial array is actually an array of arrays, because each row has
 * a variable number of columns. So to actually build the cast linked list,
 * we find the array of casts associated with the type, and loop through it
 * adding the casts to the list. The one last trick we need to do is making
 * sure the type pointer in the swig_cast_info struct is correct.
 *
 * First off, we lookup the cast->type name to see if it is already loaded.
 * There are three cases to handle:
 *  1) If the cast->type has already been loaded AND the type we are adding
 *     casting info to has not been loaded (it is in this module), THEN we
 *     replace the cast->type pointer with the type pointer that has already
 *     been loaded.
 *  2) If BOTH types (the one we are adding casting info to, and the
 *     cast->type) are loaded, THEN the cast info has already been loaded by
 *     the previous module so we just ignore it.
 *  3) Finally, if cast->type has not already been loaded, then we add that
 *     swig_cast_info to the linked list (because the cast->type) pointer will
 *     be correct.
 * ----------------------------------------------------------------------------- */

#ifdef __cplusplus
extern "C" {
#if 0
} /* c-mode */
#endif
#endif

#if 0
#define SWIGRUNTIME_DEBUG
#endif

#ifndef SWIG_INIT_CLIENT_DATA_TYPE
#define SWIG_INIT_CLIENT_DATA_TYPE void *
#endif

SWIGRUNTIME void
SWIG_InitializeModule(SWIG_INIT_CLIENT_DATA_TYPE clientdata) {
  size_t i;
  swig_module_info *module_head, *iter;
  int init;
  
  /* check to see if the circular list has been setup, if not, set it up */
  if (swig_module.next==0) {
    /* Initialize the swig_module */
    swig_module.type_initial = swig_type_initial;
    swig_module.cast_initial = swig_cast_initial;
    swig_module.next = &swig_module;
    init = 1;
  } else {
    init = 0;
  }
  
  /* Try and load any already created modules */
  module_head = SWIG_GetModule(clientdata);
  if (!module_head) {
    /* This is the first module loaded for this interpreter */
    /* so set the swig module into the interpreter */
    SWIG_SetModule(clientdata, &swig_module);
  } else {
    /* the interpreter has loaded a SWIG module, but has it loaded this one? */
    iter=module_head;
    do {
      if (iter==&swig_module) {
        /* Our module is already in the list, so there's nothing more to do. */
        return;
      }
      iter=iter->next;
    } while (iter!= module_head);
    
    /* otherwise we must add our module into the list */
    swig_module.next = module_head->next;
    module_head->next = &swig_module;
  }
  
  /* When multiple interpreters are used, a module could have already been initialized in
       a different interpreter, but not yet have a pointer in this interpreter.
       In this case, we do not want to continue adding types... everything should be
       set up already */
  if (init == 0) return;
  
  /* Now work on filling in swig_module.types */
#ifdef SWIGRUNTIME_DEBUG
  printf("SWIG_InitializeModule: size %lu\n", (unsigned long)swig_module.size);
#endif
  for (i = 0; i < swig_module.size; ++i) {
    swig_type_info *type = 0;
    swig_type_info *ret;
    swig_cast_info *cast;
    
#ifdef SWIGRUNTIME_DEBUG
    printf("SWIG_InitializeModule: type %lu %s\n", (unsigned long)i, swig_module.type_initial[i]->name);
#endif
    
    /* if there is another module already loaded */
    if (swig_module.next != &swig_module) {
      type = SWIG_MangledTypeQueryModule(swig_module.next, &swig_module, swig_module.type_initial[i]->name);
    }
    if (type) {
      /* Overwrite clientdata field */
#ifdef SWIGRUNTIME_DEBUG
      printf("SWIG_InitializeModule: found type %s\n", type->name);
#endif
      if (swig_module.type_initial[i]->clientdata) {
        type->clientdata = swig_module.type_initial[i]->clientdata;
#ifdef SWIGRUNTIME_DEBUG
        printf("SWIG_InitializeModule: found and overwrite type %s \n", type->name);
#endif
      }
    } else {
      type = swig_module.type_initial[i];
    }
    
    /* Insert casting types */
    cast = swig_module.cast_initial[i];
    while (cast->type) {
      /* Don't need to add information already in the list */
      ret = 0;
#ifdef SWIGRUNTIME_DEBUG
      printf("SWIG_InitializeModule: look cast %s\n", cast->type->name);
#endif
      if (swig_module.next != &swig_module) {
        ret = SWIG_MangledTypeQueryModule(swig_module.next, &swig_module, cast->type->name);
#ifdef SWIGRUNTIME_DEBUG
        if (ret) printf("SWIG_InitializeModule: found cast %s\n", ret->name);
#endif
      }
      if (ret) {
        if (type == swig_module.type_initial[i]) {
#ifdef SWIGRUNTIME_DEBUG
          printf("SWIG_InitializeModule: skip old type %s\n", ret->name);
#endif
          cast->type = ret;
          ret = 0;
        } else {
          /* Check for casting already in the list */
          swig_cast_info *ocast = SWIG_TypeCheck(ret->name, type);
#ifdef SWIGRUNTIME_DEBUG
          if (ocast) printf("SWIG_InitializeModule: skip old cast %s\n", ret->name);
#endif
          if (!ocast) ret = 0;
        }
      }
      
      if (!ret) {
#ifdef SWIGRUNTIME_DEBUG
        printf("SWIG_InitializeModule: adding cast %s\n", cast->type->name);
#endif
        if (type->cast) {
          type->cast->prev = cast;
          cast->next = type->cast;
        }
        type->cast = cast;
      }
      cast++;
    }
    /* Set entry in modules->types array equal to the type */
    swig_module.types[i] = type;
  }
  swig_module.types[i] = 0;
  
#ifdef SWIGRUNTIME_DEBUG
  printf("**** SWIG_InitializeModule: Cast List ******\n");
  for (i = 0; i < swig_module.size; ++i) {
    int j = 0;
    swig_cast_info *cast = swig_module.cast_initial[i];
    printf("SWIG_InitializeModule: type %lu %s\n", (unsigned long)i, swig_module.type_initial[i]->name);
    while (cast->type) {
      printf("SWIG_InitializeModule: cast type %s\n", cast->type->name);
      cast++;
      ++j;
    }
    printf("---- Total casts: %d\n",j);
  }
  printf("**** SWIG_InitializeModule: Cast List ******\n");
#endif
}

/* This function will propagate the clientdata field of type to
* any new swig_type_info structures that have been added into the list
* of equivalent types.  It is like calling
* SWIG_TypeClientData(type, clientdata) a second time.
*/
SWIGRUNTIME void
SWIG_PropagateClientData(void) {
  size_t i;
  swig_cast_info *equiv;
  static int init_run = 0;
  
  if (init_run) return;
  init_run = 1;
  
  for (i = 0; i < swig_module.size; i++) {
    if (swig_module.types[i]->clientdata) {
      equiv = swig_module.types[i]->cast;
      while (equiv) {
        if (!equiv->converter) {
          if (equiv->type && !equiv->type->clientdata)
          SWIG_TypeClientData(equiv->type, swig_module.types[i]->clientdata);
        }
        equiv = equiv->next;
      }
    }
  }
}

#ifdef __cplusplus
#if 0
{
  /* c-mode */
#endif
}
#endif



#ifdef __cplusplus
extern "C" {
#endif
  
  /* -----------------------------------------------------------------------------
   * constants/methods manipulation
   * ----------------------------------------------------------------------------- */
  
  /* Install Constants */
  SWIGINTERN void
  SWIG_Python_InstallConstants(PyObject *d, swig_const_info constants[]) {
    PyObject *obj = 0;
    size_t i;
    for (i = 0; constants[i].type; ++i) {
      switch(constants[i].type) {
      case SWIG_PY_POINTER:
        obj = SWIG_InternalNewPointerObj(constants[i].pvalue, *(constants[i]).ptype,0);
        break;
      case SWIG_PY_BINARY:
        obj = SWIG_NewPackedObj(constants[i].pvalue, constants[i].lvalue, *(constants[i].ptype));
        break;
      default:
        obj = 0;
        break;
      }
      if (obj) {
        PyDict_SetItemString(d, constants[i].name, obj);
        SWIG_Py_DECREF(obj);
      }
    }
  }
  
  /* -----------------------------------------------------------------------------
   * Patch %callback methods' docstrings to hold the callback ptrs
   * -----------------------------------------------------------------------------*/
  
  SWIGINTERN void
  SWIG_Python_FixMethods(PyMethodDef *methods, const swig_const_info *const_table, swig_type_info **types, swig_type_info **types_initial) {
    size_t i;
    for (i = 0; methods[i].ml_name; ++i) {
      const char *c = methods[i].ml_doc;
      if (!c) continue;
      c = strstr(c, "swig_ptr: ");
      if (c) {
        int j;
        const swig_const_info *ci = 0;
        const char *name = c + 10;
        for (j = 0; const_table[j].type; ++j) {
          if (strncmp(const_table[j].name, name, 
              strlen(const_table[j].name)) == 0) {
            ci = &(const_table[j]);
            break;
          }
        }
        if (ci) {
          void *ptr = (ci->type == SWIG_PY_POINTER) ? ci->pvalue : 0;
          if (ptr) {
            size_t shift = (ci->ptype) - types;
            swig_type_info *ty = types_initial[shift];
            size_t ldoc = (c - methods[i].ml_doc);
            size_t lptr = strlen(ty->name)+2*sizeof(void*)+2;
            char *ndoc = (char*)malloc(ldoc + lptr + 10);
            if (ndoc) {
              char *buff = ndoc;
              memcpy(buff, methods[i].ml_doc, ldoc);
              buff += ldoc;
              memcpy(buff, "swig_ptr: ", 10);
              buff += 10;
              SWIG_PackVoidPtr(buff, ptr, ty->name, lptr);
              methods[i].ml_doc = ndoc;
            }
          }
        }
      }
    }
  } 
  
#ifdef __cplusplus
}
#endif




/* -----------------------------------------------------------------------------*
 *  Partial Init method
 * -----------------------------------------------------------------------------*/

#ifdef __cplusplus
extern "C"
#endif

SWIGEXPORT 
#if PY_VERSION_HEX >= 0x03000000
PyObject*
#else
void
#endif
SWIG_init(void) {
  PyObject *m, *d, *md, *globals;
  
#if PY_VERSION_HEX >= 0x03000000
  static struct PyModuleDef SWIG_module = {
    PyModuleDef_HEAD_INIT,
    SWIG_name,
    NULL,
    -1,
    SwigMethods,
    NULL,
    NULL,
    NULL,
    NULL
  };
#endif
  
#if defined(SWIGPYTHON_BUILTIN)
  static SwigPyClientData SwigPyObject_clientdata = {
    0, 0, 0, 0, 0, 0, 0
  };
  static PyGetSetDef this_getset_def = {
    (char *)"this", &SwigPyBuiltin_ThisClosure, NULL, NULL, NULL
  };
  static SwigPyGetSet thisown_getset_closure = {
    SwigPyObject_own,
    SwigPyObject_own
  };
  static PyGetSetDef thisown_getset_def = {
    (char *)"thisown", SwigPyBuiltin_GetterClosure, SwigPyBuiltin_SetterClosure, NULL, &thisown_getset_closure
  };
  PyTypeObject *builtin_pytype;
  int builtin_base_count;
  swig_type_info *builtin_basetype;
  PyObject *tuple;
  PyGetSetDescrObject *static_getset;
  PyTypeObject *metatype;
  PyTypeObject *swigpyobject;
  SwigPyClientData *cd;
  PyObject *public_interface, *public_symbol;
  PyObject *this_descr;
  PyObject *thisown_descr;
  PyObject *self = 0;
  int i;
  
  (void)builtin_pytype;
  (void)builtin_base_count;
  (void)builtin_basetype;
  (void)tuple;
  (void)static_getset;
  (void)self;
  
  /* Metaclass is used to implement static member variables */
  metatype = SwigPyObjectType();
  assert(metatype);
#endif
  
  (void)globals;
  
  /* Create singletons now to avoid potential deadlocks with multi-threaded usage after module initialization */
  SWIG_This();
  SWIG_Python_TypeCache();
  SwigPyPacked_type();
#ifndef SWIGPYTHON_BUILTIN
  SwigPyObject_type();
#endif
  
  /* Fix SwigMethods to carry the callback ptrs when needed */
  SWIG_Python_FixMethods(SwigMethods, swig_const_table, swig_types, swig_type_initial);
  
#if PY_VERSION_HEX >= 0x03000000
  m = PyModule_Create(&SWIG_module);
#else
  m = Py_InitModule(SWIG_name, SwigMethods);
#endif
  
  md = d = PyModule_GetDict(m);
  (void)md;
  
  SWIG_InitializeModule(0);
  
#ifdef SWIGPYTHON_BUILTIN
  swigpyobject = SwigPyObject_TypeOnce();
  
  SwigPyObject_stype = SWIG_MangledTypeQuery("_p_SwigPyObject");
  assert(SwigPyObject_stype);
  cd = (SwigPyClientData*) SwigPyObject_stype->clientdata;
  if (!cd) {
    SwigPyObject_stype->clientdata = &SwigPyObject_clientdata;
    SwigPyObject_clientdata.pytype = swigpyobject;
  } else if (swigpyobject->tp_basicsize != cd->pytype->tp_basicsize) {
    PyErr_SetString(PyExc_RuntimeError, "Import error: attempted to load two incompatible swig-generated modules.");
# if PY_VERSION_HEX >= 0x03000000
    return NULL;
# else
    return;
# endif
  }
  
  /* All objects have a 'this' attribute */
  this_descr = PyDescr_NewGetSet(SwigPyObject_type(), &this_getset_def);
  (void)this_descr;
  
  /* All objects have a 'thisown' attribute */
  thisown_descr = PyDescr_NewGetSet(SwigPyObject_type(), &thisown_getset_def);
  (void)thisown_descr;
  
  public_interface = PyList_New(0);
  public_symbol = 0;
  (void)public_symbol;
  
  PyDict_SetItemString(md, "__all__", public_interface);
  SWIG_Py_DECREF(public_interface);
  for (i = 0; SwigMethods[i].ml_name != NULL; ++i)
  SwigPyBuiltin_AddPublicSymbol(public_interface, SwigMethods[i].ml_name);
  for (i = 0; swig_const_table[i].name != 0; ++i)
  SwigPyBuiltin_AddPublicSymbol(public_interface, swig_const_table[i].name);
#endif
  
  SWIG_InstallConstants(d,swig_const_table);
  
  
  import_array();
  
  SWIG_Python_SetConstant(d, "SQRT2",SWIG_From_double((double)(1.4142135623730950488016887242096980785696718753769)));
  SWIG_Python_SetConstant(d, "PI",SWIG_From_double((double)(3.1415926535897932384626433832795028841971693993751)));
  SWIG_Python_SetConstant(d, "TWOPI",SWIG_From_double((double)(6.2831853071795864769252867665590057683943387987502)));
  SWIG_Python_SetConstant(d, "DEGTORAD",SWIG_From_double((double)(0.017453292519943295769236907684886127134428718885417)));
  SWIG_Python_SetConstant(d, "RADTODEG",SWIG_From_double((double)(57.29577951308232087679815481410517033240547246656)));
  SWIG_Python_SetConstant(d, "PIBYTWO",SWIG_From_double((double)(1.5707963267948966192313216916397514420985846996876)));
  SWIG_Python_SetConstant(d, "SOL",SWIG_From_double((double)(299792458.0)));
  SWIG_Python_SetConstant(d, "SECPERJULYR",SWIG_From_double((double)(31557600.0)));
  SWIG_Python_SetConstant(d, "SECPERDAY",SWIG_From_double((double)(86400.0)));
  SWIG_Python_SetConstant(d, "ARCSEC2RAD",SWIG_From_double((double)(4.8481368110953599358991410235794797595635330237270e-6)));
  SWIG_Python_SetConstant(d, "SEC2RAD",SWIG_From_double((double)(7.2722052166430399038487115353692196393452995355905e-5)));
  SWIG_Python_SetConstant(d, "LOWACC",SWIG_From_int((int)(LOWACC)));
  SWIG_Python_SetConstant(d, "HIGHACC",SWIG_From_int((int)(HIGHACC)));
  SWIG_Python_SetConstant(d, "INTERBIN",SWIG_From_int((int)(INTERBIN)));
  SWIG_Python_SetConstant(d, "INTERPOLATE",SWIG_From_int((int)(INTERPOLATE)));
  SWIG_Python_SetConstant(d, "NO_CHECK_ALIASED",SWIG_From_int((int)(NO_CHECK_ALIASED)));
  SWIG_Python_SetConstant(d, "CHECK_ALIASED",SWIG_From_int((int)(CHECK_ALIASED)));
  SWIG_Python_SetConstant(d, "CONV",SWIG_From_int((int)(CONV)));
  SWIG_Python_SetConstant(d, "CORR",SWIG_From_int((int)(CORR)));
  SWIG_Python_SetConstant(d, "INPLACE_CONV",SWIG_From_int((int)(INPLACE_CONV)));
  SWIG_Python_SetConstant(d, "INPLACE_CORR",SWIG_From_int((int)(INPLACE_CORR)));
  SWIG_Python_SetConstant(d, "FFTDK",SWIG_From_int((int)(FFTDK)));
  SWIG_Python_SetConstant(d, "FFTD",SWIG_From_int((int)(FFTD)));
  SWIG_Python_SetConstant(d, "FFTK",SWIG_From_int((int)(FFTK)));
  SWIG_Python_SetConstant(d, "NOFFTS",SWIG_From_int((int)(NOFFTS)));
  SWIG_Python_SetConstant(d, "RAW",SWIG_From_int((int)(RAW)));
  SWIG_Python_SetConstant(d, "PREPPED",SWIG_From_int((int)(PREPPED)));
  SWIG_Python_SetConstant(d, "FFT",SWIG_From_int((int)(FFT)));
  SWIG_Python_SetConstant(d, "SAME",SWIG_From_int((int)(SAME)));
#if PY_VERSION_HEX >= 0x03000000
  return m;
#else
  return;
#endif
}

