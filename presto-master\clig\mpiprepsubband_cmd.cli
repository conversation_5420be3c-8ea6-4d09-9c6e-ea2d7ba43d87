# Admin data

Name mpiprepsubband

Usage "Converts a raw radio data file into many de-dispersed time-series (including barycentering)."

Version [exec date +%d%b%y]

Commandline full_cmd_line

# Options (in order you want them to appear)

String -o       outfile {Root of the output file names} \
	-m
Flag   -filterbank  filterbank  {Raw data in SIGPROC filterbank format}
Flag   -psrfits     psrfits     {Raw data in PSRFITS format}
Flag   -noweights  noweights  {Do not apply PSRFITS weights}
Flag   -noscales   noscales   {Do not apply PSRFITS scales}
Flag   -nooffsets  nooffsets  {Do not apply PSRFITS offsets}
Flag   -wapp    wapp    {Raw data in Wideband Arecibo Pulsar Processor (WAPP) format}
Flag   -window  window  {Window correlator lags with a Hamming window before FFTing}
Int    -numwapps numwapps  {Number of WAPPs used with contiguous frequencies} \
	-r 1 8    -d 1
Int    -if      ifs     {A specific IF to use if available (summed IFs is the default)} \
        -r 0 1
Float  -clip    clip    {Time-domain sigma to use for clipping (0.0 = no clipping, 6.0 = default} \
	-r 0 1000.0  -d 6.0
Flag   -noclip  noclip  {Do not clip the data.  (The default is to _always_ clip!)}
Flag   -invert  invert  {For rawdata, flip (or invert) the band}
Flag   -zerodm  zerodm  {Subtract the mean of all channels from each sample (i.e. remove zero DM)}
Flag   -runavg  runavg  {Running mean subtraction from the input data}
Long    -numout  numout  {Output this many values.  If there are not enough values in the original data file, will pad the output file with the average value} \
	-r 1 oo
Flag   -nobary  nobary  {Do not barycenter the data}
Long   -offset  offset {Number of spectra to offset into as starting data point} \
    -r 0 oo -d 0
Double -start  start  {Starting point of the processing as a fraction of the full obs} \
    -r 0.0 1.0  -d 0.0
Double  -lodm   lodm    {The lowest dispersion measure to de-disperse (cm^-3 pc)} \
	-r 0 oo  -d 0
Double  -dmstep dmstep  {The stepsize in dispersion measure to use(cm^-3 pc)} \
	-r 0 oo  -d 1.0
Int     -numdms numdms  {The number of DMs to de-disperse} \
	-r 1 128000 -d 10
Int     -nsub   nsub    {The number of sub-bands to use} \
	-r 1 4096 -d 32
Int     -downsamp downsamp {The number of neighboring bins to co-add} \
	-r 1 128 -d 1
String -mask    maskfile {File containing masking information to use}
String -ignorechan ignorechanstr {Comma separated string (no spaces!) of channels to ignore (or file containing such string).  Ranges are specified by min:max[:step]}

# Rest of command line:

Rest infile {Input data file name.  If the data is not in a known raw format, it should be a single channel of single-precision floating point data.  In this case a '.inf' file with the same root filename must also exist (Note that this means that the input data file must have a suffix that starts with a period)} \
        -c 1 16384
