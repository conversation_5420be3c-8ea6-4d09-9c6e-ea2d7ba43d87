.\" clig manual page template
.\" (C) 1995 <PERSON> (<EMAIL>)
.\"
.\" This file was generated by
.\" clig -- command line interface generator
.\"
.\"
.\" Clig will always edit the lines between pairs of `cligPart ...',
.\" but will not complain, if a pair is missing. So, if you want to
.\" make up a certain part of the manual page by hand rather than have
.\" it edited by clig, remove the respective pair of cligPart-lines.
.\"
.\" cligPart TITLE
.TH "prepfold" 1 "04Feb23" "Clig-manuals" "Programmer's Manual"
.\" cligPart TITLE end

.\" cligPart NAME
.SH NAME
prepfold \- Prepares (if required) and folds raw radio data, standard time series, or events.
.\" cligPart NAME end

.\" cligPart SYNOPSIS
.SH SYNOPSIS
.B prepfold
[-ncpus ncpus]
[-o outfile]
[-filterbank]
[-psrfits]
[-noweights]
[-noscales]
[-nooffsets]
[-wapp]
[-window]
[-topo]
[-invert]
[-zerodm]
[-absphase]
[-barypolycos]
[-debug]
[-samples]
[-normalize]
[-numwapps numwapps]
[-if ifs]
[-clip clip]
[-noclip]
[-noxwin]
[-runavg]
[-fine]
[-coarse]
[-slow]
[-searchpdd]
[-searchfdd]
[-nosearch]
[-nopsearch]
[-nopdsearch]
[-nodmsearch]
[-scaleparts]
[-allgrey]
[-fixchi]
[-justprofs]
[-dm dm]
[-n proflen]
[-nsub nsub]
[-npart npart]
[-pstep pstep]
[-pdstep pdstep]
[-dmstep dmstep]
[-npfact npfact]
[-ndmfact ndmfact]
[-p p]
[-pd pd]
[-pdd pdd]
[-f f]
[-fd fd]
[-fdd fdd]
[-pfact pfact]
[-ffact ffact]
[-phs phs]
[-start startT]
[-end endT]
[-psr psrname]
[-par parname]
[-polycos polycofile]
[-timing timing]
[-rzwcand rzwcand]
[-rzwfile rzwfile]
[-accelcand accelcand]
[-accelfile accelfile]
[-bin]
[-pb pb]
[-x asinic]
[-e e]
[-To To]
[-w w]
[-wdot wdot]
[-mask maskfile]
[-ignorechan ignorechanstr]
[-events]
[-days]
[-mjds]
[-double]
[-offset offset]
infile ...
.\" cligPart SYNOPSIS end

.\" cligPart OPTIONS
.SH OPTIONS
.IP -ncpus
Number of processors to use with OpenMP,
.br
1 Int value between 1 and oo.
.br
Default: `1'
.IP -o
Root of the output file names,
.br
1 String value
.IP -filterbank
Raw data in SIGPROC filterbank format.
.IP -psrfits
Raw data in PSRFITS format.
.IP -noweights
Do not apply PSRFITS weights.
.IP -noscales
Do not apply PSRFITS scales.
.IP -nooffsets
Do not apply PSRFITS offsets.
.IP -wapp
Raw data in Wideband Arecibo Pulsar Processor (WAPP) format.
.IP -window
Window correlator lags with a Hamming window before FFTing.
.IP -topo
Fold the data topocentrically (i.e. don't barycenter).
.IP -invert
For rawdata, flip (or invert) the band.
.IP -zerodm
Subtract the mean of all channels from each sample (i.e. remove zero DM).
.IP -absphase
Use the absolute phase associated with polycos.
.IP -barypolycos
Force the use of polycos for barycentered events.
.IP -debug
Show debugging output when calling TEMPO for polycos.
.IP -samples
Treat the data as samples and not as finite-duration integrated data.
.IP -normalize
Bandpass flatten the data by normalizing the subbands.
.IP -numwapps
Number of WAPPs used with contiguous frequencies,
.br
1 Int value between 1 and 8.
.br
Default: `1'
.IP -if
A specific IF to use if available (summed IFs is the default),
.br
1 Int value between 0 and 1.
.IP -clip
Time-domain sigma to use for clipping (0.0 = no clipping, 6.0 = default,
.br
1 Float value between 0 and 1000.0.
.br
Default: `6.0'
.IP -noclip
Do not clip the data.  (The default is to _always_ clip!).
.IP -noxwin
Do not show the result plots on-screen, only make postscript files.
.IP -runavg
Subtract each blocks average as it is read (single channel data only).
.IP -fine
A finer gridding in the p/pdot plane (for well known p and pdot).
.IP -coarse
A coarser gridding in the p/pdot plane (for uknown p and pdot).
.IP -slow
Sets useful flags for slow pulsars.
.IP -searchpdd
Search p-dotdots as well as p and p-dots.
.IP -searchfdd
Search f-dotdots as well as f and f-dots.
.IP -nosearch
Show but do not search the p/pdot and/or DM phase spaces.
.IP -nopsearch
Show but do not search over period.
.IP -nopdsearch
Show but do not search over p-dot.
.IP -nodmsearch
Show but do not search over DM.
.IP -scaleparts
Scale the part profiles independently.
.IP -allgrey
Make all the images greyscale instead of color.
.IP -fixchi
Adjust the reduced chi^2 values so that off-pulse reduced chi^2 = 1.
.IP -justprofs
Only output the profile portions of the plot.
.IP -dm
The central DM of the search (cm^-3 pc),
.br
1 Double value between 0 and oo.
.br
Default: `0'
.IP -n
The number of bins in the profile.  Defaults to the number of sampling bins which correspond to one folded period,
.br
1 Int value.
.IP -nsub
The number of sub-bands to use for the DM search.  If unspecified, will use something reasonable.,
.br
1 Int value between 1 and 4096.
.IP -npart
The number of sub-integrations to use for the period search,
.br
1 Int value between 1 and 4096.
.br
Default: `64'
.IP -pstep
The minimum period stepsize over the observation in profile bins,
.br
1 Int value between 1 and 10.
.br
Default: `2'
.IP -pdstep
The minimum P-dot stepsize over the observation in profile bins,
.br
1 Int value between 1 and 20.
.br
Default: `4'
.IP -dmstep
The minimum DM stepsize over the observation in profile bins,
.br
1 Int value between 1 and 10.
.br
Default: `2'
.IP -npfact
2 * npfact * proflen + 1 periods and p-dots will be searched,
.br
1 Int value between 1 and 10.
.br
Default: `2'
.IP -ndmfact
2 * ndmfact * proflen + 1 DMs will be searched,
.br
1 Int value between 1 and 1000.
.br
Default: `3'
.IP -p
The nominative folding period (s),
.br
1 Double value between 0 and oo.
.IP -pd
The nominative period derivative (s/s),
.br
1 Double value.
.br
Default: `0.0'
.IP -pdd
The nominative period 2nd derivative (s/s^2),
.br
1 Double value.
.br
Default: `0.0'
.IP -f
The nominative folding frequency (hz),
.br
1 Double value between 0 and oo.
.IP -fd
The nominative frequency derivative (hz/s),
.br
1 Double value.
.br
Default: `0'
.IP -fdd
The nominative frequency 2nd derivative (hz/s^2),
.br
1 Double value.
.br
Default: `0'
.IP -pfact
A factor to multiple the candidate p and p-dot by,
.br
1 Double value between 0.0 and 100.0.
.br
Default: `1.0'
.IP -ffact
A factor to multiple the candidate f and f-dot by,
.br
1 Double value between 0.0 and 100.0.
.br
Default: `1.0'
.IP -phs
Offset phase for the profil,
.br
1 Double value between 0.0 and 1.0.
.br
Default: `0.0'
.IP -start
The folding start time as a fraction of the full obs,
.br
1 Double value between 0.0 and 1.0.
.br
Default: `0.0'
.IP -end
The folding end time as a fraction of the full obs,
.br
1 Double value between 0.0 and 1.0.
.br
Default: `1.0'
.IP -psr
Name of pulsar to fold (do not include J or B),
.br
1 String value
.IP -par
Name of a TEMPO par file from which to get PSR params,
.br
1 String value
.IP -polycos
File containing TEMPO polycos for psrname (not required),
.br
1 String value
.IP -timing
Sets useful flags for TOA generation. Generates polycos (if required) based on the par file specified as the argument. (This means you don't need the -par or -psr commands!),
.br
1 String value
.IP -rzwcand
The candidate number to fold from 'infile'_rzw.cand,
.br
1 Int value between 1 and oo.
.IP -rzwfile
Name of the rzw search '.cand' file to use (with suffix),
.br
1 String value
.IP -accelcand
The candidate number to fold from 'infile'_rzw.cand,
.br
1 Int value between 1 and oo.
.IP -accelfile
Name of the accel search '.cand' file to use (with suffix),
.br
1 String value
.IP -bin
Fold a binary pulsar.  Must include all of the following parameters.
.IP -pb
The orbital period (s),
.br
1 Double value between 0 and oo.
.IP -x
The projected orbital semi-major axis (lt-sec),
.br
1 Double value between 0 and oo.
.IP -e
The orbital eccentricity,
.br
1 Double value between 0 and 0.9999999.
.br
Default: `0'
.IP -To
The time of periastron passage (MJD),
.br
1 Double value between 0 and oo.
.IP -w
Longitude of periastron (deg),
.br
1 Double value between 0 and 360.
.IP -wdot
Rate of advance of periastron (deg/yr),
.br
1 Double value.
.br
Default: `0'
.IP -mask
File containing masking information to use,
.br
1 String value
.IP -ignorechan
Comma separated string (no spaces!) of channels to ignore (or file containing such string).  Ranges are specified by min:max[:step],
.br
1 String value
.IP -events
Use a event file instead of a time series (.dat) file.
.IP -days
Events are in days since the EPOCH in the '.inf' file (default is seconds).
.IP -mjds
Events are in MJDs.
.IP -double
Events are in binary double precision (default is ASCII).
.IP -offset
A time offset to add to the 1st event in the same units as the events,
.br
1 Double value.
.br
Default: `0'
.IP infile
Input data file name.  If the data is not in a regognized raw data format, it should be a file containing a time series of single-precision floats or short ints.  In this case a '.inf' file with the same root filename must also exist (Note that this means that the input data file must have a suffix that starts with a period).
.\" cligPart OPTIONS end

.\" cligPart DESCRIPTION
.SH DESCRIPTION
This manual page was generated automagically by clig, the
Command Line Interface Generator. Actually the programmer
using clig was supposed to edit this part of the manual
page after
generating it with clig, but obviously (s)he didn't.

Sadly enough clig does not yet have the power to pick a good
program description out of blue air ;-(
.\" cligPart DESCRIPTION end
