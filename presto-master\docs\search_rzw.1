.\" clig manual page template
.\" (C) 1995 <PERSON> (<EMAIL>)
.\"
.\" This file was generated by
.\" clig -- command line interface generator
.\"
.\"
.\" Clig will always edit the lines between pairs of `cligPart ...',
.\" but will not complain, if a pair is missing. So, if you want to
.\" make up a certain part of the manual page by hand rather than have
.\" it edited by clig, remove the respective pair of cligPart-lines.
.\"
.\" cligPart TITLE
.TH "search_rzw" 1 "12Mar10" "Clig-manuals" "Programmer's Manual"
.\" cligPart TITLE end

.\" cligPart NAME
.SH NAME
search_rzw \- Searches an FFT for pulsar candidates using a Fourier domain acceleration search.
.\" cligPart NAME end

.\" cligPart SYNOPSIS
.SH SYNOPSIS
.B search_rzw
[-ncand ncand]
[-zlo zlo]
[-zhi zhi]
[-rlo rlo]
[-rhi rhi]
[-flo flo]
[-fhi fhi]
[-lobin lobin]
[-zapfile zapfile]
[-baryv baryv]
[-photon]
infile
.\" cligPart SYNOPSIS end

.\" cligPart OPTIONS
.SH OPTIONS
.IP -ncand
Number of candidates to try to return,
.br
1 Int value between 1 and 10000.
.br
Default: `100'
.IP -zlo
The low Fourier frequency derivative to search,
.br
1 Int value between -2000000 and 2000000.
.br
Default: ` -50'
.IP -zhi
The high Fourier frequency derivative to search,
.br
1 Int value between -2000000 and 2000000.
.br
Default: `50'
.IP -rlo
The lowest Fourier frequency to search,
.br
1 Int value between 0 and oo.
.br
Default: `300'
.IP -rhi
The highest Fourier frequency to search,
.br
1 Int value between 0 and oo.
.IP -flo
The lowest frequency (Hz) to search,
.br
1 Int value between 0 and oo.
.IP -fhi
The highest frequency (Hz) to search,
.br
1 Int value between 0 and oo.
.IP -lobin
The first Fourier frequency in the data file,
.br
1 Int value between 0 and oo.
.br
Default: `0'
.IP -zapfile
A file containing a list of freqs to ignore (i.e. RFI),
.br
1 String value
.IP -baryv
The earth's radial velocity component (v/c) towards the observation (used to convert topocentric birdie freqs to barycentric),
.br
1 Double value between -0.1 and 0.1.
.br
Default: `0.0'
.IP -photon
Data is poissonian so use freq 0 as power normalization.
.IP infile
Input file name (no suffix) of floating point fft data.  A '.inf' file of the same name must also exist.
.\" cligPart OPTIONS end

.SH DESCRIPTION
This program searches an FFT found in the input file for pulsars using
a Fourier domain acceleration search (i.e. Ransom and Eikenberry,
2000, in prep.).  The search uses a spacing of 0.5 frequency bins in
the fourier frequency (r) direction and 2 'bins' in the fdot (z)
direction.

The 'zapfile', if present, contains a list of center frequencies (Hz)
and widths (Hz), which the search routine should ignore (usually these
are known noise sources in the data).  Lines beginning with '#' are
ignored.  The example file would be:

# Freq(Hz) Freq-width(Hz)
.br
60.0 0.02
.br
120.0 0.01
.br

The routine outputs formatted statistics for the 'ncand' best
candidates found in the search region.  The routine currently cannot
search the 'w' dimension.  This may be fixed shortly.


Copyright 1999, Scott M. Ransom (<EMAIL>)
