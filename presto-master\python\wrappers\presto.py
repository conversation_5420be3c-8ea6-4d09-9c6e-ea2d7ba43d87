# This file was automatically generated by SWIG (https://www.swig.org).
# Version 4.3.0
#
# Do not make changes to this file unless you know what you are doing - modify
# the SWIG interface file instead.

from sys import version_info as _swig_python_version_info
import _presto

try:
    import builtins as __builtin__
except ImportError:
    import __builtin__

def _swig_repr(self):
    try:
        strthis = "proxy of " + self.this.__repr__()
    except __builtin__.Exception:
        strthis = ""
    return "<%s.%s; %s >" % (self.__class__.__module__, self.__class__.__name__, strthis,)


def _swig_setattr_nondynamic_instance_variable(set):
    def set_instance_attr(self, name, value):
        if name == "this":
            set(self, name, value)
        elif name == "thisown":
            self.this.own(value)
        elif hasattr(self, name) and isinstance(getattr(type(self), name), property):
            set(self, name, value)
        else:
            raise AttributeError("You cannot add instance attributes to %s" % self)
    return set_instance_attr


def _swig_setattr_nondynamic_class_variable(set):
    def set_class_attr(cls, name, value):
        if hasattr(cls, name) and not isinstance(getattr(cls, name), property):
            set(cls, name, value)
        else:
            raise AttributeError("You cannot add class attributes to %s" % cls)
    return set_class_attr


def _swig_add_metaclass(metaclass):
    """Class decorator for adding a metaclass to a SWIG wrapped class - a slimmed down version of six.add_metaclass"""
    def wrapper(cls):
        return metaclass(cls.__name__, cls.__bases__, cls.__dict__.copy())
    return wrapper


class _SwigNonDynamicMeta(type):
    """Meta class to enforce nondynamic attributes (no new attributes) for a class"""
    __setattr__ = _swig_setattr_nondynamic_class_variable(type.__setattr__)


class fcomplex(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    r = property(_presto.fcomplex_r_get, _presto.fcomplex_r_set)
    i = property(_presto.fcomplex_i_get, _presto.fcomplex_i_set)

    def __init__(self):
        _presto.fcomplex_swiginit(self, _presto.new_fcomplex())
    __swig_destroy__ = _presto.delete_fcomplex

# Register fcomplex in _presto:
_presto.fcomplex_swigregister(fcomplex)
SQRT2 = _presto.SQRT2
PI = _presto.PI
TWOPI = _presto.TWOPI
DEGTORAD = _presto.DEGTORAD
RADTODEG = _presto.RADTODEG
PIBYTWO = _presto.PIBYTWO
SOL = _presto.SOL
SECPERJULYR = _presto.SECPERJULYR
SECPERDAY = _presto.SECPERDAY
ARCSEC2RAD = _presto.ARCSEC2RAD
SEC2RAD = _presto.SEC2RAD
LOWACC = _presto.LOWACC
HIGHACC = _presto.HIGHACC
INTERBIN = _presto.INTERBIN
INTERPOLATE = _presto.INTERPOLATE
NO_CHECK_ALIASED = _presto.NO_CHECK_ALIASED
CHECK_ALIASED = _presto.CHECK_ALIASED
CONV = _presto.CONV
CORR = _presto.CORR
INPLACE_CONV = _presto.INPLACE_CONV
INPLACE_CORR = _presto.INPLACE_CORR
FFTDK = _presto.FFTDK
FFTD = _presto.FFTD
FFTK = _presto.FFTK
NOFFTS = _presto.NOFFTS
RAW = _presto.RAW
PREPPED = _presto.PREPPED
FFT = _presto.FFT
SAME = _presto.SAME

def read_wisdom():
    return _presto.read_wisdom()

def good_factor(nn):
    return _presto.good_factor(nn)

def fftwcall(indata, isign):
    return _presto.fftwcall(indata, isign)

def tablesixstepfft(indata, isign):
    return _presto.tablesixstepfft(indata, isign)

def realfft(data, isign):
    return _presto.realfft(data, isign)
class infodata(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    ra_s = property(_presto.infodata_ra_s_get, _presto.infodata_ra_s_set)
    dec_s = property(_presto.infodata_dec_s_get, _presto.infodata_dec_s_set)
    N = property(_presto.infodata_N_get, _presto.infodata_N_set)
    dt = property(_presto.infodata_dt_get, _presto.infodata_dt_set)
    fov = property(_presto.infodata_fov_get, _presto.infodata_fov_set)
    mjd_f = property(_presto.infodata_mjd_f_get, _presto.infodata_mjd_f_set)
    dm = property(_presto.infodata_dm_get, _presto.infodata_dm_set)
    freq = property(_presto.infodata_freq_get, _presto.infodata_freq_set)
    freqband = property(_presto.infodata_freqband_get, _presto.infodata_freqband_set)
    chan_wid = property(_presto.infodata_chan_wid_get, _presto.infodata_chan_wid_set)
    wavelen = property(_presto.infodata_wavelen_get, _presto.infodata_wavelen_set)
    waveband = property(_presto.infodata_waveband_get, _presto.infodata_waveband_set)
    energy = property(_presto.infodata_energy_get, _presto.infodata_energy_set)
    energyband = property(_presto.infodata_energyband_get, _presto.infodata_energyband_set)
    num_chan = property(_presto.infodata_num_chan_get, _presto.infodata_num_chan_set)
    mjd_i = property(_presto.infodata_mjd_i_get, _presto.infodata_mjd_i_set)
    ra_h = property(_presto.infodata_ra_h_get, _presto.infodata_ra_h_set)
    ra_m = property(_presto.infodata_ra_m_get, _presto.infodata_ra_m_set)
    dec_d = property(_presto.infodata_dec_d_get, _presto.infodata_dec_d_set)
    dec_m = property(_presto.infodata_dec_m_get, _presto.infodata_dec_m_set)
    bary = property(_presto.infodata_bary_get, _presto.infodata_bary_set)
    numonoff = property(_presto.infodata_numonoff_get, _presto.infodata_numonoff_set)
    notes = property(_presto.infodata_notes_get, _presto.infodata_notes_set)
    name = property(_presto.infodata_name_get, _presto.infodata_name_set)
    object = property(_presto.infodata_object_get, _presto.infodata_object_set)
    instrument = property(_presto.infodata_instrument_get, _presto.infodata_instrument_set)
    observer = property(_presto.infodata_observer_get, _presto.infodata_observer_set)
    analyzer = property(_presto.infodata_analyzer_get, _presto.infodata_analyzer_set)
    telescope = property(_presto.infodata_telescope_get, _presto.infodata_telescope_set)
    band = property(_presto.infodata_band_get, _presto.infodata_band_set)
    filt = property(_presto.infodata_filt_get, _presto.infodata_filt_set)

    def __init__(self):
        _presto.infodata_swiginit(self, _presto.new_infodata())
    __swig_destroy__ = _presto.delete_infodata

# Register infodata in _presto:
_presto.infodata_swigregister(infodata)

def readinf(data, filenm):
    return _presto.readinf(data, filenm)

def writeinf(data):
    return _presto.writeinf(data)
class orbitparams(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    p = property(_presto.orbitparams_p_get, _presto.orbitparams_p_set)
    e = property(_presto.orbitparams_e_get, _presto.orbitparams_e_set)
    x = property(_presto.orbitparams_x_get, _presto.orbitparams_x_set)
    w = property(_presto.orbitparams_w_get, _presto.orbitparams_w_set)
    t = property(_presto.orbitparams_t_get, _presto.orbitparams_t_set)
    pd = property(_presto.orbitparams_pd_get, _presto.orbitparams_pd_set)
    wd = property(_presto.orbitparams_wd_get, _presto.orbitparams_wd_set)

    def __init__(self):
        _presto.orbitparams_swiginit(self, _presto.new_orbitparams())
    __swig_destroy__ = _presto.delete_orbitparams

# Register orbitparams in _presto:
_presto.orbitparams_swigregister(orbitparams)
class psrparams(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    jname = property(_presto.psrparams_jname_get, _presto.psrparams_jname_set)
    bname = property(_presto.psrparams_bname_get, _presto.psrparams_bname_set)
    alias = property(_presto.psrparams_alias_get, _presto.psrparams_alias_set)
    ra2000 = property(_presto.psrparams_ra2000_get, _presto.psrparams_ra2000_set)
    dec2000 = property(_presto.psrparams_dec2000_get, _presto.psrparams_dec2000_set)
    dm = property(_presto.psrparams_dm_get, _presto.psrparams_dm_set)
    timepoch = property(_presto.psrparams_timepoch_get, _presto.psrparams_timepoch_set)
    p = property(_presto.psrparams_p_get, _presto.psrparams_p_set)
    pd = property(_presto.psrparams_pd_get, _presto.psrparams_pd_set)
    pdd = property(_presto.psrparams_pdd_get, _presto.psrparams_pdd_set)
    f = property(_presto.psrparams_f_get, _presto.psrparams_f_set)
    fd = property(_presto.psrparams_fd_get, _presto.psrparams_fd_set)
    fdd = property(_presto.psrparams_fdd_get, _presto.psrparams_fdd_set)
    orb = property(_presto.psrparams_orb_get, _presto.psrparams_orb_set)

    def __init__(self):
        _presto.psrparams_swiginit(self, _presto.new_psrparams())
    __swig_destroy__ = _presto.delete_psrparams

# Register psrparams in _presto:
_presto.psrparams_swigregister(psrparams)

def get_psr_at_epoch(psrname, epoch, psr):
    return _presto.get_psr_at_epoch(psrname, epoch, psr)

def get_psr_from_parfile(parfilenm, epoch, psr):
    return _presto.get_psr_from_parfile(parfilenm, epoch, psr)

def mjd_to_datestr(mjd, datestr):
    return _presto.mjd_to_datestr(mjd, datestr)

def fresnl(xxa):
    return _presto.fresnl(xxa)
class rderivs(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    pow = property(_presto.rderivs_pow_get, _presto.rderivs_pow_set)
    phs = property(_presto.rderivs_phs_get, _presto.rderivs_phs_set)
    dpow = property(_presto.rderivs_dpow_get, _presto.rderivs_dpow_set)
    dphs = property(_presto.rderivs_dphs_get, _presto.rderivs_dphs_set)
    d2pow = property(_presto.rderivs_d2pow_get, _presto.rderivs_d2pow_set)
    d2phs = property(_presto.rderivs_d2phs_get, _presto.rderivs_d2phs_set)
    locpow = property(_presto.rderivs_locpow_get, _presto.rderivs_locpow_set)

    def __init__(self):
        _presto.rderivs_swiginit(self, _presto.new_rderivs())
    __swig_destroy__ = _presto.delete_rderivs

# Register rderivs in _presto:
_presto.rderivs_swigregister(rderivs)
class fourierprops(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    r = property(_presto.fourierprops_r_get, _presto.fourierprops_r_set)
    rerr = property(_presto.fourierprops_rerr_get, _presto.fourierprops_rerr_set)
    z = property(_presto.fourierprops_z_get, _presto.fourierprops_z_set)
    zerr = property(_presto.fourierprops_zerr_get, _presto.fourierprops_zerr_set)
    w = property(_presto.fourierprops_w_get, _presto.fourierprops_w_set)
    werr = property(_presto.fourierprops_werr_get, _presto.fourierprops_werr_set)
    pow = property(_presto.fourierprops_pow_get, _presto.fourierprops_pow_set)
    powerr = property(_presto.fourierprops_powerr_get, _presto.fourierprops_powerr_set)
    sig = property(_presto.fourierprops_sig_get, _presto.fourierprops_sig_set)
    rawpow = property(_presto.fourierprops_rawpow_get, _presto.fourierprops_rawpow_set)
    phs = property(_presto.fourierprops_phs_get, _presto.fourierprops_phs_set)
    phserr = property(_presto.fourierprops_phserr_get, _presto.fourierprops_phserr_set)
    cen = property(_presto.fourierprops_cen_get, _presto.fourierprops_cen_set)
    cenerr = property(_presto.fourierprops_cenerr_get, _presto.fourierprops_cenerr_set)
    pur = property(_presto.fourierprops_pur_get, _presto.fourierprops_pur_set)
    purerr = property(_presto.fourierprops_purerr_get, _presto.fourierprops_purerr_set)
    locpow = property(_presto.fourierprops_locpow_get, _presto.fourierprops_locpow_set)

    def __init__(self):
        _presto.fourierprops_swiginit(self, _presto.new_fourierprops())
    __swig_destroy__ = _presto.delete_fourierprops

# Register fourierprops in _presto:
_presto.fourierprops_swigregister(fourierprops)
class foldstats(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    numdata = property(_presto.foldstats_numdata_get, _presto.foldstats_numdata_set)
    data_avg = property(_presto.foldstats_data_avg_get, _presto.foldstats_data_avg_set)
    data_var = property(_presto.foldstats_data_var_get, _presto.foldstats_data_var_set)
    numprof = property(_presto.foldstats_numprof_get, _presto.foldstats_numprof_set)
    prof_avg = property(_presto.foldstats_prof_avg_get, _presto.foldstats_prof_avg_set)
    prof_var = property(_presto.foldstats_prof_var_get, _presto.foldstats_prof_var_set)
    redchi = property(_presto.foldstats_redchi_get, _presto.foldstats_redchi_set)

    def __init__(self):
        _presto.foldstats_swiginit(self, _presto.new_foldstats())
    __swig_destroy__ = _presto.delete_foldstats

# Register foldstats in _presto:
_presto.foldstats_swigregister(foldstats)

def gen_fvect(nl):
    return _presto.gen_fvect(nl)

def gen_cvect(nl):
    return _presto.gen_cvect(nl)

def power_arr(dft):
    return _presto.power_arr(dft)

def phase_arr(dft):
    return _presto.phase_arr(dft)

def frotate(data, bins_to_left):
    return _presto.frotate(data, bins_to_left)

def drotate(data, bins_to_left):
    return _presto.drotate(data, bins_to_left)

def keplers_eqn(t, p_orb, e, Eacc):
    return _presto.keplers_eqn(t, p_orb, e, Eacc)

def E_to_phib(E, orb):
    return _presto.E_to_phib(E, orb)

def E_to_v(E, orb):
    return _presto.E_to_v(E, orb)

def E_to_p(E, p_psr, orb):
    return _presto.E_to_p(E, p_psr, orb)

def E_to_z(E, p_psr, T, orb):
    return _presto.E_to_z(E, p_psr, T, orb)

def E_to_phib_BT(E, orb):
    return _presto.E_to_phib_BT(E, orb)

def dorbint(Eo, numpts, dt, orb):
    return _presto.dorbint(Eo, numpts, dt, orb)

def binary_velocity(T, orbit):
    return _presto.binary_velocity(T, orbit)

def r_resp_halfwidth(accuracy):
    return _presto.r_resp_halfwidth(accuracy)

def z_resp_halfwidth(z, accuracy):
    return _presto.z_resp_halfwidth(z, accuracy)

def w_resp_halfwidth(z, w, accuracy):
    return _presto.w_resp_halfwidth(z, w, accuracy)

def bin_resp_halfwidth(ppsr, T, orbit):
    return _presto.bin_resp_halfwidth(ppsr, T, orbit)

def gen_r_response(roffset, numbetween, numkern):
    return _presto.gen_r_response(roffset, numbetween, numkern)

def gen_z_response(roffset, numbetween, numkern, z):
    return _presto.gen_z_response(roffset, numbetween, numkern, z)

def gen_w_response(roffset, numbetween, numkern, z, w):
    return _presto.gen_w_response(roffset, numbetween, numkern, z, w)

def gen_w_response2(roffset, numbetween, numkern, z, w):
    return _presto.gen_w_response2(roffset, numbetween, numkern, z, w)

def gen_bin_response(roffset, numbetween, numkern, ppsr, T, orbit):
    return _presto.gen_bin_response(roffset, numbetween, numkern, ppsr, T, orbit)

def get_localpower(data, r):
    return _presto.get_localpower(data, r)

def get_localpower3d(data, r, z, w):
    return _presto.get_localpower3d(data, r, z, w)

def get_derivs3d(data, r, z, w, localpower, result):
    return _presto.get_derivs3d(data, r, z, w, localpower, result)

def calc_props(data, r, z, w, result):
    return _presto.calc_props(data, r, z, w, result)

def calc_binprops(props, T, lowbin, nfftbins, result):
    return _presto.calc_binprops(props, T, lowbin, nfftbins, result)

def calc_rzwerrs(props, T, result):
    return _presto.calc_rzwerrs(props, T, result)

def extended_equiv_gaussian_sigma(logp):
    return _presto.extended_equiv_gaussian_sigma(logp)

def log_asymtotic_incomplete_gamma(a, z):
    return _presto.log_asymtotic_incomplete_gamma(a, z)

def log_asymtotic_gamma(z):
    return _presto.log_asymtotic_gamma(z)

def equivalent_gaussian_sigma(logp):
    return _presto.equivalent_gaussian_sigma(logp)

def chi2_logp(chi2, dof):
    return _presto.chi2_logp(chi2, dof)

def chi2_sigma(chi2, dof):
    return _presto.chi2_sigma(chi2, dof)

def candidate_sigma(power, numsum, numtrials):
    return _presto.candidate_sigma(power, numsum, numtrials)

def power_for_sigma(sigma, numsum, numtrials):
    return _presto.power_for_sigma(sigma, numsum, numtrials)

def switch_f_and_p(_in, ind, indd):
    return _presto.switch_f_and_p(_in, ind, indd)

def chisqr(data, avg, var):
    return _presto.chisqr(data, avg, var)

def z2n(data, var, n):
    return _presto.z2n(data, var, n)

def print_candidate(cand, dt, N, nph, numerrdigits):
    return _presto.print_candidate(cand, dt, N, nph, numerrdigits)

def print_bin_candidate(cand, numerrdigits):
    return _presto.print_bin_candidate(cand, numerrdigits)

def fopen(filename, mode):
    return _presto.fopen(filename, mode)

def fputs(arg1, arg2):
    return _presto.fputs(arg1, arg2)

def fclose(arg1):
    return _presto.fclose(arg1)

def fseek(stream, offset, whence):
    return _presto.fseek(stream, offset, whence)

def read_rzw_cand(file, cands):
    return _presto.read_rzw_cand(file, cands)

def get_rzw_cand(filenm, candnum, cand):
    return _presto.get_rzw_cand(filenm, candnum, cand)

def read_bin_cand(file, cands):
    return _presto.read_bin_cand(file, cands)

def get_bin_cand(filenm, candnum, cand):
    return _presto.get_bin_cand(filenm, candnum, cand)

def next2_to_n(x):
    return _presto.next2_to_n(x)

def is_power_of_10(n):
    return _presto.is_power_of_10(n)

def choose_good_N(orig_N):
    return _presto.choose_good_N(orig_N)

def dms2rad(deg, min, sec):
    return _presto.dms2rad(deg, min, sec)

def hms2rad(hour, min, sec):
    return _presto.hms2rad(hour, min, sec)

def hours2hms(hours):
    return _presto.hours2hms(hours)

def deg2dms(degrees):
    return _presto.deg2dms(degrees)

def sphere_ang_diff(ra1, dec1, ra2, dec2):
    return _presto.sphere_ang_diff(ra1, dec1, ra2, dec2)

def rz_interp(data, r, z, kern_half_width):
    return _presto.rz_interp(data, r, z, kern_half_width)

def corr_rz_plane(data, numbetween, startbin, zlo, zhi, numz, fftlen, accuracy):
    return _presto.corr_rz_plane(data, numbetween, startbin, zlo, zhi, numz, fftlen, accuracy)

def corr_rzw_vol(data, numbetween, startbin, zlo, zhi, numz, wlo, whi, numw, fftlen, accuracy):
    return _presto.corr_rzw_vol(data, numbetween, startbin, zlo, zhi, numz, wlo, whi, numw, fftlen, accuracy)

def max_r_arr(data, rin, derivs):
    return _presto.max_r_arr(data, rin, derivs)

def max_rz_arr(data, rin, zin, derivs):
    return _presto.max_rz_arr(data, rin, zin, derivs)

def max_rz_arr_harmonics(data, rin, zin, derivdata):
    return _presto.max_rz_arr_harmonics(data, rin, zin, derivdata)

def max_rzw_arr_harmonics(data, rin, zin, win, derivdata):
    return _presto.max_rzw_arr_harmonics(data, rin, zin, win, derivdata)

def max_rzw_arr(data, rin, zin, win, derivs):
    return _presto.max_rzw_arr(data, rin, zin, win, derivs)

def barycenter(topotimes, barytimes, voverc, ra, dec, obs, ephem):
    return _presto.barycenter(topotimes, barytimes, voverc, ra, dec, obs, ephem)

def DOF_corr(dt_per_bin):
    return _presto.DOF_corr(dt_per_bin)

def simplefold(data, dt, tlo, prof, startphs, f0, fdot, fdotdot, standard):
    return _presto.simplefold(data, dt, tlo, prof, startphs, f0, fdot, fdotdot, standard)

def nice_output_1(output, val, err, len):
    return _presto.nice_output_1(output, val, err, len)

def nice_output_2(output, val, err, len):
    return _presto.nice_output_2(output, val, err, len)

