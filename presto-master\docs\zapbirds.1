.\" clig manual page template
.\" (C) 1995 <PERSON> (<EMAIL>)
.\"
.\" This file was generated by
.\" clig -- command line interface generator
.\"
.\"
.\" Clig will always edit the lines between pairs of `cligPart ...',
.\" but will not complain, if a pair is missing. So, if you want to
.\" make up a certain part of the manual page by hand rather than have
.\" it edited by clig, remove the respective pair of cligPart-lines.
.\"
.\" cligPart TITLE
.TH "zapbirds" 1 "09Jul20" "Clig-manuals" "Programmer's Manual"
.\" cligPart TITLE end

.\" cligPart NAME
.SH NAME
zapbirds \- Allows you to interactively or automatically zap interference from an FFT.
.\" cligPart NAME end

.\" cligPart SYNOPSIS
.SH SYNOPSIS
.B zapbirds
[-zap]
[-zapfile zapfile]
[-in inzapfile]
[-out outzapfile]
[-baryv baryv]
infile ...
.\" cligPart SYNOPSIS end

.\" cligPart OPTIONS
.SH OPTIONS
.IP -zap
Zap the birds in the FFT from 'zapfile' (write to the FFT file).
.IP -zapfile
A file of freqs and widths to zap from the FFT (when using '-zap'),
.br
1 String value
.IP -in
A file containing a list of freqs (Hz) and the # of harmonics to zap,
.br
1 String value
.IP -out
A file to write that will contain the freqs and widths (Hz) zapped,
.br
1 String value
.IP -baryv
The radial velocity component (v/c) towards the target during the obs,
.br
1 Double value between -0.1 and 0.1.
.br
Default: `0.0'
.IP infile
Input file name (no suffix) of floating point fft data.  A '.inf' file of the same name must also exist.
.\" cligPart OPTIONS end

.\" cligPart DESCRIPTION
.SH DESCRIPTION
This manual page was generated automagically by clig, the
Command Line Interface Generator. Actually the programmer
using clig was supposed to edit this part of the manual
page after
generating it with clig, but obviously (s)he didn't.

Sadly enough clig does not yet have the power to pick a good
program description out of blue air ;-(
.\" cligPart DESCRIPTION end
