# Input file       =  2013-04-02-16:32:16_downsampled.fil
# Candidate        =  1838.89ms_Cand
# Telescope        =  Parkes
# Epoch_topo       =  56384.689074074071
# Epoch_bary (MJD) =  56384.690941195819
# T_sample         =  0.001024
# Data Folded      =  4147200
# Data Avg         =  1475.50168891538 
# Data StdDev      =  14.8144784271016 
# Profile Bins     =  64
# Profile Avg      =  95575455.2976855 
# Profile StdDev   =  3771.15053599633 
# Reduced chi-sqr  =  31.667
# Prob(Noise)      <  0   (~41.4 sigma)
# Best DM          =  599.840
# P_topo (ms)      =  1839.33497838014  +/- 0.00509
# P'_topo (s/s)    =  -3.51371536908081e-07 +/- 9.28e-09
# P''_topo (s/s^2) =  0                 +/- 1.42e-11
# P_bary (ms)      =  1839.51647686768  +/- 0.00509
# P'_bary (s/s)    =  -3.51495072784787e-07 +/- 9.28e-09
# P''_bary (s/s^2) =  1.27311154901642e-13 +/- 1.42e-11
# P_orb (s)        =  16462.9597611786 
# asin(i)/c (s)    =  3.2135705947876  
# eccentricity     =  0                
# w (deg)          =  0                
# T_peri           =  56384.606229984915
######################################################
   0  9.556498e+07
   1  9.556995e+07
   2  9.556468e+07
   3  9.556548e+07
   4  9.556554e+07
   5  9.55705e+07
   6  9.555838e+07
   7  9.556434e+07
   8  9.556757e+07
   9  9.556013e+07
  10  9.555633e+07
  11  9.557028e+07
  12  9.557018e+07
  13  9.556997e+07
  14  9.557077e+07
  15  9.55777e+07
  16  9.55763e+07
  17  9.557775e+07
  18  9.559145e+07
  19  9.563806e+07
  20  9.56499e+07
  21  9.56044e+07
  22  9.558319e+07
  23  9.557616e+07
  24  9.55752e+07
  25  9.557189e+07
  26  9.556445e+07
  27  9.556787e+07
  28  9.557351e+07
  29  9.556816e+07
  30  9.556534e+07
  31  9.556182e+07
  32  9.556077e+07
  33  9.556372e+07
  34  9.557054e+07
  35  9.556415e+07
  36  9.556488e+07
  37  9.5571e+07
  38  9.5567e+07
  39  9.556826e+07
  40  9.556676e+07
  41  9.55685e+07
  42  9.556223e+07
  43  9.556588e+07
  44  9.556669e+07
  45  9.556734e+07
  46  9.556904e+07
  47  9.557739e+07
  48  9.557614e+07
  49  9.557288e+07
  50  9.557869e+07
  51  9.564778e+07
  52  9.564777e+07
  53  9.561862e+07
  54  9.558561e+07
  55  9.557368e+07
  56  9.557126e+07
  57  9.55685e+07
  58  9.557263e+07
  59  9.556242e+07
  60  9.556402e+07
  61  9.556899e+07
  62  9.556326e+07
  63  9.55705e+07
