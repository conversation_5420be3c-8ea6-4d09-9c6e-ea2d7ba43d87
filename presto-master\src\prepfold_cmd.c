/*****
  command line parser -- generated by clig
  (http://wsd.iitb.fhg.de/~kir/clighome/)

  The command line parser `clig':
  (C) 1995-2004 <PERSON> (<EMAIL>)
*****/

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <ctype.h>
#include <errno.h>
#include <limits.h>
#include <float.h>
#include <math.h>

#include "prepfold_cmd.h"

char *Program;

/*@-null*/

static Cmdline cmd = {
  /***** -ncpus: Number of processors to use with OpenMP */
  /* ncpusP = */ 1,
  /* ncpus = */ 1,
  /* ncpusC = */ 1,
  /***** -o: Root of the output file names */
  /* outfileP = */ 0,
  /* outfile = */ (char*)0,
  /* outfileC = */ 0,
  /***** -filterbank: Raw data in SIGPROC filterbank format */
  /* filterbankP = */ 0,
  /***** -psrfits: Raw data in PSRFITS format */
  /* psrfitsP = */ 0,
  /***** -noweights: Do not apply PSRFITS weights */
  /* noweightsP = */ 0,
  /***** -noscales: Do not apply PSRFITS scales */
  /* noscalesP = */ 0,
  /***** -nooffsets: Do not apply PSRFITS offsets */
  /* nooffsetsP = */ 0,
  /***** -wapp: Raw data in Wideband Arecibo Pulsar Processor (WAPP) format */
  /* wappP = */ 0,
  /***** -window: Window correlator lags with a Hamming window before FFTing */
  /* windowP = */ 0,
  /***** -topo: Fold the data topocentrically (i.e. don't barycenter) */
  /* topoP = */ 0,
  /***** -invert: For rawdata, flip (or invert) the band */
  /* invertP = */ 0,
  /***** -zerodm: Subtract the mean of all channels from each sample (i.e. remove zero DM) */
  /* zerodmP = */ 0,
  /***** -absphase: Use the absolute phase associated with polycos */
  /* absphaseP = */ 0,
  /***** -barypolycos: Force the use of polycos for barycentered events */
  /* barypolycosP = */ 0,
  /***** -debug: Show debugging output when calling TEMPO for polycos */
  /* debugP = */ 0,
  /***** -samples: Treat the data as samples and not as finite-duration integrated data */
  /* samplesP = */ 0,
  /***** -normalize: Bandpass flatten the data by normalizing the subbands */
  /* normalizeP = */ 0,
  /***** -numwapps: Number of WAPPs used with contiguous frequencies */
  /* numwappsP = */ 1,
  /* numwapps = */ 1,
  /* numwappsC = */ 1,
  /***** -if: A specific IF to use if available (summed IFs is the default) */
  /* ifsP = */ 0,
  /* ifs = */ (int)0,
  /* ifsC = */ 0,
  /***** -clip: Time-domain sigma to use for clipping (0.0 = no clipping, 6.0 = default */
  /* clipP = */ 1,
  /* clip = */ 6.0,
  /* clipC = */ 1,
  /***** -noclip: Do not clip the data.  (The default is to _always_ clip!) */
  /* noclipP = */ 0,
  /***** -noxwin: Do not show the result plots on-screen, only make postscript files */
  /* noxwinP = */ 0,
  /***** -runavg: Subtract each blocks average as it is read (single channel data only) */
  /* runavgP = */ 0,
  /***** -fine: A finer gridding in the p/pdot plane (for well known p and pdot) */
  /* fineP = */ 0,
  /***** -coarse: A coarser gridding in the p/pdot plane (for uknown p and pdot) */
  /* coarseP = */ 0,
  /***** -slow: Sets useful flags for slow pulsars */
  /* slowP = */ 0,
  /***** -searchpdd: Search p-dotdots as well as p and p-dots */
  /* searchpddP = */ 0,
  /***** -searchfdd: Search f-dotdots as well as f and f-dots */
  /* searchfddP = */ 0,
  /***** -nosearch: Show but do not search the p/pdot and/or DM phase spaces */
  /* nosearchP = */ 0,
  /***** -nopsearch: Show but do not search over period */
  /* nopsearchP = */ 0,
  /***** -nopdsearch: Show but do not search over p-dot */
  /* nopdsearchP = */ 0,
  /***** -nodmsearch: Show but do not search over DM */
  /* nodmsearchP = */ 0,
  /***** -scaleparts: Scale the part profiles independently */
  /* scalepartsP = */ 0,
  /***** -allgrey: Make all the images greyscale instead of color */
  /* allgreyP = */ 0,
  /***** -fixchi: Adjust the reduced chi^2 values so that off-pulse reduced chi^2 = 1 */
  /* fixchiP = */ 0,
  /***** -justprofs: Only output the profile portions of the plot */
  /* justprofsP = */ 0,
  /***** -dm: The central DM of the search (cm^-3 pc) */
  /* dmP = */ 1,
  /* dm = */ 0,
  /* dmC = */ 1,
  /***** -n: The number of bins in the profile.  Defaults to the number of sampling bins which correspond to one folded period */
  /* proflenP = */ 0,
  /* proflen = */ (int)0,
  /* proflenC = */ 0,
  /***** -nsub: The number of sub-bands to use for the DM search.  If unspecified, will use something reasonable. */
  /* nsubP = */ 0,
  /* nsub = */ (int)0,
  /* nsubC = */ 0,
  /***** -npart: The number of sub-integrations to use for the period search */
  /* npartP = */ 1,
  /* npart = */ 64,
  /* npartC = */ 1,
  /***** -pstep: The minimum period stepsize over the observation in profile bins */
  /* pstepP = */ 1,
  /* pstep = */ 2,
  /* pstepC = */ 1,
  /***** -pdstep: The minimum P-dot stepsize over the observation in profile bins */
  /* pdstepP = */ 1,
  /* pdstep = */ 4,
  /* pdstepC = */ 1,
  /***** -dmstep: The minimum DM stepsize over the observation in profile bins */
  /* dmstepP = */ 1,
  /* dmstep = */ 2,
  /* dmstepC = */ 1,
  /***** -npfact: 2 * npfact * proflen + 1 periods and p-dots will be searched */
  /* npfactP = */ 1,
  /* npfact = */ 2,
  /* npfactC = */ 1,
  /***** -ndmfact: 2 * ndmfact * proflen + 1 DMs will be searched */
  /* ndmfactP = */ 1,
  /* ndmfact = */ 3,
  /* ndmfactC = */ 1,
  /***** -p: The nominative folding period (s) */
  /* pP = */ 0,
  /* p = */ (double)0,
  /* pC = */ 0,
  /***** -pd: The nominative period derivative (s/s) */
  /* pdP = */ 1,
  /* pd = */ 0.0,
  /* pdC = */ 1,
  /***** -pdd: The nominative period 2nd derivative (s/s^2) */
  /* pddP = */ 1,
  /* pdd = */ 0.0,
  /* pddC = */ 1,
  /***** -f: The nominative folding frequency (hz) */
  /* fP = */ 0,
  /* f = */ (double)0,
  /* fC = */ 0,
  /***** -fd: The nominative frequency derivative (hz/s) */
  /* fdP = */ 1,
  /* fd = */ 0,
  /* fdC = */ 1,
  /***** -fdd: The nominative frequency 2nd derivative (hz/s^2) */
  /* fddP = */ 1,
  /* fdd = */ 0,
  /* fddC = */ 1,
  /***** -pfact: A factor to multiple the candidate p and p-dot by */
  /* pfactP = */ 1,
  /* pfact = */ 1.0,
  /* pfactC = */ 1,
  /***** -ffact: A factor to multiple the candidate f and f-dot by */
  /* ffactP = */ 1,
  /* ffact = */ 1.0,
  /* ffactC = */ 1,
  /***** -phs: Offset phase for the profil */
  /* phsP = */ 1,
  /* phs = */ 0.0,
  /* phsC = */ 1,
  /***** -start: The folding start time as a fraction of the full obs */
  /* startTP = */ 1,
  /* startT = */ 0.0,
  /* startTC = */ 1,
  /***** -end: The folding end time as a fraction of the full obs */
  /* endTP = */ 1,
  /* endT = */ 1.0,
  /* endTC = */ 1,
  /***** -psr: Name of pulsar to fold (do not include J or B) */
  /* psrnameP = */ 0,
  /* psrname = */ (char*)0,
  /* psrnameC = */ 0,
  /***** -par: Name of a TEMPO par file from which to get PSR params */
  /* parnameP = */ 0,
  /* parname = */ (char*)0,
  /* parnameC = */ 0,
  /***** -polycos: File containing TEMPO polycos for psrname (not required) */
  /* polycofileP = */ 0,
  /* polycofile = */ (char*)0,
  /* polycofileC = */ 0,
  /***** -timing: Sets useful flags for TOA generation. Generates polycos (if required) based on the par file specified as the argument. (This means you don't need the -par or -psr commands!) */
  /* timingP = */ 0,
  /* timing = */ (char*)0,
  /* timingC = */ 0,
  /***** -rzwcand: The candidate number to fold from 'infile'_rzw.cand */
  /* rzwcandP = */ 0,
  /* rzwcand = */ (int)0,
  /* rzwcandC = */ 0,
  /***** -rzwfile: Name of the rzw search '.cand' file to use (with suffix) */
  /* rzwfileP = */ 0,
  /* rzwfile = */ (char*)0,
  /* rzwfileC = */ 0,
  /***** -accelcand: The candidate number to fold from 'infile'_rzw.cand */
  /* accelcandP = */ 0,
  /* accelcand = */ (int)0,
  /* accelcandC = */ 0,
  /***** -accelfile: Name of the accel search '.cand' file to use (with suffix) */
  /* accelfileP = */ 0,
  /* accelfile = */ (char*)0,
  /* accelfileC = */ 0,
  /***** -bin: Fold a binary pulsar.  Must include all of the following parameters */
  /* binaryP = */ 0,
  /***** -pb: The orbital period (s) */
  /* pbP = */ 0,
  /* pb = */ (double)0,
  /* pbC = */ 0,
  /***** -x: The projected orbital semi-major axis (lt-sec) */
  /* asinicP = */ 0,
  /* asinic = */ (double)0,
  /* asinicC = */ 0,
  /***** -e: The orbital eccentricity */
  /* eP = */ 1,
  /* e = */ 0,
  /* eC = */ 1,
  /***** -To: The time of periastron passage (MJD) */
  /* ToP = */ 0,
  /* To = */ (double)0,
  /* ToC = */ 0,
  /***** -w: Longitude of periastron (deg) */
  /* wP = */ 0,
  /* w = */ (double)0,
  /* wC = */ 0,
  /***** -wdot: Rate of advance of periastron (deg/yr) */
  /* wdotP = */ 1,
  /* wdot = */ 0,
  /* wdotC = */ 1,
  /***** -mask: File containing masking information to use */
  /* maskfileP = */ 0,
  /* maskfile = */ (char*)0,
  /* maskfileC = */ 0,
  /***** -ignorechan: Comma separated string (no spaces!) of channels to ignore (or file containing such string).  Ranges are specified by min:max[:step] */
  /* ignorechanstrP = */ 0,
  /* ignorechanstr = */ (char*)0,
  /* ignorechanstrC = */ 0,
  /***** -events: Use a event file instead of a time series (.dat) file */
  /* eventsP = */ 0,
  /***** -days: Events are in days since the EPOCH in the '.inf' file (default is seconds) */
  /* daysP = */ 0,
  /***** -mjds: Events are in MJDs */
  /* mjdsP = */ 0,
  /***** -double: Events are in binary double precision (default is ASCII) */
  /* doubleP = */ 0,
  /***** -offset: A time offset to add to the 1st event in the same units as the events */
  /* offsetP = */ 1,
  /* offset = */ 0,
  /* offsetC = */ 1,
  /***** uninterpreted rest of command line */
  /* argc = */ 0,
  /* argv = */ (char**)0,
  /***** the original command line concatenated */
  /* full_cmd_line = */ NULL
};

/*@=null*/

/***** let LCLint run more smoothly */
/*@-predboolothers*/
/*@-boolops*/


/******************************************************************/
/*****
 This is a bit tricky. We want to make a difference between overflow
 and underflow and we want to allow v==Inf or v==-Inf but not
 v>FLT_MAX. 

 We don't use fabs to avoid linkage with -lm.
*****/
static void
checkFloatConversion(double v, char *option, char *arg)
{
  char *err = NULL;

  if( (errno==ERANGE && v!=0.0) /* even double overflowed */
      || (v<HUGE_VAL && v>-HUGE_VAL && (v<0.0?-v:v)>(double)FLT_MAX) ) {
    err = "large";
  } else if( (errno==ERANGE && v==0.0) 
	     || (v!=0.0 && (v<0.0?-v:v)<(double)FLT_MIN) ) {
    err = "small";
  }
  if( err ) {
    fprintf(stderr, 
	    "%s: parameter `%s' of option `%s' to %s to represent\n",
	    Program, arg, option, err);
    exit(EXIT_FAILURE);
  }
}

int
getIntOpt(int argc, char **argv, int i, int *value, int force)
{
  char *end;
  long v;

  if( ++i>=argc ) goto nothingFound;

  errno = 0;
  v = strtol(argv[i], &end, 0);

  /***** check for conversion error */
  if( end==argv[i] ) goto nothingFound;

  /***** check for surplus non-whitespace */
  while( isspace((int) *end) ) end+=1;
  if( *end ) goto nothingFound;

  /***** check if it fits into an int */
  if( errno==ERANGE || v>(long)INT_MAX || v<(long)INT_MIN ) {
    fprintf(stderr, 
	    "%s: parameter `%s' of option `%s' to large to represent\n",
	    Program, argv[i], argv[i-1]);
    exit(EXIT_FAILURE);
  }
  *value = (int)v;

  return i;

nothingFound:
  if( !force ) return i-1;

  fprintf(stderr, 
	  "%s: missing or malformed integer value after option `%s'\n",
	  Program, argv[i-1]);
    exit(EXIT_FAILURE);
}
/**********************************************************************/

int
getIntOpts(int argc, char **argv, int i, 
	   int **values,
	   int cmin, int cmax)
/*****
  We want to find at least cmin values and at most cmax values.
  cmax==-1 then means infinitely many are allowed.
*****/
{
  int alloced, used;
  char *end;
  long v;
  if( i+cmin >= argc ) {
    fprintf(stderr, 
	    "%s: option `%s' wants at least %d parameters\n",
	    Program, argv[i], cmin);
    exit(EXIT_FAILURE);
  }

  /***** 
    alloc a bit more than cmin values. It does not hurt to have room
    for a bit more values than cmax.
  *****/
  alloced = cmin + 4;
  *values = (int*)calloc((size_t)alloced, sizeof(int));
  if( ! *values ) {
outMem:
    fprintf(stderr, 
	    "%s: out of memory while parsing option `%s'\n",
	    Program, argv[i]);
    exit(EXIT_FAILURE);
  }

  for(used=0; (cmax==-1 || used<cmax) && used+i+1<argc; used++) {
    if( used==alloced ) {
      alloced += 8;
      *values = (int *) realloc(*values, alloced*sizeof(int));
      if( !*values ) goto outMem;
    }

    errno = 0;
    v = strtol(argv[used+i+1], &end, 0);

    /***** check for conversion error */
    if( end==argv[used+i+1] ) break;

    /***** check for surplus non-whitespace */
    while( isspace((int) *end) ) end+=1;
    if( *end ) break;

    /***** check for overflow */
    if( errno==ERANGE || v>(long)INT_MAX || v<(long)INT_MIN ) {
      fprintf(stderr, 
	      "%s: parameter `%s' of option `%s' to large to represent\n",
	      Program, argv[i+used+1], argv[i]);
      exit(EXIT_FAILURE);
    }

    (*values)[used] = (int)v;

  }
    
  if( used<cmin ) {
    fprintf(stderr, 
	    "%s: parameter `%s' of `%s' should be an "
	    "integer value\n",
	    Program, argv[i+used+1], argv[i]);
    exit(EXIT_FAILURE);
  }

  return i+used;
}
/**********************************************************************/

int
getLongOpt(int argc, char **argv, int i, long *value, int force)
{
  char *end;

  if( ++i>=argc ) goto nothingFound;

  errno = 0;
  *value = strtol(argv[i], &end, 0);

  /***** check for conversion error */
  if( end==argv[i] ) goto nothingFound;

  /***** check for surplus non-whitespace */
  while( isspace((int) *end) ) end+=1;
  if( *end ) goto nothingFound;

  /***** check for overflow */
  if( errno==ERANGE ) {
    fprintf(stderr, 
	    "%s: parameter `%s' of option `%s' to large to represent\n",
	    Program, argv[i], argv[i-1]);
    exit(EXIT_FAILURE);
  }
  return i;

nothingFound:
  /***** !force means: this parameter may be missing.*/
  if( !force ) return i-1;

  fprintf(stderr, 
	  "%s: missing or malformed value after option `%s'\n",
	  Program, argv[i-1]);
    exit(EXIT_FAILURE);
}
/**********************************************************************/

int
getLongOpts(int argc, char **argv, int i, 
	    long **values,
	    int cmin, int cmax)
/*****
  We want to find at least cmin values and at most cmax values.
  cmax==-1 then means infinitely many are allowed.
*****/
{
  int alloced, used;
  char *end;

  if( i+cmin >= argc ) {
    fprintf(stderr, 
	    "%s: option `%s' wants at least %d parameters\n",
	    Program, argv[i], cmin);
    exit(EXIT_FAILURE);
  }

  /***** 
    alloc a bit more than cmin values. It does not hurt to have room
    for a bit more values than cmax.
  *****/
  alloced = cmin + 4;
  *values = (long int *)calloc((size_t)alloced, sizeof(long));
  if( ! *values ) {
outMem:
    fprintf(stderr, 
	    "%s: out of memory while parsing option `%s'\n",
	    Program, argv[i]);
    exit(EXIT_FAILURE);
  }

  for(used=0; (cmax==-1 || used<cmax) && used+i+1<argc; used++) {
    if( used==alloced ) {
      alloced += 8;
      *values = (long int*) realloc(*values, alloced*sizeof(long));
      if( !*values ) goto outMem;
    }

    errno = 0;
    (*values)[used] = strtol(argv[used+i+1], &end, 0);

    /***** check for conversion error */
    if( end==argv[used+i+1] ) break;

    /***** check for surplus non-whitespace */
    while( isspace((int) *end) ) end+=1; 
    if( *end ) break;

    /***** check for overflow */
    if( errno==ERANGE ) {
      fprintf(stderr, 
	      "%s: parameter `%s' of option `%s' to large to represent\n",
	      Program, argv[i+used+1], argv[i]);
      exit(EXIT_FAILURE);
    }

  }
    
  if( used<cmin ) {
    fprintf(stderr, 
	    "%s: parameter `%s' of `%s' should be an "
	    "integer value\n",
	    Program, argv[i+used+1], argv[i]);
    exit(EXIT_FAILURE);
  }

  return i+used;
}
/**********************************************************************/

int
getFloatOpt(int argc, char **argv, int i, float *value, int force)
{
  char *end;
  double v;

  if( ++i>=argc ) goto nothingFound;

  errno = 0;
  v = strtod(argv[i], &end);

  /***** check for conversion error */
  if( end==argv[i] ) goto nothingFound;

  /***** check for surplus non-whitespace */
  while( isspace((int) *end) ) end+=1;
  if( *end ) goto nothingFound;

  /***** check for overflow */
  checkFloatConversion(v, argv[i-1], argv[i]);

  *value = (float)v;

  return i;

nothingFound:
  if( !force ) return i-1;

  fprintf(stderr,
	  "%s: missing or malformed float value after option `%s'\n",
	  Program, argv[i-1]);
  exit(EXIT_FAILURE);
 
}
/**********************************************************************/

int
getFloatOpts(int argc, char **argv, int i, 
	   float **values,
	   int cmin, int cmax)
/*****
  We want to find at least cmin values and at most cmax values.
  cmax==-1 then means infinitely many are allowed.
*****/
{
  int alloced, used;
  char *end;
  double v;

  if( i+cmin >= argc ) {
    fprintf(stderr, 
	    "%s: option `%s' wants at least %d parameters\n",
	    Program, argv[i], cmin);
    exit(EXIT_FAILURE);
  }

  /***** 
    alloc a bit more than cmin values.
  *****/
  alloced = cmin + 4;
  *values = (float*)calloc((size_t)alloced, sizeof(float));
  if( ! *values ) {
outMem:
    fprintf(stderr, 
	    "%s: out of memory while parsing option `%s'\n",
	    Program, argv[i]);
    exit(EXIT_FAILURE);
  }

  for(used=0; (cmax==-1 || used<cmax) && used+i+1<argc; used++) {
    if( used==alloced ) {
      alloced += 8;
      *values = (float *) realloc(*values, alloced*sizeof(float));
      if( !*values ) goto outMem;
    }

    errno = 0;
    v = strtod(argv[used+i+1], &end);

    /***** check for conversion error */
    if( end==argv[used+i+1] ) break;

    /***** check for surplus non-whitespace */
    while( isspace((int) *end) ) end+=1;
    if( *end ) break;

    /***** check for overflow */
    checkFloatConversion(v, argv[i], argv[i+used+1]);
    
    (*values)[used] = (float)v;
  }
    
  if( used<cmin ) {
    fprintf(stderr, 
	    "%s: parameter `%s' of `%s' should be a "
	    "floating-point value\n",
	    Program, argv[i+used+1], argv[i]);
    exit(EXIT_FAILURE);
  }

  return i+used;
}
/**********************************************************************/

int
getDoubleOpt(int argc, char **argv, int i, double *value, int force)
{
  char *end;

  if( ++i>=argc ) goto nothingFound;

  errno = 0;
  *value = strtod(argv[i], &end);

  /***** check for conversion error */
  if( end==argv[i] ) goto nothingFound;

  /***** check for surplus non-whitespace */
  while( isspace((int) *end) ) end+=1;
  if( *end ) goto nothingFound;

  /***** check for overflow */
  if( errno==ERANGE ) {
    fprintf(stderr, 
	    "%s: parameter `%s' of option `%s' to %s to represent\n",
	    Program, argv[i], argv[i-1],
	    (*value==0.0 ? "small" : "large"));
    exit(EXIT_FAILURE);
  }

  return i;

nothingFound:
  if( !force ) return i-1;

  fprintf(stderr,
	  "%s: missing or malformed value after option `%s'\n",
	  Program, argv[i-1]);
  exit(EXIT_FAILURE);
 
}
/**********************************************************************/

int
getDoubleOpts(int argc, char **argv, int i, 
	   double **values,
	   int cmin, int cmax)
/*****
  We want to find at least cmin values and at most cmax values.
  cmax==-1 then means infinitely many are allowed.
*****/
{
  int alloced, used;
  char *end;

  if( i+cmin >= argc ) {
    fprintf(stderr, 
	    "%s: option `%s' wants at least %d parameters\n",
	    Program, argv[i], cmin);
    exit(EXIT_FAILURE);
  }

  /***** 
    alloc a bit more than cmin values.
  *****/
  alloced = cmin + 4;
  *values = (double*)calloc((size_t)alloced, sizeof(double));
  if( ! *values ) {
outMem:
    fprintf(stderr, 
	    "%s: out of memory while parsing option `%s'\n",
	    Program, argv[i]);
    exit(EXIT_FAILURE);
  }

  for(used=0; (cmax==-1 || used<cmax) && used+i+1<argc; used++) {
    if( used==alloced ) {
      alloced += 8;
      *values = (double *) realloc(*values, alloced*sizeof(double));
      if( !*values ) goto outMem;
    }

    errno = 0;
    (*values)[used] = strtod(argv[used+i+1], &end);

    /***** check for conversion error */
    if( end==argv[used+i+1] ) break;

    /***** check for surplus non-whitespace */
    while( isspace((int) *end) ) end+=1;
    if( *end ) break;

    /***** check for overflow */
    if( errno==ERANGE ) {
      fprintf(stderr, 
	      "%s: parameter `%s' of option `%s' to %s to represent\n",
	      Program, argv[i+used+1], argv[i],
	      ((*values)[used]==0.0 ? "small" : "large"));
      exit(EXIT_FAILURE);
    }

  }
    
  if( used<cmin ) {
    fprintf(stderr, 
	    "%s: parameter `%s' of `%s' should be a "
	    "double value\n",
	    Program, argv[i+used+1], argv[i]);
    exit(EXIT_FAILURE);
  }

  return i+used;
}
/**********************************************************************/

/**
  force will be set if we need at least one argument for the option.
*****/
int
getStringOpt(int argc, char **argv, int i, char **value, int force)
{
  i += 1;
  if( i>=argc ) {
    if( force ) {
      fprintf(stderr, "%s: missing string after option `%s'\n",
	      Program, argv[i-1]);
      exit(EXIT_FAILURE);
    } 
    return i-1;
  }
  
  if( !force && argv[i][0] == '-' ) return i-1;
  *value = argv[i];
  return i;
}
/**********************************************************************/

int
getStringOpts(int argc, char **argv, int i, 
	   char*  **values,
	   int cmin, int cmax)
/*****
  We want to find at least cmin values and at most cmax values.
  cmax==-1 then means infinitely many are allowed.
*****/
{
  int alloced, used;

  if( i+cmin >= argc ) {
    fprintf(stderr, 
	    "%s: option `%s' wants at least %d parameters\n",
	    Program, argv[i], cmin);
    exit(EXIT_FAILURE);
  }

  alloced = cmin + 4;
    
  *values = (char**)calloc((size_t)alloced, sizeof(char*));
  if( ! *values ) {
outMem:
    fprintf(stderr, 
	    "%s: out of memory during parsing of option `%s'\n",
	    Program, argv[i]);
    exit(EXIT_FAILURE);
  }

  for(used=0; (cmax==-1 || used<cmax) && used+i+1<argc; used++) {
    if( used==alloced ) {
      alloced += 8;
      *values = (char **)realloc(*values, alloced*sizeof(char*));
      if( !*values ) goto outMem;
    }

    if( used>=cmin && argv[used+i+1][0]=='-' ) break;
    (*values)[used] = argv[used+i+1];
  }
    
  if( used<cmin ) {
    fprintf(stderr, 
    "%s: less than %d parameters for option `%s', only %d found\n",
	    Program, cmin, argv[i], used);
    exit(EXIT_FAILURE);
  }

  return i+used;
}
/**********************************************************************/

void
checkIntLower(char *opt, int *values, int count, int max)
{
  int i;

  for(i=0; i<count; i++) {
    if( values[i]<=max ) continue;
    fprintf(stderr, 
	    "%s: parameter %d of option `%s' greater than max=%d\n",
	    Program, i+1, opt, max);
    exit(EXIT_FAILURE);
  }
}
/**********************************************************************/

void
checkIntHigher(char *opt, int *values, int count, int min)
{
  int i;

  for(i=0; i<count; i++) {
    if( values[i]>=min ) continue;
    fprintf(stderr, 
	    "%s: parameter %d of option `%s' smaller than min=%d\n",
	    Program, i+1, opt, min);
    exit(EXIT_FAILURE);
  }
}
/**********************************************************************/

void
checkLongLower(char *opt, long *values, int count, long max)
{
  int i;

  for(i=0; i<count; i++) {
    if( values[i]<=max ) continue;
    fprintf(stderr, 
	    "%s: parameter %d of option `%s' greater than max=%ld\n",
	    Program, i+1, opt, max);
    exit(EXIT_FAILURE);
  }
}
/**********************************************************************/

void
checkLongHigher(char *opt, long *values, int count, long min)
{
  int i;

  for(i=0; i<count; i++) {
    if( values[i]>=min ) continue;
    fprintf(stderr, 
	    "%s: parameter %d of option `%s' smaller than min=%ld\n",
	    Program, i+1, opt, min);
    exit(EXIT_FAILURE);
  }
}
/**********************************************************************/

void
checkFloatLower(char *opt, float *values, int count, float max)
{
  int i;

  for(i=0; i<count; i++) {
    if( values[i]<=max ) continue;
    fprintf(stderr, 
	    "%s: parameter %d of option `%s' greater than max=%f\n",
	    Program, i+1, opt, max);
    exit(EXIT_FAILURE);
  }
}
/**********************************************************************/

void
checkFloatHigher(char *opt, float *values, int count, float min)
{
  int i;

  for(i=0; i<count; i++) {
    if( values[i]>=min ) continue;
    fprintf(stderr, 
	    "%s: parameter %d of option `%s' smaller than min=%f\n",
	    Program, i+1, opt, min);
    exit(EXIT_FAILURE);
  }
}
/**********************************************************************/

void
checkDoubleLower(char *opt, double *values, int count, double max)
{
  int i;

  for(i=0; i<count; i++) {
    if( values[i]<=max ) continue;
    fprintf(stderr, 
	    "%s: parameter %d of option `%s' greater than max=%f\n",
	    Program, i+1, opt, max);
    exit(EXIT_FAILURE);
  }
}
/**********************************************************************/

void
checkDoubleHigher(char *opt, double *values, int count, double min)
{
  int i;

  for(i=0; i<count; i++) {
    if( values[i]>=min ) continue;
    fprintf(stderr, 
	    "%s: parameter %d of option `%s' smaller than min=%f\n",
	    Program, i+1, opt, min);
    exit(EXIT_FAILURE);
  }
}
/**********************************************************************/

static char *
catArgv(int argc, char **argv)
{
  int i;
  size_t l;
  char *s, *t;

  for(i=0, l=0; i<argc; i++) l += (1+strlen(argv[i]));
  s = (char *)malloc(l);
  if( !s ) {
    fprintf(stderr, "%s: out of memory\n", Program);
    exit(EXIT_FAILURE);
  }
  strcpy(s, argv[0]);
  t = s;
  for(i=1; i<argc; i++) {
    t = t+strlen(t);
    *t++ = ' ';
    strcpy(t, argv[i]);
  }
  return s;
}
/**********************************************************************/

void
showOptionValues(void)
{
  int i;

  printf("Full command line is:\n`%s'\n", cmd.full_cmd_line);

  /***** -ncpus: Number of processors to use with OpenMP */
  if( !cmd.ncpusP ) {
    printf("-ncpus not found.\n");
  } else {
    printf("-ncpus found:\n");
    if( !cmd.ncpusC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%d'\n", cmd.ncpus);
    }
  }

  /***** -o: Root of the output file names */
  if( !cmd.outfileP ) {
    printf("-o not found.\n");
  } else {
    printf("-o found:\n");
    if( !cmd.outfileC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%s'\n", cmd.outfile);
    }
  }

  /***** -filterbank: Raw data in SIGPROC filterbank format */
  if( !cmd.filterbankP ) {
    printf("-filterbank not found.\n");
  } else {
    printf("-filterbank found:\n");
  }

  /***** -psrfits: Raw data in PSRFITS format */
  if( !cmd.psrfitsP ) {
    printf("-psrfits not found.\n");
  } else {
    printf("-psrfits found:\n");
  }

  /***** -noweights: Do not apply PSRFITS weights */
  if( !cmd.noweightsP ) {
    printf("-noweights not found.\n");
  } else {
    printf("-noweights found:\n");
  }

  /***** -noscales: Do not apply PSRFITS scales */
  if( !cmd.noscalesP ) {
    printf("-noscales not found.\n");
  } else {
    printf("-noscales found:\n");
  }

  /***** -nooffsets: Do not apply PSRFITS offsets */
  if( !cmd.nooffsetsP ) {
    printf("-nooffsets not found.\n");
  } else {
    printf("-nooffsets found:\n");
  }

  /***** -wapp: Raw data in Wideband Arecibo Pulsar Processor (WAPP) format */
  if( !cmd.wappP ) {
    printf("-wapp not found.\n");
  } else {
    printf("-wapp found:\n");
  }

  /***** -window: Window correlator lags with a Hamming window before FFTing */
  if( !cmd.windowP ) {
    printf("-window not found.\n");
  } else {
    printf("-window found:\n");
  }

  /***** -topo: Fold the data topocentrically (i.e. don't barycenter) */
  if( !cmd.topoP ) {
    printf("-topo not found.\n");
  } else {
    printf("-topo found:\n");
  }

  /***** -invert: For rawdata, flip (or invert) the band */
  if( !cmd.invertP ) {
    printf("-invert not found.\n");
  } else {
    printf("-invert found:\n");
  }

  /***** -zerodm: Subtract the mean of all channels from each sample (i.e. remove zero DM) */
  if( !cmd.zerodmP ) {
    printf("-zerodm not found.\n");
  } else {
    printf("-zerodm found:\n");
  }

  /***** -absphase: Use the absolute phase associated with polycos */
  if( !cmd.absphaseP ) {
    printf("-absphase not found.\n");
  } else {
    printf("-absphase found:\n");
  }

  /***** -barypolycos: Force the use of polycos for barycentered events */
  if( !cmd.barypolycosP ) {
    printf("-barypolycos not found.\n");
  } else {
    printf("-barypolycos found:\n");
  }

  /***** -debug: Show debugging output when calling TEMPO for polycos */
  if( !cmd.debugP ) {
    printf("-debug not found.\n");
  } else {
    printf("-debug found:\n");
  }

  /***** -samples: Treat the data as samples and not as finite-duration integrated data */
  if( !cmd.samplesP ) {
    printf("-samples not found.\n");
  } else {
    printf("-samples found:\n");
  }

  /***** -normalize: Bandpass flatten the data by normalizing the subbands */
  if( !cmd.normalizeP ) {
    printf("-normalize not found.\n");
  } else {
    printf("-normalize found:\n");
  }

  /***** -numwapps: Number of WAPPs used with contiguous frequencies */
  if( !cmd.numwappsP ) {
    printf("-numwapps not found.\n");
  } else {
    printf("-numwapps found:\n");
    if( !cmd.numwappsC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%d'\n", cmd.numwapps);
    }
  }

  /***** -if: A specific IF to use if available (summed IFs is the default) */
  if( !cmd.ifsP ) {
    printf("-if not found.\n");
  } else {
    printf("-if found:\n");
    if( !cmd.ifsC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%d'\n", cmd.ifs);
    }
  }

  /***** -clip: Time-domain sigma to use for clipping (0.0 = no clipping, 6.0 = default */
  if( !cmd.clipP ) {
    printf("-clip not found.\n");
  } else {
    printf("-clip found:\n");
    if( !cmd.clipC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%.40g'\n", cmd.clip);
    }
  }

  /***** -noclip: Do not clip the data.  (The default is to _always_ clip!) */
  if( !cmd.noclipP ) {
    printf("-noclip not found.\n");
  } else {
    printf("-noclip found:\n");
  }

  /***** -noxwin: Do not show the result plots on-screen, only make postscript files */
  if( !cmd.noxwinP ) {
    printf("-noxwin not found.\n");
  } else {
    printf("-noxwin found:\n");
  }

  /***** -runavg: Subtract each blocks average as it is read (single channel data only) */
  if( !cmd.runavgP ) {
    printf("-runavg not found.\n");
  } else {
    printf("-runavg found:\n");
  }

  /***** -fine: A finer gridding in the p/pdot plane (for well known p and pdot) */
  if( !cmd.fineP ) {
    printf("-fine not found.\n");
  } else {
    printf("-fine found:\n");
  }

  /***** -coarse: A coarser gridding in the p/pdot plane (for uknown p and pdot) */
  if( !cmd.coarseP ) {
    printf("-coarse not found.\n");
  } else {
    printf("-coarse found:\n");
  }

  /***** -slow: Sets useful flags for slow pulsars */
  if( !cmd.slowP ) {
    printf("-slow not found.\n");
  } else {
    printf("-slow found:\n");
  }

  /***** -searchpdd: Search p-dotdots as well as p and p-dots */
  if( !cmd.searchpddP ) {
    printf("-searchpdd not found.\n");
  } else {
    printf("-searchpdd found:\n");
  }

  /***** -searchfdd: Search f-dotdots as well as f and f-dots */
  if( !cmd.searchfddP ) {
    printf("-searchfdd not found.\n");
  } else {
    printf("-searchfdd found:\n");
  }

  /***** -nosearch: Show but do not search the p/pdot and/or DM phase spaces */
  if( !cmd.nosearchP ) {
    printf("-nosearch not found.\n");
  } else {
    printf("-nosearch found:\n");
  }

  /***** -nopsearch: Show but do not search over period */
  if( !cmd.nopsearchP ) {
    printf("-nopsearch not found.\n");
  } else {
    printf("-nopsearch found:\n");
  }

  /***** -nopdsearch: Show but do not search over p-dot */
  if( !cmd.nopdsearchP ) {
    printf("-nopdsearch not found.\n");
  } else {
    printf("-nopdsearch found:\n");
  }

  /***** -nodmsearch: Show but do not search over DM */
  if( !cmd.nodmsearchP ) {
    printf("-nodmsearch not found.\n");
  } else {
    printf("-nodmsearch found:\n");
  }

  /***** -scaleparts: Scale the part profiles independently */
  if( !cmd.scalepartsP ) {
    printf("-scaleparts not found.\n");
  } else {
    printf("-scaleparts found:\n");
  }

  /***** -allgrey: Make all the images greyscale instead of color */
  if( !cmd.allgreyP ) {
    printf("-allgrey not found.\n");
  } else {
    printf("-allgrey found:\n");
  }

  /***** -fixchi: Adjust the reduced chi^2 values so that off-pulse reduced chi^2 = 1 */
  if( !cmd.fixchiP ) {
    printf("-fixchi not found.\n");
  } else {
    printf("-fixchi found:\n");
  }

  /***** -justprofs: Only output the profile portions of the plot */
  if( !cmd.justprofsP ) {
    printf("-justprofs not found.\n");
  } else {
    printf("-justprofs found:\n");
  }

  /***** -dm: The central DM of the search (cm^-3 pc) */
  if( !cmd.dmP ) {
    printf("-dm not found.\n");
  } else {
    printf("-dm found:\n");
    if( !cmd.dmC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%.40g'\n", cmd.dm);
    }
  }

  /***** -n: The number of bins in the profile.  Defaults to the number of sampling bins which correspond to one folded period */
  if( !cmd.proflenP ) {
    printf("-n not found.\n");
  } else {
    printf("-n found:\n");
    if( !cmd.proflenC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%d'\n", cmd.proflen);
    }
  }

  /***** -nsub: The number of sub-bands to use for the DM search.  If unspecified, will use something reasonable. */
  if( !cmd.nsubP ) {
    printf("-nsub not found.\n");
  } else {
    printf("-nsub found:\n");
    if( !cmd.nsubC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%d'\n", cmd.nsub);
    }
  }

  /***** -npart: The number of sub-integrations to use for the period search */
  if( !cmd.npartP ) {
    printf("-npart not found.\n");
  } else {
    printf("-npart found:\n");
    if( !cmd.npartC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%d'\n", cmd.npart);
    }
  }

  /***** -pstep: The minimum period stepsize over the observation in profile bins */
  if( !cmd.pstepP ) {
    printf("-pstep not found.\n");
  } else {
    printf("-pstep found:\n");
    if( !cmd.pstepC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%d'\n", cmd.pstep);
    }
  }

  /***** -pdstep: The minimum P-dot stepsize over the observation in profile bins */
  if( !cmd.pdstepP ) {
    printf("-pdstep not found.\n");
  } else {
    printf("-pdstep found:\n");
    if( !cmd.pdstepC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%d'\n", cmd.pdstep);
    }
  }

  /***** -dmstep: The minimum DM stepsize over the observation in profile bins */
  if( !cmd.dmstepP ) {
    printf("-dmstep not found.\n");
  } else {
    printf("-dmstep found:\n");
    if( !cmd.dmstepC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%d'\n", cmd.dmstep);
    }
  }

  /***** -npfact: 2 * npfact * proflen + 1 periods and p-dots will be searched */
  if( !cmd.npfactP ) {
    printf("-npfact not found.\n");
  } else {
    printf("-npfact found:\n");
    if( !cmd.npfactC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%d'\n", cmd.npfact);
    }
  }

  /***** -ndmfact: 2 * ndmfact * proflen + 1 DMs will be searched */
  if( !cmd.ndmfactP ) {
    printf("-ndmfact not found.\n");
  } else {
    printf("-ndmfact found:\n");
    if( !cmd.ndmfactC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%d'\n", cmd.ndmfact);
    }
  }

  /***** -p: The nominative folding period (s) */
  if( !cmd.pP ) {
    printf("-p not found.\n");
  } else {
    printf("-p found:\n");
    if( !cmd.pC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%.40g'\n", cmd.p);
    }
  }

  /***** -pd: The nominative period derivative (s/s) */
  if( !cmd.pdP ) {
    printf("-pd not found.\n");
  } else {
    printf("-pd found:\n");
    if( !cmd.pdC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%.40g'\n", cmd.pd);
    }
  }

  /***** -pdd: The nominative period 2nd derivative (s/s^2) */
  if( !cmd.pddP ) {
    printf("-pdd not found.\n");
  } else {
    printf("-pdd found:\n");
    if( !cmd.pddC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%.40g'\n", cmd.pdd);
    }
  }

  /***** -f: The nominative folding frequency (hz) */
  if( !cmd.fP ) {
    printf("-f not found.\n");
  } else {
    printf("-f found:\n");
    if( !cmd.fC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%.40g'\n", cmd.f);
    }
  }

  /***** -fd: The nominative frequency derivative (hz/s) */
  if( !cmd.fdP ) {
    printf("-fd not found.\n");
  } else {
    printf("-fd found:\n");
    if( !cmd.fdC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%.40g'\n", cmd.fd);
    }
  }

  /***** -fdd: The nominative frequency 2nd derivative (hz/s^2) */
  if( !cmd.fddP ) {
    printf("-fdd not found.\n");
  } else {
    printf("-fdd found:\n");
    if( !cmd.fddC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%.40g'\n", cmd.fdd);
    }
  }

  /***** -pfact: A factor to multiple the candidate p and p-dot by */
  if( !cmd.pfactP ) {
    printf("-pfact not found.\n");
  } else {
    printf("-pfact found:\n");
    if( !cmd.pfactC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%.40g'\n", cmd.pfact);
    }
  }

  /***** -ffact: A factor to multiple the candidate f and f-dot by */
  if( !cmd.ffactP ) {
    printf("-ffact not found.\n");
  } else {
    printf("-ffact found:\n");
    if( !cmd.ffactC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%.40g'\n", cmd.ffact);
    }
  }

  /***** -phs: Offset phase for the profil */
  if( !cmd.phsP ) {
    printf("-phs not found.\n");
  } else {
    printf("-phs found:\n");
    if( !cmd.phsC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%.40g'\n", cmd.phs);
    }
  }

  /***** -start: The folding start time as a fraction of the full obs */
  if( !cmd.startTP ) {
    printf("-start not found.\n");
  } else {
    printf("-start found:\n");
    if( !cmd.startTC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%.40g'\n", cmd.startT);
    }
  }

  /***** -end: The folding end time as a fraction of the full obs */
  if( !cmd.endTP ) {
    printf("-end not found.\n");
  } else {
    printf("-end found:\n");
    if( !cmd.endTC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%.40g'\n", cmd.endT);
    }
  }

  /***** -psr: Name of pulsar to fold (do not include J or B) */
  if( !cmd.psrnameP ) {
    printf("-psr not found.\n");
  } else {
    printf("-psr found:\n");
    if( !cmd.psrnameC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%s'\n", cmd.psrname);
    }
  }

  /***** -par: Name of a TEMPO par file from which to get PSR params */
  if( !cmd.parnameP ) {
    printf("-par not found.\n");
  } else {
    printf("-par found:\n");
    if( !cmd.parnameC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%s'\n", cmd.parname);
    }
  }

  /***** -polycos: File containing TEMPO polycos for psrname (not required) */
  if( !cmd.polycofileP ) {
    printf("-polycos not found.\n");
  } else {
    printf("-polycos found:\n");
    if( !cmd.polycofileC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%s'\n", cmd.polycofile);
    }
  }

  /***** -timing: Sets useful flags for TOA generation. Generates polycos (if required) based on the par file specified as the argument. (This means you don't need the -par or -psr commands!) */
  if( !cmd.timingP ) {
    printf("-timing not found.\n");
  } else {
    printf("-timing found:\n");
    if( !cmd.timingC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%s'\n", cmd.timing);
    }
  }

  /***** -rzwcand: The candidate number to fold from 'infile'_rzw.cand */
  if( !cmd.rzwcandP ) {
    printf("-rzwcand not found.\n");
  } else {
    printf("-rzwcand found:\n");
    if( !cmd.rzwcandC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%d'\n", cmd.rzwcand);
    }
  }

  /***** -rzwfile: Name of the rzw search '.cand' file to use (with suffix) */
  if( !cmd.rzwfileP ) {
    printf("-rzwfile not found.\n");
  } else {
    printf("-rzwfile found:\n");
    if( !cmd.rzwfileC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%s'\n", cmd.rzwfile);
    }
  }

  /***** -accelcand: The candidate number to fold from 'infile'_rzw.cand */
  if( !cmd.accelcandP ) {
    printf("-accelcand not found.\n");
  } else {
    printf("-accelcand found:\n");
    if( !cmd.accelcandC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%d'\n", cmd.accelcand);
    }
  }

  /***** -accelfile: Name of the accel search '.cand' file to use (with suffix) */
  if( !cmd.accelfileP ) {
    printf("-accelfile not found.\n");
  } else {
    printf("-accelfile found:\n");
    if( !cmd.accelfileC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%s'\n", cmd.accelfile);
    }
  }

  /***** -bin: Fold a binary pulsar.  Must include all of the following parameters */
  if( !cmd.binaryP ) {
    printf("-bin not found.\n");
  } else {
    printf("-bin found:\n");
  }

  /***** -pb: The orbital period (s) */
  if( !cmd.pbP ) {
    printf("-pb not found.\n");
  } else {
    printf("-pb found:\n");
    if( !cmd.pbC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%.40g'\n", cmd.pb);
    }
  }

  /***** -x: The projected orbital semi-major axis (lt-sec) */
  if( !cmd.asinicP ) {
    printf("-x not found.\n");
  } else {
    printf("-x found:\n");
    if( !cmd.asinicC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%.40g'\n", cmd.asinic);
    }
  }

  /***** -e: The orbital eccentricity */
  if( !cmd.eP ) {
    printf("-e not found.\n");
  } else {
    printf("-e found:\n");
    if( !cmd.eC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%.40g'\n", cmd.e);
    }
  }

  /***** -To: The time of periastron passage (MJD) */
  if( !cmd.ToP ) {
    printf("-To not found.\n");
  } else {
    printf("-To found:\n");
    if( !cmd.ToC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%.40g'\n", cmd.To);
    }
  }

  /***** -w: Longitude of periastron (deg) */
  if( !cmd.wP ) {
    printf("-w not found.\n");
  } else {
    printf("-w found:\n");
    if( !cmd.wC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%.40g'\n", cmd.w);
    }
  }

  /***** -wdot: Rate of advance of periastron (deg/yr) */
  if( !cmd.wdotP ) {
    printf("-wdot not found.\n");
  } else {
    printf("-wdot found:\n");
    if( !cmd.wdotC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%.40g'\n", cmd.wdot);
    }
  }

  /***** -mask: File containing masking information to use */
  if( !cmd.maskfileP ) {
    printf("-mask not found.\n");
  } else {
    printf("-mask found:\n");
    if( !cmd.maskfileC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%s'\n", cmd.maskfile);
    }
  }

  /***** -ignorechan: Comma separated string (no spaces!) of channels to ignore (or file containing such string).  Ranges are specified by min:max[:step] */
  if( !cmd.ignorechanstrP ) {
    printf("-ignorechan not found.\n");
  } else {
    printf("-ignorechan found:\n");
    if( !cmd.ignorechanstrC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%s'\n", cmd.ignorechanstr);
    }
  }

  /***** -events: Use a event file instead of a time series (.dat) file */
  if( !cmd.eventsP ) {
    printf("-events not found.\n");
  } else {
    printf("-events found:\n");
  }

  /***** -days: Events are in days since the EPOCH in the '.inf' file (default is seconds) */
  if( !cmd.daysP ) {
    printf("-days not found.\n");
  } else {
    printf("-days found:\n");
  }

  /***** -mjds: Events are in MJDs */
  if( !cmd.mjdsP ) {
    printf("-mjds not found.\n");
  } else {
    printf("-mjds found:\n");
  }

  /***** -double: Events are in binary double precision (default is ASCII) */
  if( !cmd.doubleP ) {
    printf("-double not found.\n");
  } else {
    printf("-double found:\n");
  }

  /***** -offset: A time offset to add to the 1st event in the same units as the events */
  if( !cmd.offsetP ) {
    printf("-offset not found.\n");
  } else {
    printf("-offset found:\n");
    if( !cmd.offsetC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%.40g'\n", cmd.offset);
    }
  }
  if( !cmd.argc ) {
    printf("no remaining parameters in argv\n");
  } else {
    printf("argv =");
    for(i=0; i<cmd.argc; i++) {
      printf(" `%s'", cmd.argv[i]);
    }
    printf("\n");
  }
}
/**********************************************************************/

void
usage(void)
{
  fprintf(stderr,"%s","   [-ncpus ncpus] [-o outfile] [-filterbank] [-psrfits] [-noweights] [-noscales] [-nooffsets] [-wapp] [-window] [-topo] [-invert] [-zerodm] [-absphase] [-barypolycos] [-debug] [-samples] [-normalize] [-numwapps numwapps] [-if ifs] [-clip clip] [-noclip] [-noxwin] [-runavg] [-fine] [-coarse] [-slow] [-searchpdd] [-searchfdd] [-nosearch] [-nopsearch] [-nopdsearch] [-nodmsearch] [-scaleparts] [-allgrey] [-fixchi] [-justprofs] [-dm dm] [-n proflen] [-nsub nsub] [-npart npart] [-pstep pstep] [-pdstep pdstep] [-dmstep dmstep] [-npfact npfact] [-ndmfact ndmfact] [-p p] [-pd pd] [-pdd pdd] [-f f] [-fd fd] [-fdd fdd] [-pfact pfact] [-ffact ffact] [-phs phs] [-start startT] [-end endT] [-psr psrname] [-par parname] [-polycos polycofile] [-timing timing] [-rzwcand rzwcand] [-rzwfile rzwfile] [-accelcand accelcand] [-accelfile accelfile] [-bin] [-pb pb] [-x asinic] [-e e] [-To To] [-w w] [-wdot wdot] [-mask maskfile] [-ignorechan ignorechanstr] [-events] [-days] [-mjds] [-double] [-offset offset] [--] infile ...\n");
  fprintf(stderr,"%s","      Prepares (if required) and folds raw radio data, standard time series, or events.\n");
  fprintf(stderr,"%s","          -ncpus: Number of processors to use with OpenMP\n");
  fprintf(stderr,"%s","                  1 int value between 1 and oo\n");
  fprintf(stderr,"%s","                  default: `1'\n");
  fprintf(stderr,"%s","              -o: Root of the output file names\n");
  fprintf(stderr,"%s","                  1 char* value\n");
  fprintf(stderr,"%s","     -filterbank: Raw data in SIGPROC filterbank format\n");
  fprintf(stderr,"%s","        -psrfits: Raw data in PSRFITS format\n");
  fprintf(stderr,"%s","      -noweights: Do not apply PSRFITS weights\n");
  fprintf(stderr,"%s","       -noscales: Do not apply PSRFITS scales\n");
  fprintf(stderr,"%s","      -nooffsets: Do not apply PSRFITS offsets\n");
  fprintf(stderr,"%s","           -wapp: Raw data in Wideband Arecibo Pulsar Processor (WAPP) format\n");
  fprintf(stderr,"%s","         -window: Window correlator lags with a Hamming window before FFTing\n");
  fprintf(stderr,"%s","           -topo: Fold the data topocentrically (i.e. don't barycenter)\n");
  fprintf(stderr,"%s","         -invert: For rawdata, flip (or invert) the band\n");
  fprintf(stderr,"%s","         -zerodm: Subtract the mean of all channels from each sample (i.e. remove zero DM)\n");
  fprintf(stderr,"%s","       -absphase: Use the absolute phase associated with polycos\n");
  fprintf(stderr,"%s","    -barypolycos: Force the use of polycos for barycentered events\n");
  fprintf(stderr,"%s","          -debug: Show debugging output when calling TEMPO for polycos\n");
  fprintf(stderr,"%s","        -samples: Treat the data as samples and not as finite-duration integrated data\n");
  fprintf(stderr,"%s","      -normalize: Bandpass flatten the data by normalizing the subbands\n");
  fprintf(stderr,"%s","       -numwapps: Number of WAPPs used with contiguous frequencies\n");
  fprintf(stderr,"%s","                  1 int value between 1 and 8\n");
  fprintf(stderr,"%s","                  default: `1'\n");
  fprintf(stderr,"%s","             -if: A specific IF to use if available (summed IFs is the default)\n");
  fprintf(stderr,"%s","                  1 int value between 0 and 1\n");
  fprintf(stderr,"%s","           -clip: Time-domain sigma to use for clipping (0.0 = no clipping, 6.0 = default\n");
  fprintf(stderr,"%s","                  1 float value between 0 and 1000.0\n");
  fprintf(stderr,"%s","                  default: `6.0'\n");
  fprintf(stderr,"%s","         -noclip: Do not clip the data.  (The default is to _always_ clip!)\n");
  fprintf(stderr,"%s","         -noxwin: Do not show the result plots on-screen, only make postscript files\n");
  fprintf(stderr,"%s","         -runavg: Subtract each blocks average as it is read (single channel data only)\n");
  fprintf(stderr,"%s","           -fine: A finer gridding in the p/pdot plane (for well known p and pdot)\n");
  fprintf(stderr,"%s","         -coarse: A coarser gridding in the p/pdot plane (for uknown p and pdot)\n");
  fprintf(stderr,"%s","           -slow: Sets useful flags for slow pulsars\n");
  fprintf(stderr,"%s","      -searchpdd: Search p-dotdots as well as p and p-dots\n");
  fprintf(stderr,"%s","      -searchfdd: Search f-dotdots as well as f and f-dots\n");
  fprintf(stderr,"%s","       -nosearch: Show but do not search the p/pdot and/or DM phase spaces\n");
  fprintf(stderr,"%s","      -nopsearch: Show but do not search over period\n");
  fprintf(stderr,"%s","     -nopdsearch: Show but do not search over p-dot\n");
  fprintf(stderr,"%s","     -nodmsearch: Show but do not search over DM\n");
  fprintf(stderr,"%s","     -scaleparts: Scale the part profiles independently\n");
  fprintf(stderr,"%s","        -allgrey: Make all the images greyscale instead of color\n");
  fprintf(stderr,"%s","         -fixchi: Adjust the reduced chi^2 values so that off-pulse reduced chi^2 = 1\n");
  fprintf(stderr,"%s","      -justprofs: Only output the profile portions of the plot\n");
  fprintf(stderr,"%s","             -dm: The central DM of the search (cm^-3 pc)\n");
  fprintf(stderr,"%s","                  1 double value between 0 and oo\n");
  fprintf(stderr,"%s","                  default: `0'\n");
  fprintf(stderr,"%s","              -n: The number of bins in the profile.  Defaults to the number of sampling bins which correspond to one folded period\n");
  fprintf(stderr,"%s","                  1 int value\n");
  fprintf(stderr,"%s","           -nsub: The number of sub-bands to use for the DM search.  If unspecified, will use something reasonable.\n");
  fprintf(stderr,"%s","                  1 int value between 1 and 4096\n");
  fprintf(stderr,"%s","          -npart: The number of sub-integrations to use for the period search\n");
  fprintf(stderr,"%s","                  1 int value between 1 and 4096\n");
  fprintf(stderr,"%s","                  default: `64'\n");
  fprintf(stderr,"%s","          -pstep: The minimum period stepsize over the observation in profile bins\n");
  fprintf(stderr,"%s","                  1 int value between 1 and 10\n");
  fprintf(stderr,"%s","                  default: `2'\n");
  fprintf(stderr,"%s","         -pdstep: The minimum P-dot stepsize over the observation in profile bins\n");
  fprintf(stderr,"%s","                  1 int value between 1 and 20\n");
  fprintf(stderr,"%s","                  default: `4'\n");
  fprintf(stderr,"%s","         -dmstep: The minimum DM stepsize over the observation in profile bins\n");
  fprintf(stderr,"%s","                  1 int value between 1 and 10\n");
  fprintf(stderr,"%s","                  default: `2'\n");
  fprintf(stderr,"%s","         -npfact: 2 * npfact * proflen + 1 periods and p-dots will be searched\n");
  fprintf(stderr,"%s","                  1 int value between 1 and 10\n");
  fprintf(stderr,"%s","                  default: `2'\n");
  fprintf(stderr,"%s","        -ndmfact: 2 * ndmfact * proflen + 1 DMs will be searched\n");
  fprintf(stderr,"%s","                  1 int value between 1 and 1000\n");
  fprintf(stderr,"%s","                  default: `3'\n");
  fprintf(stderr,"%s","              -p: The nominative folding period (s)\n");
  fprintf(stderr,"%s","                  1 double value between 0 and oo\n");
  fprintf(stderr,"%s","             -pd: The nominative period derivative (s/s)\n");
  fprintf(stderr,"%s","                  1 double value\n");
  fprintf(stderr,"%s","                  default: `0.0'\n");
  fprintf(stderr,"%s","            -pdd: The nominative period 2nd derivative (s/s^2)\n");
  fprintf(stderr,"%s","                  1 double value\n");
  fprintf(stderr,"%s","                  default: `0.0'\n");
  fprintf(stderr,"%s","              -f: The nominative folding frequency (hz)\n");
  fprintf(stderr,"%s","                  1 double value between 0 and oo\n");
  fprintf(stderr,"%s","             -fd: The nominative frequency derivative (hz/s)\n");
  fprintf(stderr,"%s","                  1 double value\n");
  fprintf(stderr,"%s","                  default: `0'\n");
  fprintf(stderr,"%s","            -fdd: The nominative frequency 2nd derivative (hz/s^2)\n");
  fprintf(stderr,"%s","                  1 double value\n");
  fprintf(stderr,"%s","                  default: `0'\n");
  fprintf(stderr,"%s","          -pfact: A factor to multiple the candidate p and p-dot by\n");
  fprintf(stderr,"%s","                  1 double value between 0.0 and 100.0\n");
  fprintf(stderr,"%s","                  default: `1.0'\n");
  fprintf(stderr,"%s","          -ffact: A factor to multiple the candidate f and f-dot by\n");
  fprintf(stderr,"%s","                  1 double value between 0.0 and 100.0\n");
  fprintf(stderr,"%s","                  default: `1.0'\n");
  fprintf(stderr,"%s","            -phs: Offset phase for the profil\n");
  fprintf(stderr,"%s","                  1 double value between 0.0 and 1.0\n");
  fprintf(stderr,"%s","                  default: `0.0'\n");
  fprintf(stderr,"%s","          -start: The folding start time as a fraction of the full obs\n");
  fprintf(stderr,"%s","                  1 double value between 0.0 and 1.0\n");
  fprintf(stderr,"%s","                  default: `0.0'\n");
  fprintf(stderr,"%s","            -end: The folding end time as a fraction of the full obs\n");
  fprintf(stderr,"%s","                  1 double value between 0.0 and 1.0\n");
  fprintf(stderr,"%s","                  default: `1.0'\n");
  fprintf(stderr,"%s","            -psr: Name of pulsar to fold (do not include J or B)\n");
  fprintf(stderr,"%s","                  1 char* value\n");
  fprintf(stderr,"%s","            -par: Name of a TEMPO par file from which to get PSR params\n");
  fprintf(stderr,"%s","                  1 char* value\n");
  fprintf(stderr,"%s","        -polycos: File containing TEMPO polycos for psrname (not required)\n");
  fprintf(stderr,"%s","                  1 char* value\n");
  fprintf(stderr,"%s","         -timing: Sets useful flags for TOA generation. Generates polycos (if required) based on the par file specified as the argument. (This means you don't need the -par or -psr commands!)\n");
  fprintf(stderr,"%s","                  1 char* value\n");
  fprintf(stderr,"%s","        -rzwcand: The candidate number to fold from 'infile'_rzw.cand\n");
  fprintf(stderr,"%s","                  1 int value between 1 and oo\n");
  fprintf(stderr,"%s","        -rzwfile: Name of the rzw search '.cand' file to use (with suffix)\n");
  fprintf(stderr,"%s","                  1 char* value\n");
  fprintf(stderr,"%s","      -accelcand: The candidate number to fold from 'infile'_rzw.cand\n");
  fprintf(stderr,"%s","                  1 int value between 1 and oo\n");
  fprintf(stderr,"%s","      -accelfile: Name of the accel search '.cand' file to use (with suffix)\n");
  fprintf(stderr,"%s","                  1 char* value\n");
  fprintf(stderr,"%s","            -bin: Fold a binary pulsar.  Must include all of the following parameters\n");
  fprintf(stderr,"%s","             -pb: The orbital period (s)\n");
  fprintf(stderr,"%s","                  1 double value between 0 and oo\n");
  fprintf(stderr,"%s","              -x: The projected orbital semi-major axis (lt-sec)\n");
  fprintf(stderr,"%s","                  1 double value between 0 and oo\n");
  fprintf(stderr,"%s","              -e: The orbital eccentricity\n");
  fprintf(stderr,"%s","                  1 double value between 0 and 0.9999999\n");
  fprintf(stderr,"%s","                  default: `0'\n");
  fprintf(stderr,"%s","             -To: The time of periastron passage (MJD)\n");
  fprintf(stderr,"%s","                  1 double value between 0 and oo\n");
  fprintf(stderr,"%s","              -w: Longitude of periastron (deg)\n");
  fprintf(stderr,"%s","                  1 double value between 0 and 360\n");
  fprintf(stderr,"%s","           -wdot: Rate of advance of periastron (deg/yr)\n");
  fprintf(stderr,"%s","                  1 double value\n");
  fprintf(stderr,"%s","                  default: `0'\n");
  fprintf(stderr,"%s","           -mask: File containing masking information to use\n");
  fprintf(stderr,"%s","                  1 char* value\n");
  fprintf(stderr,"%s","     -ignorechan: Comma separated string (no spaces!) of channels to ignore (or file containing such string).  Ranges are specified by min:max[:step]\n");
  fprintf(stderr,"%s","                  1 char* value\n");
  fprintf(stderr,"%s","         -events: Use a event file instead of a time series (.dat) file\n");
  fprintf(stderr,"%s","           -days: Events are in days since the EPOCH in the '.inf' file (default is seconds)\n");
  fprintf(stderr,"%s","           -mjds: Events are in MJDs\n");
  fprintf(stderr,"%s","         -double: Events are in binary double precision (default is ASCII)\n");
  fprintf(stderr,"%s","         -offset: A time offset to add to the 1st event in the same units as the events\n");
  fprintf(stderr,"%s","                  1 double value\n");
  fprintf(stderr,"%s","                  default: `0'\n");
  fprintf(stderr,"%s","          infile: Input data file name.  If the data is not in a regognized raw data format, it should be a file containing a time series of single-precision floats or short ints.  In this case a '.inf' file with the same root filename must also exist (Note that this means that the input data file must have a suffix that starts with a period)\n");
  fprintf(stderr,"%s","                  1...16384 values\n");
  fprintf(stderr,"%s","  version: 04Feb23\n");
  fprintf(stderr,"%s","  ");
  exit(EXIT_FAILURE);
}
/**********************************************************************/
Cmdline *
parseCmdline(int argc, char **argv)
{
  int i;

  Program = argv[0];
  cmd.full_cmd_line = catArgv(argc, argv);
  for(i=1, cmd.argc=1; i<argc; i++) {
    if( 0==strcmp("--", argv[i]) ) {
      while( ++i<argc ) argv[cmd.argc++] = argv[i];
      continue;
    }

    if( 0==strcmp("-ncpus", argv[i]) ) {
      int keep = i;
      cmd.ncpusP = 1;
      i = getIntOpt(argc, argv, i, &cmd.ncpus, 1);
      cmd.ncpusC = i-keep;
      checkIntHigher("-ncpus", &cmd.ncpus, cmd.ncpusC, 1);
      continue;
    }

    if( 0==strcmp("-o", argv[i]) ) {
      int keep = i;
      cmd.outfileP = 1;
      i = getStringOpt(argc, argv, i, &cmd.outfile, 1);
      cmd.outfileC = i-keep;
      continue;
    }

    if( 0==strcmp("-filterbank", argv[i]) ) {
      cmd.filterbankP = 1;
      continue;
    }

    if( 0==strcmp("-psrfits", argv[i]) ) {
      cmd.psrfitsP = 1;
      continue;
    }

    if( 0==strcmp("-noweights", argv[i]) ) {
      cmd.noweightsP = 1;
      continue;
    }

    if( 0==strcmp("-noscales", argv[i]) ) {
      cmd.noscalesP = 1;
      continue;
    }

    if( 0==strcmp("-nooffsets", argv[i]) ) {
      cmd.nooffsetsP = 1;
      continue;
    }

    if( 0==strcmp("-wapp", argv[i]) ) {
      cmd.wappP = 1;
      continue;
    }

    if( 0==strcmp("-window", argv[i]) ) {
      cmd.windowP = 1;
      continue;
    }

    if( 0==strcmp("-topo", argv[i]) ) {
      cmd.topoP = 1;
      continue;
    }

    if( 0==strcmp("-invert", argv[i]) ) {
      cmd.invertP = 1;
      continue;
    }

    if( 0==strcmp("-zerodm", argv[i]) ) {
      cmd.zerodmP = 1;
      continue;
    }

    if( 0==strcmp("-absphase", argv[i]) ) {
      cmd.absphaseP = 1;
      continue;
    }

    if( 0==strcmp("-barypolycos", argv[i]) ) {
      cmd.barypolycosP = 1;
      continue;
    }

    if( 0==strcmp("-debug", argv[i]) ) {
      cmd.debugP = 1;
      continue;
    }

    if( 0==strcmp("-samples", argv[i]) ) {
      cmd.samplesP = 1;
      continue;
    }

    if( 0==strcmp("-normalize", argv[i]) ) {
      cmd.normalizeP = 1;
      continue;
    }

    if( 0==strcmp("-numwapps", argv[i]) ) {
      int keep = i;
      cmd.numwappsP = 1;
      i = getIntOpt(argc, argv, i, &cmd.numwapps, 1);
      cmd.numwappsC = i-keep;
      checkIntLower("-numwapps", &cmd.numwapps, cmd.numwappsC, 8);
      checkIntHigher("-numwapps", &cmd.numwapps, cmd.numwappsC, 1);
      continue;
    }

    if( 0==strcmp("-if", argv[i]) ) {
      int keep = i;
      cmd.ifsP = 1;
      i = getIntOpt(argc, argv, i, &cmd.ifs, 1);
      cmd.ifsC = i-keep;
      checkIntLower("-if", &cmd.ifs, cmd.ifsC, 1);
      checkIntHigher("-if", &cmd.ifs, cmd.ifsC, 0);
      continue;
    }

    if( 0==strcmp("-clip", argv[i]) ) {
      int keep = i;
      cmd.clipP = 1;
      i = getFloatOpt(argc, argv, i, &cmd.clip, 1);
      cmd.clipC = i-keep;
      checkFloatLower("-clip", &cmd.clip, cmd.clipC, 1000.0);
      checkFloatHigher("-clip", &cmd.clip, cmd.clipC, 0);
      continue;
    }

    if( 0==strcmp("-noclip", argv[i]) ) {
      cmd.noclipP = 1;
      continue;
    }

    if( 0==strcmp("-noxwin", argv[i]) ) {
      cmd.noxwinP = 1;
      continue;
    }

    if( 0==strcmp("-runavg", argv[i]) ) {
      cmd.runavgP = 1;
      continue;
    }

    if( 0==strcmp("-fine", argv[i]) ) {
      cmd.fineP = 1;
      continue;
    }

    if( 0==strcmp("-coarse", argv[i]) ) {
      cmd.coarseP = 1;
      continue;
    }

    if( 0==strcmp("-slow", argv[i]) ) {
      cmd.slowP = 1;
      continue;
    }

    if( 0==strcmp("-searchpdd", argv[i]) ) {
      cmd.searchpddP = 1;
      continue;
    }

    if( 0==strcmp("-searchfdd", argv[i]) ) {
      cmd.searchfddP = 1;
      continue;
    }

    if( 0==strcmp("-nosearch", argv[i]) ) {
      cmd.nosearchP = 1;
      continue;
    }

    if( 0==strcmp("-nopsearch", argv[i]) ) {
      cmd.nopsearchP = 1;
      continue;
    }

    if( 0==strcmp("-nopdsearch", argv[i]) ) {
      cmd.nopdsearchP = 1;
      continue;
    }

    if( 0==strcmp("-nodmsearch", argv[i]) ) {
      cmd.nodmsearchP = 1;
      continue;
    }

    if( 0==strcmp("-scaleparts", argv[i]) ) {
      cmd.scalepartsP = 1;
      continue;
    }

    if( 0==strcmp("-allgrey", argv[i]) ) {
      cmd.allgreyP = 1;
      continue;
    }

    if( 0==strcmp("-fixchi", argv[i]) ) {
      cmd.fixchiP = 1;
      continue;
    }

    if( 0==strcmp("-justprofs", argv[i]) ) {
      cmd.justprofsP = 1;
      continue;
    }

    if( 0==strcmp("-dm", argv[i]) ) {
      int keep = i;
      cmd.dmP = 1;
      i = getDoubleOpt(argc, argv, i, &cmd.dm, 1);
      cmd.dmC = i-keep;
      checkDoubleHigher("-dm", &cmd.dm, cmd.dmC, 0);
      continue;
    }

    if( 0==strcmp("-n", argv[i]) ) {
      int keep = i;
      cmd.proflenP = 1;
      i = getIntOpt(argc, argv, i, &cmd.proflen, 1);
      cmd.proflenC = i-keep;
      continue;
    }

    if( 0==strcmp("-nsub", argv[i]) ) {
      int keep = i;
      cmd.nsubP = 1;
      i = getIntOpt(argc, argv, i, &cmd.nsub, 1);
      cmd.nsubC = i-keep;
      checkIntLower("-nsub", &cmd.nsub, cmd.nsubC, 4096);
      checkIntHigher("-nsub", &cmd.nsub, cmd.nsubC, 1);
      continue;
    }

    if( 0==strcmp("-npart", argv[i]) ) {
      int keep = i;
      cmd.npartP = 1;
      i = getIntOpt(argc, argv, i, &cmd.npart, 1);
      cmd.npartC = i-keep;
      checkIntLower("-npart", &cmd.npart, cmd.npartC, 4096);
      checkIntHigher("-npart", &cmd.npart, cmd.npartC, 1);
      continue;
    }

    if( 0==strcmp("-pstep", argv[i]) ) {
      int keep = i;
      cmd.pstepP = 1;
      i = getIntOpt(argc, argv, i, &cmd.pstep, 1);
      cmd.pstepC = i-keep;
      checkIntLower("-pstep", &cmd.pstep, cmd.pstepC, 10);
      checkIntHigher("-pstep", &cmd.pstep, cmd.pstepC, 1);
      continue;
    }

    if( 0==strcmp("-pdstep", argv[i]) ) {
      int keep = i;
      cmd.pdstepP = 1;
      i = getIntOpt(argc, argv, i, &cmd.pdstep, 1);
      cmd.pdstepC = i-keep;
      checkIntLower("-pdstep", &cmd.pdstep, cmd.pdstepC, 20);
      checkIntHigher("-pdstep", &cmd.pdstep, cmd.pdstepC, 1);
      continue;
    }

    if( 0==strcmp("-dmstep", argv[i]) ) {
      int keep = i;
      cmd.dmstepP = 1;
      i = getIntOpt(argc, argv, i, &cmd.dmstep, 1);
      cmd.dmstepC = i-keep;
      checkIntLower("-dmstep", &cmd.dmstep, cmd.dmstepC, 10);
      checkIntHigher("-dmstep", &cmd.dmstep, cmd.dmstepC, 1);
      continue;
    }

    if( 0==strcmp("-npfact", argv[i]) ) {
      int keep = i;
      cmd.npfactP = 1;
      i = getIntOpt(argc, argv, i, &cmd.npfact, 1);
      cmd.npfactC = i-keep;
      checkIntLower("-npfact", &cmd.npfact, cmd.npfactC, 10);
      checkIntHigher("-npfact", &cmd.npfact, cmd.npfactC, 1);
      continue;
    }

    if( 0==strcmp("-ndmfact", argv[i]) ) {
      int keep = i;
      cmd.ndmfactP = 1;
      i = getIntOpt(argc, argv, i, &cmd.ndmfact, 1);
      cmd.ndmfactC = i-keep;
      checkIntLower("-ndmfact", &cmd.ndmfact, cmd.ndmfactC, 1000);
      checkIntHigher("-ndmfact", &cmd.ndmfact, cmd.ndmfactC, 1);
      continue;
    }

    if( 0==strcmp("-p", argv[i]) ) {
      int keep = i;
      cmd.pP = 1;
      i = getDoubleOpt(argc, argv, i, &cmd.p, 1);
      cmd.pC = i-keep;
      checkDoubleHigher("-p", &cmd.p, cmd.pC, 0);
      continue;
    }

    if( 0==strcmp("-pd", argv[i]) ) {
      int keep = i;
      cmd.pdP = 1;
      i = getDoubleOpt(argc, argv, i, &cmd.pd, 1);
      cmd.pdC = i-keep;
      continue;
    }

    if( 0==strcmp("-pdd", argv[i]) ) {
      int keep = i;
      cmd.pddP = 1;
      i = getDoubleOpt(argc, argv, i, &cmd.pdd, 1);
      cmd.pddC = i-keep;
      continue;
    }

    if( 0==strcmp("-f", argv[i]) ) {
      int keep = i;
      cmd.fP = 1;
      i = getDoubleOpt(argc, argv, i, &cmd.f, 1);
      cmd.fC = i-keep;
      checkDoubleHigher("-f", &cmd.f, cmd.fC, 0);
      continue;
    }

    if( 0==strcmp("-fd", argv[i]) ) {
      int keep = i;
      cmd.fdP = 1;
      i = getDoubleOpt(argc, argv, i, &cmd.fd, 1);
      cmd.fdC = i-keep;
      continue;
    }

    if( 0==strcmp("-fdd", argv[i]) ) {
      int keep = i;
      cmd.fddP = 1;
      i = getDoubleOpt(argc, argv, i, &cmd.fdd, 1);
      cmd.fddC = i-keep;
      continue;
    }

    if( 0==strcmp("-pfact", argv[i]) ) {
      int keep = i;
      cmd.pfactP = 1;
      i = getDoubleOpt(argc, argv, i, &cmd.pfact, 1);
      cmd.pfactC = i-keep;
      checkDoubleLower("-pfact", &cmd.pfact, cmd.pfactC, 100.0);
      checkDoubleHigher("-pfact", &cmd.pfact, cmd.pfactC, 0.0);
      continue;
    }

    if( 0==strcmp("-ffact", argv[i]) ) {
      int keep = i;
      cmd.ffactP = 1;
      i = getDoubleOpt(argc, argv, i, &cmd.ffact, 1);
      cmd.ffactC = i-keep;
      checkDoubleLower("-ffact", &cmd.ffact, cmd.ffactC, 100.0);
      checkDoubleHigher("-ffact", &cmd.ffact, cmd.ffactC, 0.0);
      continue;
    }

    if( 0==strcmp("-phs", argv[i]) ) {
      int keep = i;
      cmd.phsP = 1;
      i = getDoubleOpt(argc, argv, i, &cmd.phs, 1);
      cmd.phsC = i-keep;
      checkDoubleLower("-phs", &cmd.phs, cmd.phsC, 1.0);
      checkDoubleHigher("-phs", &cmd.phs, cmd.phsC, 0.0);
      continue;
    }

    if( 0==strcmp("-start", argv[i]) ) {
      int keep = i;
      cmd.startTP = 1;
      i = getDoubleOpt(argc, argv, i, &cmd.startT, 1);
      cmd.startTC = i-keep;
      checkDoubleLower("-start", &cmd.startT, cmd.startTC, 1.0);
      checkDoubleHigher("-start", &cmd.startT, cmd.startTC, 0.0);
      continue;
    }

    if( 0==strcmp("-end", argv[i]) ) {
      int keep = i;
      cmd.endTP = 1;
      i = getDoubleOpt(argc, argv, i, &cmd.endT, 1);
      cmd.endTC = i-keep;
      checkDoubleLower("-end", &cmd.endT, cmd.endTC, 1.0);
      checkDoubleHigher("-end", &cmd.endT, cmd.endTC, 0.0);
      continue;
    }

    if( 0==strcmp("-psr", argv[i]) ) {
      int keep = i;
      cmd.psrnameP = 1;
      i = getStringOpt(argc, argv, i, &cmd.psrname, 1);
      cmd.psrnameC = i-keep;
      continue;
    }

    if( 0==strcmp("-par", argv[i]) ) {
      int keep = i;
      cmd.parnameP = 1;
      i = getStringOpt(argc, argv, i, &cmd.parname, 1);
      cmd.parnameC = i-keep;
      continue;
    }

    if( 0==strcmp("-polycos", argv[i]) ) {
      int keep = i;
      cmd.polycofileP = 1;
      i = getStringOpt(argc, argv, i, &cmd.polycofile, 1);
      cmd.polycofileC = i-keep;
      continue;
    }

    if( 0==strcmp("-timing", argv[i]) ) {
      int keep = i;
      cmd.timingP = 1;
      i = getStringOpt(argc, argv, i, &cmd.timing, 1);
      cmd.timingC = i-keep;
      continue;
    }

    if( 0==strcmp("-rzwcand", argv[i]) ) {
      int keep = i;
      cmd.rzwcandP = 1;
      i = getIntOpt(argc, argv, i, &cmd.rzwcand, 1);
      cmd.rzwcandC = i-keep;
      checkIntHigher("-rzwcand", &cmd.rzwcand, cmd.rzwcandC, 1);
      continue;
    }

    if( 0==strcmp("-rzwfile", argv[i]) ) {
      int keep = i;
      cmd.rzwfileP = 1;
      i = getStringOpt(argc, argv, i, &cmd.rzwfile, 1);
      cmd.rzwfileC = i-keep;
      continue;
    }

    if( 0==strcmp("-accelcand", argv[i]) ) {
      int keep = i;
      cmd.accelcandP = 1;
      i = getIntOpt(argc, argv, i, &cmd.accelcand, 1);
      cmd.accelcandC = i-keep;
      checkIntHigher("-accelcand", &cmd.accelcand, cmd.accelcandC, 1);
      continue;
    }

    if( 0==strcmp("-accelfile", argv[i]) ) {
      int keep = i;
      cmd.accelfileP = 1;
      i = getStringOpt(argc, argv, i, &cmd.accelfile, 1);
      cmd.accelfileC = i-keep;
      continue;
    }

    if( 0==strcmp("-bin", argv[i]) ) {
      cmd.binaryP = 1;
      continue;
    }

    if( 0==strcmp("-pb", argv[i]) ) {
      int keep = i;
      cmd.pbP = 1;
      i = getDoubleOpt(argc, argv, i, &cmd.pb, 1);
      cmd.pbC = i-keep;
      checkDoubleHigher("-pb", &cmd.pb, cmd.pbC, 0);
      continue;
    }

    if( 0==strcmp("-x", argv[i]) ) {
      int keep = i;
      cmd.asinicP = 1;
      i = getDoubleOpt(argc, argv, i, &cmd.asinic, 1);
      cmd.asinicC = i-keep;
      checkDoubleHigher("-x", &cmd.asinic, cmd.asinicC, 0);
      continue;
    }

    if( 0==strcmp("-e", argv[i]) ) {
      int keep = i;
      cmd.eP = 1;
      i = getDoubleOpt(argc, argv, i, &cmd.e, 1);
      cmd.eC = i-keep;
      checkDoubleLower("-e", &cmd.e, cmd.eC, 0.9999999);
      checkDoubleHigher("-e", &cmd.e, cmd.eC, 0);
      continue;
    }

    if( 0==strcmp("-To", argv[i]) ) {
      int keep = i;
      cmd.ToP = 1;
      i = getDoubleOpt(argc, argv, i, &cmd.To, 1);
      cmd.ToC = i-keep;
      checkDoubleHigher("-To", &cmd.To, cmd.ToC, 0);
      continue;
    }

    if( 0==strcmp("-w", argv[i]) ) {
      int keep = i;
      cmd.wP = 1;
      i = getDoubleOpt(argc, argv, i, &cmd.w, 1);
      cmd.wC = i-keep;
      checkDoubleLower("-w", &cmd.w, cmd.wC, 360);
      checkDoubleHigher("-w", &cmd.w, cmd.wC, 0);
      continue;
    }

    if( 0==strcmp("-wdot", argv[i]) ) {
      int keep = i;
      cmd.wdotP = 1;
      i = getDoubleOpt(argc, argv, i, &cmd.wdot, 1);
      cmd.wdotC = i-keep;
      continue;
    }

    if( 0==strcmp("-mask", argv[i]) ) {
      int keep = i;
      cmd.maskfileP = 1;
      i = getStringOpt(argc, argv, i, &cmd.maskfile, 1);
      cmd.maskfileC = i-keep;
      continue;
    }

    if( 0==strcmp("-ignorechan", argv[i]) ) {
      int keep = i;
      cmd.ignorechanstrP = 1;
      i = getStringOpt(argc, argv, i, &cmd.ignorechanstr, 1);
      cmd.ignorechanstrC = i-keep;
      continue;
    }

    if( 0==strcmp("-events", argv[i]) ) {
      cmd.eventsP = 1;
      continue;
    }

    if( 0==strcmp("-days", argv[i]) ) {
      cmd.daysP = 1;
      continue;
    }

    if( 0==strcmp("-mjds", argv[i]) ) {
      cmd.mjdsP = 1;
      continue;
    }

    if( 0==strcmp("-double", argv[i]) ) {
      cmd.doubleP = 1;
      continue;
    }

    if( 0==strcmp("-offset", argv[i]) ) {
      int keep = i;
      cmd.offsetP = 1;
      i = getDoubleOpt(argc, argv, i, &cmd.offset, 1);
      cmd.offsetC = i-keep;
      continue;
    }

    if( argv[i][0]=='-' ) {
      fprintf(stderr, "\n%s: unknown option `%s'\n\n",
              Program, argv[i]);
      usage();
    }
    argv[cmd.argc++] = argv[i];
  }/* for i */


  /*@-mustfree*/
  cmd.argv = argv+1;
  /*@=mustfree*/
  cmd.argc -= 1;

  if( 1>cmd.argc ) {
    fprintf(stderr, "%s: there should be at least 1 non-option argument(s)\n",
            Program);
    exit(EXIT_FAILURE);
  }
  if( 16384<cmd.argc ) {
    fprintf(stderr, "%s: there should be at most 16384 non-option argument(s)\n",
            Program);
    exit(EXIT_FAILURE);
  }
  /*@-compmempass*/  return &cmd;
}

