/*****
  command line parser -- generated by clig
  (http://wsd.iitb.fhg.de/~kir/clighome/)

  The command line parser `clig':
  (C) 1995-2004 <PERSON> (<EMAIL>)
*****/

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <ctype.h>
#include <errno.h>
#include <limits.h>
#include <float.h>
#include <math.h>

#include "prepsubband_cmd.h"

char *Program;

/*@-null*/

static Cmdline cmd = {
  /***** -ncpus: Number of processors to use with OpenMP */
  /* ncpusP = */ 1,
  /* ncpus = */ 1,
  /* ncpusC = */ 1,
  /***** -o: Root of the output file names */
  /* outfileP = */ 0,
  /* outfile = */ (char*)0,
  /* outfileC = */ 0,
  /***** -filterbank: Raw data in SIGPROC filterbank format */
  /* filterbankP = */ 0,
  /***** -psrfits: Raw data in PSRFITS format */
  /* psrfitsP = */ 0,
  /***** -noweights: Do not apply PSRFITS weights */
  /* noweightsP = */ 0,
  /***** -noscales: Do not apply PSRFITS scales */
  /* noscalesP = */ 0,
  /***** -nooffsets: Do not apply PSRFITS offsets */
  /* nooffsetsP = */ 0,
  /***** -wapp: Raw data in Wideband Arecibo Pulsar Processor (WAPP) format */
  /* wappP = */ 0,
  /***** -window: Window correlator lags with a Hamming window before FFTing */
  /* windowP = */ 0,
  /***** -numwapps: Number of WAPPs used with contiguous frequencies */
  /* numwappsP = */ 1,
  /* numwapps = */ 1,
  /* numwappsC = */ 1,
  /***** -if: A specific IF to use if available (summed IFs is the default) */
  /* ifsP = */ 0,
  /* ifs = */ (int)0,
  /* ifsC = */ 0,
  /***** -clip: Time-domain sigma to use for clipping (0.0 = no clipping, 6.0 = default */
  /* clipP = */ 1,
  /* clip = */ 6.0,
  /* clipC = */ 1,
  /***** -noclip: Do not clip the data.  (The default is to _always_ clip!) */
  /* noclipP = */ 0,
  /***** -invert: For rawdata, flip (or invert) the band */
  /* invertP = */ 0,
  /***** -zerodm: Subtract the mean of all channels from each sample (i.e. remove zero DM) */
  /* zerodmP = */ 0,
  /***** -runavg: Running mean subtraction from the input data */
  /* runavgP = */ 0,
  /***** -sub: Write subbands instead of de-dispersed data */
  /* subP = */ 0,
  /***** -subdm: The DM to use when de-dispersing subbands for -sub */
  /* subdmP = */ 1,
  /* subdm = */ 0.0,
  /* subdmC = */ 1,
  /***** -numout: Output this many values.  If there are not enough values in the original data file, will pad the output file with the average value */
  /* numoutP = */ 0,
  /* numout = */ (long)0,
  /* numoutC = */ 0,
  /***** -nobary: Do not barycenter the data */
  /* nobaryP = */ 0,
  /***** -offset: Number of spectra to offset into as starting data point */
  /* offsetP = */ 1,
  /* offset = */ 0,
  /* offsetC = */ 1,
  /***** -start: Starting point of the processing as a fraction of the full obs */
  /* startP = */ 1,
  /* start = */ 0.0,
  /* startC = */ 1,
  /***** -lodm: The lowest dispersion measure to de-disperse (cm^-3 pc) */
  /* lodmP = */ 1,
  /* lodm = */ 0,
  /* lodmC = */ 1,
  /***** -dmstep: The stepsize in dispersion measure to use(cm^-3 pc) */
  /* dmstepP = */ 1,
  /* dmstep = */ 1.0,
  /* dmstepC = */ 1,
  /***** -numdms: The number of DMs to de-disperse */
  /* numdmsP = */ 1,
  /* numdms = */ 10,
  /* numdmsC = */ 1,
  /***** -nsub: The number of sub-bands to use */
  /* nsubP = */ 1,
  /* nsub = */ 32,
  /* nsubC = */ 1,
  /***** -downsamp: The number of neighboring bins to co-add */
  /* downsampP = */ 1,
  /* downsamp = */ 1,
  /* downsampC = */ 1,
  /***** -dmprec: The number of decimals in the precision of the DM in the filename. */
  /* dmprecP = */ 1,
  /* dmprec = */ 2,
  /* dmprecC = */ 1,
  /***** -mask: File containing masking information to use */
  /* maskfileP = */ 0,
  /* maskfile = */ (char*)0,
  /* maskfileC = */ 0,
  /***** -ignorechan: Comma separated string (no spaces!) of channels to ignore (or file containing such string).  Ranges are specified by min:max[:step] */
  /* ignorechanstrP = */ 0,
  /* ignorechanstr = */ (char*)0,
  /* ignorechanstrC = */ 0,
  /***** uninterpreted rest of command line */
  /* argc = */ 0,
  /* argv = */ (char**)0,
  /***** the original command line concatenated */
  /* full_cmd_line = */ NULL
};

/*@=null*/

/***** let LCLint run more smoothly */
/*@-predboolothers*/
/*@-boolops*/


/******************************************************************/
/*****
 This is a bit tricky. We want to make a difference between overflow
 and underflow and we want to allow v==Inf or v==-Inf but not
 v>FLT_MAX. 

 We don't use fabs to avoid linkage with -lm.
*****/
static void
checkFloatConversion(double v, char *option, char *arg)
{
  char *err = NULL;

  if( (errno==ERANGE && v!=0.0) /* even double overflowed */
      || (v<HUGE_VAL && v>-HUGE_VAL && (v<0.0?-v:v)>(double)FLT_MAX) ) {
    err = "large";
  } else if( (errno==ERANGE && v==0.0) 
	     || (v!=0.0 && (v<0.0?-v:v)<(double)FLT_MIN) ) {
    err = "small";
  }
  if( err ) {
    fprintf(stderr, 
	    "%s: parameter `%s' of option `%s' to %s to represent\n",
	    Program, arg, option, err);
    exit(EXIT_FAILURE);
  }
}

int
getIntOpt(int argc, char **argv, int i, int *value, int force)
{
  char *end;
  long v;

  if( ++i>=argc ) goto nothingFound;

  errno = 0;
  v = strtol(argv[i], &end, 0);

  /***** check for conversion error */
  if( end==argv[i] ) goto nothingFound;

  /***** check for surplus non-whitespace */
  while( isspace((int) *end) ) end+=1;
  if( *end ) goto nothingFound;

  /***** check if it fits into an int */
  if( errno==ERANGE || v>(long)INT_MAX || v<(long)INT_MIN ) {
    fprintf(stderr, 
	    "%s: parameter `%s' of option `%s' to large to represent\n",
	    Program, argv[i], argv[i-1]);
    exit(EXIT_FAILURE);
  }
  *value = (int)v;

  return i;

nothingFound:
  if( !force ) return i-1;

  fprintf(stderr, 
	  "%s: missing or malformed integer value after option `%s'\n",
	  Program, argv[i-1]);
    exit(EXIT_FAILURE);
}
/**********************************************************************/

int
getIntOpts(int argc, char **argv, int i, 
	   int **values,
	   int cmin, int cmax)
/*****
  We want to find at least cmin values and at most cmax values.
  cmax==-1 then means infinitely many are allowed.
*****/
{
  int alloced, used;
  char *end;
  long v;
  if( i+cmin >= argc ) {
    fprintf(stderr, 
	    "%s: option `%s' wants at least %d parameters\n",
	    Program, argv[i], cmin);
    exit(EXIT_FAILURE);
  }

  /***** 
    alloc a bit more than cmin values. It does not hurt to have room
    for a bit more values than cmax.
  *****/
  alloced = cmin + 4;
  *values = (int*)calloc((size_t)alloced, sizeof(int));
  if( ! *values ) {
outMem:
    fprintf(stderr, 
	    "%s: out of memory while parsing option `%s'\n",
	    Program, argv[i]);
    exit(EXIT_FAILURE);
  }

  for(used=0; (cmax==-1 || used<cmax) && used+i+1<argc; used++) {
    if( used==alloced ) {
      alloced += 8;
      *values = (int *) realloc(*values, alloced*sizeof(int));
      if( !*values ) goto outMem;
    }

    errno = 0;
    v = strtol(argv[used+i+1], &end, 0);

    /***** check for conversion error */
    if( end==argv[used+i+1] ) break;

    /***** check for surplus non-whitespace */
    while( isspace((int) *end) ) end+=1;
    if( *end ) break;

    /***** check for overflow */
    if( errno==ERANGE || v>(long)INT_MAX || v<(long)INT_MIN ) {
      fprintf(stderr, 
	      "%s: parameter `%s' of option `%s' to large to represent\n",
	      Program, argv[i+used+1], argv[i]);
      exit(EXIT_FAILURE);
    }

    (*values)[used] = (int)v;

  }
    
  if( used<cmin ) {
    fprintf(stderr, 
	    "%s: parameter `%s' of `%s' should be an "
	    "integer value\n",
	    Program, argv[i+used+1], argv[i]);
    exit(EXIT_FAILURE);
  }

  return i+used;
}
/**********************************************************************/

int
getLongOpt(int argc, char **argv, int i, long *value, int force)
{
  char *end;

  if( ++i>=argc ) goto nothingFound;

  errno = 0;
  *value = strtol(argv[i], &end, 0);

  /***** check for conversion error */
  if( end==argv[i] ) goto nothingFound;

  /***** check for surplus non-whitespace */
  while( isspace((int) *end) ) end+=1;
  if( *end ) goto nothingFound;

  /***** check for overflow */
  if( errno==ERANGE ) {
    fprintf(stderr, 
	    "%s: parameter `%s' of option `%s' to large to represent\n",
	    Program, argv[i], argv[i-1]);
    exit(EXIT_FAILURE);
  }
  return i;

nothingFound:
  /***** !force means: this parameter may be missing.*/
  if( !force ) return i-1;

  fprintf(stderr, 
	  "%s: missing or malformed value after option `%s'\n",
	  Program, argv[i-1]);
    exit(EXIT_FAILURE);
}
/**********************************************************************/

int
getLongOpts(int argc, char **argv, int i, 
	    long **values,
	    int cmin, int cmax)
/*****
  We want to find at least cmin values and at most cmax values.
  cmax==-1 then means infinitely many are allowed.
*****/
{
  int alloced, used;
  char *end;

  if( i+cmin >= argc ) {
    fprintf(stderr, 
	    "%s: option `%s' wants at least %d parameters\n",
	    Program, argv[i], cmin);
    exit(EXIT_FAILURE);
  }

  /***** 
    alloc a bit more than cmin values. It does not hurt to have room
    for a bit more values than cmax.
  *****/
  alloced = cmin + 4;
  *values = (long int *)calloc((size_t)alloced, sizeof(long));
  if( ! *values ) {
outMem:
    fprintf(stderr, 
	    "%s: out of memory while parsing option `%s'\n",
	    Program, argv[i]);
    exit(EXIT_FAILURE);
  }

  for(used=0; (cmax==-1 || used<cmax) && used+i+1<argc; used++) {
    if( used==alloced ) {
      alloced += 8;
      *values = (long int*) realloc(*values, alloced*sizeof(long));
      if( !*values ) goto outMem;
    }

    errno = 0;
    (*values)[used] = strtol(argv[used+i+1], &end, 0);

    /***** check for conversion error */
    if( end==argv[used+i+1] ) break;

    /***** check for surplus non-whitespace */
    while( isspace((int) *end) ) end+=1; 
    if( *end ) break;

    /***** check for overflow */
    if( errno==ERANGE ) {
      fprintf(stderr, 
	      "%s: parameter `%s' of option `%s' to large to represent\n",
	      Program, argv[i+used+1], argv[i]);
      exit(EXIT_FAILURE);
    }

  }
    
  if( used<cmin ) {
    fprintf(stderr, 
	    "%s: parameter `%s' of `%s' should be an "
	    "integer value\n",
	    Program, argv[i+used+1], argv[i]);
    exit(EXIT_FAILURE);
  }

  return i+used;
}
/**********************************************************************/

int
getFloatOpt(int argc, char **argv, int i, float *value, int force)
{
  char *end;
  double v;

  if( ++i>=argc ) goto nothingFound;

  errno = 0;
  v = strtod(argv[i], &end);

  /***** check for conversion error */
  if( end==argv[i] ) goto nothingFound;

  /***** check for surplus non-whitespace */
  while( isspace((int) *end) ) end+=1;
  if( *end ) goto nothingFound;

  /***** check for overflow */
  checkFloatConversion(v, argv[i-1], argv[i]);

  *value = (float)v;

  return i;

nothingFound:
  if( !force ) return i-1;

  fprintf(stderr,
	  "%s: missing or malformed float value after option `%s'\n",
	  Program, argv[i-1]);
  exit(EXIT_FAILURE);
 
}
/**********************************************************************/

int
getFloatOpts(int argc, char **argv, int i, 
	   float **values,
	   int cmin, int cmax)
/*****
  We want to find at least cmin values and at most cmax values.
  cmax==-1 then means infinitely many are allowed.
*****/
{
  int alloced, used;
  char *end;
  double v;

  if( i+cmin >= argc ) {
    fprintf(stderr, 
	    "%s: option `%s' wants at least %d parameters\n",
	    Program, argv[i], cmin);
    exit(EXIT_FAILURE);
  }

  /***** 
    alloc a bit more than cmin values.
  *****/
  alloced = cmin + 4;
  *values = (float*)calloc((size_t)alloced, sizeof(float));
  if( ! *values ) {
outMem:
    fprintf(stderr, 
	    "%s: out of memory while parsing option `%s'\n",
	    Program, argv[i]);
    exit(EXIT_FAILURE);
  }

  for(used=0; (cmax==-1 || used<cmax) && used+i+1<argc; used++) {
    if( used==alloced ) {
      alloced += 8;
      *values = (float *) realloc(*values, alloced*sizeof(float));
      if( !*values ) goto outMem;
    }

    errno = 0;
    v = strtod(argv[used+i+1], &end);

    /***** check for conversion error */
    if( end==argv[used+i+1] ) break;

    /***** check for surplus non-whitespace */
    while( isspace((int) *end) ) end+=1;
    if( *end ) break;

    /***** check for overflow */
    checkFloatConversion(v, argv[i], argv[i+used+1]);
    
    (*values)[used] = (float)v;
  }
    
  if( used<cmin ) {
    fprintf(stderr, 
	    "%s: parameter `%s' of `%s' should be a "
	    "floating-point value\n",
	    Program, argv[i+used+1], argv[i]);
    exit(EXIT_FAILURE);
  }

  return i+used;
}
/**********************************************************************/

int
getDoubleOpt(int argc, char **argv, int i, double *value, int force)
{
  char *end;

  if( ++i>=argc ) goto nothingFound;

  errno = 0;
  *value = strtod(argv[i], &end);

  /***** check for conversion error */
  if( end==argv[i] ) goto nothingFound;

  /***** check for surplus non-whitespace */
  while( isspace((int) *end) ) end+=1;
  if( *end ) goto nothingFound;

  /***** check for overflow */
  if( errno==ERANGE ) {
    fprintf(stderr, 
	    "%s: parameter `%s' of option `%s' to %s to represent\n",
	    Program, argv[i], argv[i-1],
	    (*value==0.0 ? "small" : "large"));
    exit(EXIT_FAILURE);
  }

  return i;

nothingFound:
  if( !force ) return i-1;

  fprintf(stderr,
	  "%s: missing or malformed value after option `%s'\n",
	  Program, argv[i-1]);
  exit(EXIT_FAILURE);
 
}
/**********************************************************************/

int
getDoubleOpts(int argc, char **argv, int i, 
	   double **values,
	   int cmin, int cmax)
/*****
  We want to find at least cmin values and at most cmax values.
  cmax==-1 then means infinitely many are allowed.
*****/
{
  int alloced, used;
  char *end;

  if( i+cmin >= argc ) {
    fprintf(stderr, 
	    "%s: option `%s' wants at least %d parameters\n",
	    Program, argv[i], cmin);
    exit(EXIT_FAILURE);
  }

  /***** 
    alloc a bit more than cmin values.
  *****/
  alloced = cmin + 4;
  *values = (double*)calloc((size_t)alloced, sizeof(double));
  if( ! *values ) {
outMem:
    fprintf(stderr, 
	    "%s: out of memory while parsing option `%s'\n",
	    Program, argv[i]);
    exit(EXIT_FAILURE);
  }

  for(used=0; (cmax==-1 || used<cmax) && used+i+1<argc; used++) {
    if( used==alloced ) {
      alloced += 8;
      *values = (double *) realloc(*values, alloced*sizeof(double));
      if( !*values ) goto outMem;
    }

    errno = 0;
    (*values)[used] = strtod(argv[used+i+1], &end);

    /***** check for conversion error */
    if( end==argv[used+i+1] ) break;

    /***** check for surplus non-whitespace */
    while( isspace((int) *end) ) end+=1;
    if( *end ) break;

    /***** check for overflow */
    if( errno==ERANGE ) {
      fprintf(stderr, 
	      "%s: parameter `%s' of option `%s' to %s to represent\n",
	      Program, argv[i+used+1], argv[i],
	      ((*values)[used]==0.0 ? "small" : "large"));
      exit(EXIT_FAILURE);
    }

  }
    
  if( used<cmin ) {
    fprintf(stderr, 
	    "%s: parameter `%s' of `%s' should be a "
	    "double value\n",
	    Program, argv[i+used+1], argv[i]);
    exit(EXIT_FAILURE);
  }

  return i+used;
}
/**********************************************************************/

/**
  force will be set if we need at least one argument for the option.
*****/
int
getStringOpt(int argc, char **argv, int i, char **value, int force)
{
  i += 1;
  if( i>=argc ) {
    if( force ) {
      fprintf(stderr, "%s: missing string after option `%s'\n",
	      Program, argv[i-1]);
      exit(EXIT_FAILURE);
    } 
    return i-1;
  }
  
  if( !force && argv[i][0] == '-' ) return i-1;
  *value = argv[i];
  return i;
}
/**********************************************************************/

int
getStringOpts(int argc, char **argv, int i, 
	   char*  **values,
	   int cmin, int cmax)
/*****
  We want to find at least cmin values and at most cmax values.
  cmax==-1 then means infinitely many are allowed.
*****/
{
  int alloced, used;

  if( i+cmin >= argc ) {
    fprintf(stderr, 
	    "%s: option `%s' wants at least %d parameters\n",
	    Program, argv[i], cmin);
    exit(EXIT_FAILURE);
  }

  alloced = cmin + 4;
    
  *values = (char**)calloc((size_t)alloced, sizeof(char*));
  if( ! *values ) {
outMem:
    fprintf(stderr, 
	    "%s: out of memory during parsing of option `%s'\n",
	    Program, argv[i]);
    exit(EXIT_FAILURE);
  }

  for(used=0; (cmax==-1 || used<cmax) && used+i+1<argc; used++) {
    if( used==alloced ) {
      alloced += 8;
      *values = (char **)realloc(*values, alloced*sizeof(char*));
      if( !*values ) goto outMem;
    }

    if( used>=cmin && argv[used+i+1][0]=='-' ) break;
    (*values)[used] = argv[used+i+1];
  }
    
  if( used<cmin ) {
    fprintf(stderr, 
    "%s: less than %d parameters for option `%s', only %d found\n",
	    Program, cmin, argv[i], used);
    exit(EXIT_FAILURE);
  }

  return i+used;
}
/**********************************************************************/

void
checkIntLower(char *opt, int *values, int count, int max)
{
  int i;

  for(i=0; i<count; i++) {
    if( values[i]<=max ) continue;
    fprintf(stderr, 
	    "%s: parameter %d of option `%s' greater than max=%d\n",
	    Program, i+1, opt, max);
    exit(EXIT_FAILURE);
  }
}
/**********************************************************************/

void
checkIntHigher(char *opt, int *values, int count, int min)
{
  int i;

  for(i=0; i<count; i++) {
    if( values[i]>=min ) continue;
    fprintf(stderr, 
	    "%s: parameter %d of option `%s' smaller than min=%d\n",
	    Program, i+1, opt, min);
    exit(EXIT_FAILURE);
  }
}
/**********************************************************************/

void
checkLongLower(char *opt, long *values, int count, long max)
{
  int i;

  for(i=0; i<count; i++) {
    if( values[i]<=max ) continue;
    fprintf(stderr, 
	    "%s: parameter %d of option `%s' greater than max=%ld\n",
	    Program, i+1, opt, max);
    exit(EXIT_FAILURE);
  }
}
/**********************************************************************/

void
checkLongHigher(char *opt, long *values, int count, long min)
{
  int i;

  for(i=0; i<count; i++) {
    if( values[i]>=min ) continue;
    fprintf(stderr, 
	    "%s: parameter %d of option `%s' smaller than min=%ld\n",
	    Program, i+1, opt, min);
    exit(EXIT_FAILURE);
  }
}
/**********************************************************************/

void
checkFloatLower(char *opt, float *values, int count, float max)
{
  int i;

  for(i=0; i<count; i++) {
    if( values[i]<=max ) continue;
    fprintf(stderr, 
	    "%s: parameter %d of option `%s' greater than max=%f\n",
	    Program, i+1, opt, max);
    exit(EXIT_FAILURE);
  }
}
/**********************************************************************/

void
checkFloatHigher(char *opt, float *values, int count, float min)
{
  int i;

  for(i=0; i<count; i++) {
    if( values[i]>=min ) continue;
    fprintf(stderr, 
	    "%s: parameter %d of option `%s' smaller than min=%f\n",
	    Program, i+1, opt, min);
    exit(EXIT_FAILURE);
  }
}
/**********************************************************************/

void
checkDoubleLower(char *opt, double *values, int count, double max)
{
  int i;

  for(i=0; i<count; i++) {
    if( values[i]<=max ) continue;
    fprintf(stderr, 
	    "%s: parameter %d of option `%s' greater than max=%f\n",
	    Program, i+1, opt, max);
    exit(EXIT_FAILURE);
  }
}
/**********************************************************************/

void
checkDoubleHigher(char *opt, double *values, int count, double min)
{
  int i;

  for(i=0; i<count; i++) {
    if( values[i]>=min ) continue;
    fprintf(stderr, 
	    "%s: parameter %d of option `%s' smaller than min=%f\n",
	    Program, i+1, opt, min);
    exit(EXIT_FAILURE);
  }
}
/**********************************************************************/

static void
missingErr(char *opt)
{
  fprintf(stderr, "%s: mandatory option `%s' missing\n",
	  Program, opt);
}
/**********************************************************************/

static char *
catArgv(int argc, char **argv)
{
  int i;
  size_t l;
  char *s, *t;

  for(i=0, l=0; i<argc; i++) l += (1+strlen(argv[i]));
  s = (char *)malloc(l);
  if( !s ) {
    fprintf(stderr, "%s: out of memory\n", Program);
    exit(EXIT_FAILURE);
  }
  strcpy(s, argv[0]);
  t = s;
  for(i=1; i<argc; i++) {
    t = t+strlen(t);
    *t++ = ' ';
    strcpy(t, argv[i]);
  }
  return s;
}
/**********************************************************************/

void
showOptionValues(void)
{
  int i;

  printf("Full command line is:\n`%s'\n", cmd.full_cmd_line);

  /***** -ncpus: Number of processors to use with OpenMP */
  if( !cmd.ncpusP ) {
    printf("-ncpus not found.\n");
  } else {
    printf("-ncpus found:\n");
    if( !cmd.ncpusC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%d'\n", cmd.ncpus);
    }
  }

  /***** -o: Root of the output file names */
  if( !cmd.outfileP ) {
    printf("-o not found.\n");
  } else {
    printf("-o found:\n");
    if( !cmd.outfileC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%s'\n", cmd.outfile);
    }
  }

  /***** -filterbank: Raw data in SIGPROC filterbank format */
  if( !cmd.filterbankP ) {
    printf("-filterbank not found.\n");
  } else {
    printf("-filterbank found:\n");
  }

  /***** -psrfits: Raw data in PSRFITS format */
  if( !cmd.psrfitsP ) {
    printf("-psrfits not found.\n");
  } else {
    printf("-psrfits found:\n");
  }

  /***** -noweights: Do not apply PSRFITS weights */
  if( !cmd.noweightsP ) {
    printf("-noweights not found.\n");
  } else {
    printf("-noweights found:\n");
  }

  /***** -noscales: Do not apply PSRFITS scales */
  if( !cmd.noscalesP ) {
    printf("-noscales not found.\n");
  } else {
    printf("-noscales found:\n");
  }

  /***** -nooffsets: Do not apply PSRFITS offsets */
  if( !cmd.nooffsetsP ) {
    printf("-nooffsets not found.\n");
  } else {
    printf("-nooffsets found:\n");
  }

  /***** -wapp: Raw data in Wideband Arecibo Pulsar Processor (WAPP) format */
  if( !cmd.wappP ) {
    printf("-wapp not found.\n");
  } else {
    printf("-wapp found:\n");
  }

  /***** -window: Window correlator lags with a Hamming window before FFTing */
  if( !cmd.windowP ) {
    printf("-window not found.\n");
  } else {
    printf("-window found:\n");
  }

  /***** -numwapps: Number of WAPPs used with contiguous frequencies */
  if( !cmd.numwappsP ) {
    printf("-numwapps not found.\n");
  } else {
    printf("-numwapps found:\n");
    if( !cmd.numwappsC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%d'\n", cmd.numwapps);
    }
  }

  /***** -if: A specific IF to use if available (summed IFs is the default) */
  if( !cmd.ifsP ) {
    printf("-if not found.\n");
  } else {
    printf("-if found:\n");
    if( !cmd.ifsC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%d'\n", cmd.ifs);
    }
  }

  /***** -clip: Time-domain sigma to use for clipping (0.0 = no clipping, 6.0 = default */
  if( !cmd.clipP ) {
    printf("-clip not found.\n");
  } else {
    printf("-clip found:\n");
    if( !cmd.clipC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%.40g'\n", cmd.clip);
    }
  }

  /***** -noclip: Do not clip the data.  (The default is to _always_ clip!) */
  if( !cmd.noclipP ) {
    printf("-noclip not found.\n");
  } else {
    printf("-noclip found:\n");
  }

  /***** -invert: For rawdata, flip (or invert) the band */
  if( !cmd.invertP ) {
    printf("-invert not found.\n");
  } else {
    printf("-invert found:\n");
  }

  /***** -zerodm: Subtract the mean of all channels from each sample (i.e. remove zero DM) */
  if( !cmd.zerodmP ) {
    printf("-zerodm not found.\n");
  } else {
    printf("-zerodm found:\n");
  }

  /***** -runavg: Running mean subtraction from the input data */
  if( !cmd.runavgP ) {
    printf("-runavg not found.\n");
  } else {
    printf("-runavg found:\n");
  }

  /***** -sub: Write subbands instead of de-dispersed data */
  if( !cmd.subP ) {
    printf("-sub not found.\n");
  } else {
    printf("-sub found:\n");
  }

  /***** -subdm: The DM to use when de-dispersing subbands for -sub */
  if( !cmd.subdmP ) {
    printf("-subdm not found.\n");
  } else {
    printf("-subdm found:\n");
    if( !cmd.subdmC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%.40g'\n", cmd.subdm);
    }
  }

  /***** -numout: Output this many values.  If there are not enough values in the original data file, will pad the output file with the average value */
  if( !cmd.numoutP ) {
    printf("-numout not found.\n");
  } else {
    printf("-numout found:\n");
    if( !cmd.numoutC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%ld'\n", cmd.numout);
    }
  }

  /***** -nobary: Do not barycenter the data */
  if( !cmd.nobaryP ) {
    printf("-nobary not found.\n");
  } else {
    printf("-nobary found:\n");
  }

  /***** -offset: Number of spectra to offset into as starting data point */
  if( !cmd.offsetP ) {
    printf("-offset not found.\n");
  } else {
    printf("-offset found:\n");
    if( !cmd.offsetC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%ld'\n", cmd.offset);
    }
  }

  /***** -start: Starting point of the processing as a fraction of the full obs */
  if( !cmd.startP ) {
    printf("-start not found.\n");
  } else {
    printf("-start found:\n");
    if( !cmd.startC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%.40g'\n", cmd.start);
    }
  }

  /***** -lodm: The lowest dispersion measure to de-disperse (cm^-3 pc) */
  if( !cmd.lodmP ) {
    printf("-lodm not found.\n");
  } else {
    printf("-lodm found:\n");
    if( !cmd.lodmC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%.40g'\n", cmd.lodm);
    }
  }

  /***** -dmstep: The stepsize in dispersion measure to use(cm^-3 pc) */
  if( !cmd.dmstepP ) {
    printf("-dmstep not found.\n");
  } else {
    printf("-dmstep found:\n");
    if( !cmd.dmstepC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%.40g'\n", cmd.dmstep);
    }
  }

  /***** -numdms: The number of DMs to de-disperse */
  if( !cmd.numdmsP ) {
    printf("-numdms not found.\n");
  } else {
    printf("-numdms found:\n");
    if( !cmd.numdmsC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%d'\n", cmd.numdms);
    }
  }

  /***** -nsub: The number of sub-bands to use */
  if( !cmd.nsubP ) {
    printf("-nsub not found.\n");
  } else {
    printf("-nsub found:\n");
    if( !cmd.nsubC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%d'\n", cmd.nsub);
    }
  }

  /***** -downsamp: The number of neighboring bins to co-add */
  if( !cmd.downsampP ) {
    printf("-downsamp not found.\n");
  } else {
    printf("-downsamp found:\n");
    if( !cmd.downsampC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%d'\n", cmd.downsamp);
    }
  }

  /***** -dmprec: The number of decimals in the precision of the DM in the filename. */
  if( !cmd.dmprecP ) {
    printf("-dmprec not found.\n");
  } else {
    printf("-dmprec found:\n");
    if( !cmd.dmprecC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%d'\n", cmd.dmprec);
    }
  }

  /***** -mask: File containing masking information to use */
  if( !cmd.maskfileP ) {
    printf("-mask not found.\n");
  } else {
    printf("-mask found:\n");
    if( !cmd.maskfileC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%s'\n", cmd.maskfile);
    }
  }

  /***** -ignorechan: Comma separated string (no spaces!) of channels to ignore (or file containing such string).  Ranges are specified by min:max[:step] */
  if( !cmd.ignorechanstrP ) {
    printf("-ignorechan not found.\n");
  } else {
    printf("-ignorechan found:\n");
    if( !cmd.ignorechanstrC ) {
      printf("  no values\n");
    } else {
      printf("  value = `%s'\n", cmd.ignorechanstr);
    }
  }
  if( !cmd.argc ) {
    printf("no remaining parameters in argv\n");
  } else {
    printf("argv =");
    for(i=0; i<cmd.argc; i++) {
      printf(" `%s'", cmd.argv[i]);
    }
    printf("\n");
  }
}
/**********************************************************************/

void
usage(void)
{
  fprintf(stderr,"%s","   [-ncpus ncpus] -o outfile [-filterbank] [-psrfits] [-noweights] [-noscales] [-nooffsets] [-wapp] [-window] [-numwapps numwapps] [-if ifs] [-clip clip] [-noclip] [-invert] [-zerodm] [-runavg] [-sub] [-subdm subdm] [-numout numout] [-nobary] [-offset offset] [-start start] [-lodm lodm] [-dmstep dmstep] [-numdms numdms] [-nsub nsub] [-downsamp downsamp] [-dmprec dmprec] [-mask maskfile] [-ignorechan ignorechanstr] [--] infile ...\n");
  fprintf(stderr,"%s","      Converts a raw radio data file into many de-dispersed time-series (including barycentering).\n");
  fprintf(stderr,"%s","         -ncpus: Number of processors to use with OpenMP\n");
  fprintf(stderr,"%s","                 1 int value between 1 and oo\n");
  fprintf(stderr,"%s","                 default: `1'\n");
  fprintf(stderr,"%s","             -o: Root of the output file names\n");
  fprintf(stderr,"%s","                 1 char* value\n");
  fprintf(stderr,"%s","    -filterbank: Raw data in SIGPROC filterbank format\n");
  fprintf(stderr,"%s","       -psrfits: Raw data in PSRFITS format\n");
  fprintf(stderr,"%s","     -noweights: Do not apply PSRFITS weights\n");
  fprintf(stderr,"%s","      -noscales: Do not apply PSRFITS scales\n");
  fprintf(stderr,"%s","     -nooffsets: Do not apply PSRFITS offsets\n");
  fprintf(stderr,"%s","          -wapp: Raw data in Wideband Arecibo Pulsar Processor (WAPP) format\n");
  fprintf(stderr,"%s","        -window: Window correlator lags with a Hamming window before FFTing\n");
  fprintf(stderr,"%s","      -numwapps: Number of WAPPs used with contiguous frequencies\n");
  fprintf(stderr,"%s","                 1 int value between 1 and 8\n");
  fprintf(stderr,"%s","                 default: `1'\n");
  fprintf(stderr,"%s","            -if: A specific IF to use if available (summed IFs is the default)\n");
  fprintf(stderr,"%s","                 1 int value between 0 and 1\n");
  fprintf(stderr,"%s","          -clip: Time-domain sigma to use for clipping (0.0 = no clipping, 6.0 = default\n");
  fprintf(stderr,"%s","                 1 float value between 0 and 1000.0\n");
  fprintf(stderr,"%s","                 default: `6.0'\n");
  fprintf(stderr,"%s","        -noclip: Do not clip the data.  (The default is to _always_ clip!)\n");
  fprintf(stderr,"%s","        -invert: For rawdata, flip (or invert) the band\n");
  fprintf(stderr,"%s","        -zerodm: Subtract the mean of all channels from each sample (i.e. remove zero DM)\n");
  fprintf(stderr,"%s","        -runavg: Running mean subtraction from the input data\n");
  fprintf(stderr,"%s","           -sub: Write subbands instead of de-dispersed data\n");
  fprintf(stderr,"%s","         -subdm: The DM to use when de-dispersing subbands for -sub\n");
  fprintf(stderr,"%s","                 1 double value between 0 and 4000.0\n");
  fprintf(stderr,"%s","                 default: `0.0'\n");
  fprintf(stderr,"%s","        -numout: Output this many values.  If there are not enough values in the original data file, will pad the output file with the average value\n");
  fprintf(stderr,"%s","                 1 long value between 1 and oo\n");
  fprintf(stderr,"%s","        -nobary: Do not barycenter the data\n");
  fprintf(stderr,"%s","        -offset: Number of spectra to offset into as starting data point\n");
  fprintf(stderr,"%s","                 1 long value between 0 and oo\n");
  fprintf(stderr,"%s","                 default: `0'\n");
  fprintf(stderr,"%s","         -start: Starting point of the processing as a fraction of the full obs\n");
  fprintf(stderr,"%s","                 1 double value between 0.0 and 1.0\n");
  fprintf(stderr,"%s","                 default: `0.0'\n");
  fprintf(stderr,"%s","          -lodm: The lowest dispersion measure to de-disperse (cm^-3 pc)\n");
  fprintf(stderr,"%s","                 1 double value between 0 and oo\n");
  fprintf(stderr,"%s","                 default: `0'\n");
  fprintf(stderr,"%s","        -dmstep: The stepsize in dispersion measure to use(cm^-3 pc)\n");
  fprintf(stderr,"%s","                 1 double value between 0 and oo\n");
  fprintf(stderr,"%s","                 default: `1.0'\n");
  fprintf(stderr,"%s","        -numdms: The number of DMs to de-disperse\n");
  fprintf(stderr,"%s","                 1 int value between 1 and 10000\n");
  fprintf(stderr,"%s","                 default: `10'\n");
  fprintf(stderr,"%s","          -nsub: The number of sub-bands to use\n");
  fprintf(stderr,"%s","                 1 int value between 1 and 4096\n");
  fprintf(stderr,"%s","                 default: `32'\n");
  fprintf(stderr,"%s","      -downsamp: The number of neighboring bins to co-add\n");
  fprintf(stderr,"%s","                 1 int value between 1 and 128\n");
  fprintf(stderr,"%s","                 default: `1'\n");
  fprintf(stderr,"%s","        -dmprec: The number of decimals in the precision of the DM in the filename.\n");
  fprintf(stderr,"%s","                 1 int value between 2 and 4\n");
  fprintf(stderr,"%s","                 default: `2'\n");
  fprintf(stderr,"%s","          -mask: File containing masking information to use\n");
  fprintf(stderr,"%s","                 1 char* value\n");
  fprintf(stderr,"%s","    -ignorechan: Comma separated string (no spaces!) of channels to ignore (or file containing such string).  Ranges are specified by min:max[:step]\n");
  fprintf(stderr,"%s","                 1 char* value\n");
  fprintf(stderr,"%s","         infile: Input data file name.  If the data is not in a known raw format, it should be a single channel of single-precision floating point data.  In this case a '.inf' file with the same root filename must also exist (Note that this means that the input data file must have a suffix that starts with a period)\n");
  fprintf(stderr,"%s","                 1...16384 values\n");
  fprintf(stderr,"%s","  version: 28Jun17\n");
  fprintf(stderr,"%s","  ");
  exit(EXIT_FAILURE);
}
/**********************************************************************/
Cmdline *
parseCmdline(int argc, char **argv)
{
  int i;
  char missingMandatory = 0;

  Program = argv[0];
  cmd.full_cmd_line = catArgv(argc, argv);
  for(i=1, cmd.argc=1; i<argc; i++) {
    if( 0==strcmp("--", argv[i]) ) {
      while( ++i<argc ) argv[cmd.argc++] = argv[i];
      continue;
    }

    if( 0==strcmp("-ncpus", argv[i]) ) {
      int keep = i;
      cmd.ncpusP = 1;
      i = getIntOpt(argc, argv, i, &cmd.ncpus, 1);
      cmd.ncpusC = i-keep;
      checkIntHigher("-ncpus", &cmd.ncpus, cmd.ncpusC, 1);
      continue;
    }

    if( 0==strcmp("-o", argv[i]) ) {
      int keep = i;
      cmd.outfileP = 1;
      i = getStringOpt(argc, argv, i, &cmd.outfile, 1);
      cmd.outfileC = i-keep;
      continue;
    }

    if( 0==strcmp("-filterbank", argv[i]) ) {
      cmd.filterbankP = 1;
      continue;
    }

    if( 0==strcmp("-psrfits", argv[i]) ) {
      cmd.psrfitsP = 1;
      continue;
    }

    if( 0==strcmp("-noweights", argv[i]) ) {
      cmd.noweightsP = 1;
      continue;
    }

    if( 0==strcmp("-noscales", argv[i]) ) {
      cmd.noscalesP = 1;
      continue;
    }

    if( 0==strcmp("-nooffsets", argv[i]) ) {
      cmd.nooffsetsP = 1;
      continue;
    }

    if( 0==strcmp("-wapp", argv[i]) ) {
      cmd.wappP = 1;
      continue;
    }

    if( 0==strcmp("-window", argv[i]) ) {
      cmd.windowP = 1;
      continue;
    }

    if( 0==strcmp("-numwapps", argv[i]) ) {
      int keep = i;
      cmd.numwappsP = 1;
      i = getIntOpt(argc, argv, i, &cmd.numwapps, 1);
      cmd.numwappsC = i-keep;
      checkIntLower("-numwapps", &cmd.numwapps, cmd.numwappsC, 8);
      checkIntHigher("-numwapps", &cmd.numwapps, cmd.numwappsC, 1);
      continue;
    }

    if( 0==strcmp("-if", argv[i]) ) {
      int keep = i;
      cmd.ifsP = 1;
      i = getIntOpt(argc, argv, i, &cmd.ifs, 1);
      cmd.ifsC = i-keep;
      checkIntLower("-if", &cmd.ifs, cmd.ifsC, 1);
      checkIntHigher("-if", &cmd.ifs, cmd.ifsC, 0);
      continue;
    }

    if( 0==strcmp("-clip", argv[i]) ) {
      int keep = i;
      cmd.clipP = 1;
      i = getFloatOpt(argc, argv, i, &cmd.clip, 1);
      cmd.clipC = i-keep;
      checkFloatLower("-clip", &cmd.clip, cmd.clipC, 1000.0);
      checkFloatHigher("-clip", &cmd.clip, cmd.clipC, 0);
      continue;
    }

    if( 0==strcmp("-noclip", argv[i]) ) {
      cmd.noclipP = 1;
      continue;
    }

    if( 0==strcmp("-invert", argv[i]) ) {
      cmd.invertP = 1;
      continue;
    }

    if( 0==strcmp("-zerodm", argv[i]) ) {
      cmd.zerodmP = 1;
      continue;
    }

    if( 0==strcmp("-runavg", argv[i]) ) {
      cmd.runavgP = 1;
      continue;
    }

    if( 0==strcmp("-sub", argv[i]) ) {
      cmd.subP = 1;
      continue;
    }

    if( 0==strcmp("-subdm", argv[i]) ) {
      int keep = i;
      cmd.subdmP = 1;
      i = getDoubleOpt(argc, argv, i, &cmd.subdm, 1);
      cmd.subdmC = i-keep;
      checkDoubleLower("-subdm", &cmd.subdm, cmd.subdmC, 4000.0);
      checkDoubleHigher("-subdm", &cmd.subdm, cmd.subdmC, 0);
      continue;
    }

    if( 0==strcmp("-numout", argv[i]) ) {
      int keep = i;
      cmd.numoutP = 1;
      i = getLongOpt(argc, argv, i, &cmd.numout, 1);
      cmd.numoutC = i-keep;
      checkLongHigher("-numout", &cmd.numout, cmd.numoutC, 1);
      continue;
    }

    if( 0==strcmp("-nobary", argv[i]) ) {
      cmd.nobaryP = 1;
      continue;
    }

    if( 0==strcmp("-offset", argv[i]) ) {
      int keep = i;
      cmd.offsetP = 1;
      i = getLongOpt(argc, argv, i, &cmd.offset, 1);
      cmd.offsetC = i-keep;
      checkLongHigher("-offset", &cmd.offset, cmd.offsetC, 0);
      continue;
    }

    if( 0==strcmp("-start", argv[i]) ) {
      int keep = i;
      cmd.startP = 1;
      i = getDoubleOpt(argc, argv, i, &cmd.start, 1);
      cmd.startC = i-keep;
      checkDoubleLower("-start", &cmd.start, cmd.startC, 1.0);
      checkDoubleHigher("-start", &cmd.start, cmd.startC, 0.0);
      continue;
    }

    if( 0==strcmp("-lodm", argv[i]) ) {
      int keep = i;
      cmd.lodmP = 1;
      i = getDoubleOpt(argc, argv, i, &cmd.lodm, 1);
      cmd.lodmC = i-keep;
      checkDoubleHigher("-lodm", &cmd.lodm, cmd.lodmC, 0);
      continue;
    }

    if( 0==strcmp("-dmstep", argv[i]) ) {
      int keep = i;
      cmd.dmstepP = 1;
      i = getDoubleOpt(argc, argv, i, &cmd.dmstep, 1);
      cmd.dmstepC = i-keep;
      checkDoubleHigher("-dmstep", &cmd.dmstep, cmd.dmstepC, 0);
      continue;
    }

    if( 0==strcmp("-numdms", argv[i]) ) {
      int keep = i;
      cmd.numdmsP = 1;
      i = getIntOpt(argc, argv, i, &cmd.numdms, 1);
      cmd.numdmsC = i-keep;
      checkIntLower("-numdms", &cmd.numdms, cmd.numdmsC, 10000);
      checkIntHigher("-numdms", &cmd.numdms, cmd.numdmsC, 1);
      continue;
    }

    if( 0==strcmp("-nsub", argv[i]) ) {
      int keep = i;
      cmd.nsubP = 1;
      i = getIntOpt(argc, argv, i, &cmd.nsub, 1);
      cmd.nsubC = i-keep;
      checkIntLower("-nsub", &cmd.nsub, cmd.nsubC, 4096);
      checkIntHigher("-nsub", &cmd.nsub, cmd.nsubC, 1);
      continue;
    }

    if( 0==strcmp("-downsamp", argv[i]) ) {
      int keep = i;
      cmd.downsampP = 1;
      i = getIntOpt(argc, argv, i, &cmd.downsamp, 1);
      cmd.downsampC = i-keep;
      checkIntLower("-downsamp", &cmd.downsamp, cmd.downsampC, 128);
      checkIntHigher("-downsamp", &cmd.downsamp, cmd.downsampC, 1);
      continue;
    }

    if( 0==strcmp("-dmprec", argv[i]) ) {
      int keep = i;
      cmd.dmprecP = 1;
      i = getIntOpt(argc, argv, i, &cmd.dmprec, 1);
      cmd.dmprecC = i-keep;
      checkIntLower("-dmprec", &cmd.dmprec, cmd.dmprecC, 4);
      checkIntHigher("-dmprec", &cmd.dmprec, cmd.dmprecC, 2);
      continue;
    }

    if( 0==strcmp("-mask", argv[i]) ) {
      int keep = i;
      cmd.maskfileP = 1;
      i = getStringOpt(argc, argv, i, &cmd.maskfile, 1);
      cmd.maskfileC = i-keep;
      continue;
    }

    if( 0==strcmp("-ignorechan", argv[i]) ) {
      int keep = i;
      cmd.ignorechanstrP = 1;
      i = getStringOpt(argc, argv, i, &cmd.ignorechanstr, 1);
      cmd.ignorechanstrC = i-keep;
      continue;
    }

    if( argv[i][0]=='-' ) {
      fprintf(stderr, "\n%s: unknown option `%s'\n\n",
              Program, argv[i]);
      usage();
    }
    argv[cmd.argc++] = argv[i];
  }/* for i */

  if( !cmd.outfileP ) {
    missingErr("-o");
    missingMandatory = 1;
  }
  if( missingMandatory ) exit(EXIT_FAILURE);

  /*@-mustfree*/
  cmd.argv = argv+1;
  /*@=mustfree*/
  cmd.argc -= 1;

  if( 1>cmd.argc ) {
    fprintf(stderr, "%s: there should be at least 1 non-option argument(s)\n",
            Program);
    exit(EXIT_FAILURE);
  }
  if( 16384<cmd.argc ) {
    fprintf(stderr, "%s: there should be at most 16384 non-option argument(s)\n",
            Program);
    exit(EXIT_FAILURE);
  }
  /*@-compmempass*/  return &cmd;
}

