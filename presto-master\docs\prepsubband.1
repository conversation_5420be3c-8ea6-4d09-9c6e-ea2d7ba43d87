.\" clig manual page template
.\" (C) 1995 <PERSON> (<EMAIL>)
.\"
.\" This file was generated by
.\" clig -- command line interface generator
.\"
.\"
.\" Clig will always edit the lines between pairs of `cligPart ...',
.\" but will not complain, if a pair is missing. So, if you want to
.\" make up a certain part of the manual page by hand rather than have
.\" it edited by clig, remove the respective pair of cligPart-lines.
.\"
.\" cligPart TITLE
.TH "prepsubband" 1 "28Jun17" "Clig-manuals" "Programmer's Manual"
.\" cligPart TITLE end

.\" cligPart NAME
.SH NAME
prepsubband \- Converts a raw radio data file into many de-dispersed time-series (including barycentering).
.\" cligPart NAME end

.\" cligPart SYNOPSIS
.SH SYNOPSIS
.B prepsubband
[-ncpus ncpus]
-o outfile
[-filterbank]
[-psrfits]
[-noweights]
[-noscales]
[-nooffsets]
[-wapp]
[-window]
[-numwapps numwapps]
[-if ifs]
[-clip clip]
[-noclip]
[-invert]
[-zerodm]
[-runavg]
[-sub]
[-subdm subdm]
[-numout numout]
[-nobary]
[-offset offset]
[-start start]
[-lodm lodm]
[-dmstep dmstep]
[-numdms numdms]
[-nsub nsub]
[-downsamp downsamp]
[-dmprec dmprec]
[-mask maskfile]
[-ignorechan ignorechanstr]
infile ...
.\" cligPart SYNOPSIS end

.\" cligPart OPTIONS
.SH OPTIONS
.IP -ncpus
Number of processors to use with OpenMP,
.br
1 Int value between 1 and oo.
.br
Default: `1'
.IP -o
Root of the output file names,
.br
1 String value
.IP -filterbank
Raw data in SIGPROC filterbank format.
.IP -psrfits
Raw data in PSRFITS format.
.IP -noweights
Do not apply PSRFITS weights.
.IP -noscales
Do not apply PSRFITS scales.
.IP -nooffsets
Do not apply PSRFITS offsets.
.IP -wapp
Raw data in Wideband Arecibo Pulsar Processor (WAPP) format.
.IP -window
Window correlator lags with a Hamming window before FFTing.
.IP -numwapps
Number of WAPPs used with contiguous frequencies,
.br
1 Int value between 1 and 8.
.br
Default: `1'
.IP -if
A specific IF to use if available (summed IFs is the default),
.br
1 Int value between 0 and 1.
.IP -clip
Time-domain sigma to use for clipping (0.0 = no clipping, 6.0 = default,
.br
1 Float value between 0 and 1000.0.
.br
Default: `6.0'
.IP -noclip
Do not clip the data.  (The default is to _always_ clip!).
.IP -invert
For rawdata, flip (or invert) the band.
.IP -zerodm
Subtract the mean of all channels from each sample (i.e. remove zero DM).
.IP -runavg
Running mean subtraction from the input data.
.IP -sub
Write subbands instead of de-dispersed data.
.IP -subdm
The DM to use when de-dispersing subbands for -sub,
.br
1 Double value between 0 and 4000.0.
.br
Default: `0.0'
.IP -numout
Output this many values.  If there are not enough values in the original data file, will pad the output file with the average value,
.br
1 Long value between 1 and oo.
.IP -nobary
Do not barycenter the data.
.IP -offset
Number of spectra to offset into as starting data point,
.br
1 Long value between 0 and oo.
.br
Default: `0'
.IP -start
Starting point of the processing as a fraction of the full obs,
.br
1 Double value between 0.0 and 1.0.
.br
Default: `0.0'
.IP -lodm
The lowest dispersion measure to de-disperse (cm^-3 pc),
.br
1 Double value between 0 and oo.
.br
Default: `0'
.IP -dmstep
The stepsize in dispersion measure to use(cm^-3 pc),
.br
1 Double value between 0 and oo.
.br
Default: `1.0'
.IP -numdms
The number of DMs to de-disperse,
.br
1 Int value between 1 and 10000.
.br
Default: `10'
.IP -nsub
The number of sub-bands to use,
.br
1 Int value between 1 and 4096.
.br
Default: `32'
.IP -downsamp
The number of neighboring bins to co-add,
.br
1 Int value between 1 and 128.
.br
Default: `1'
.IP -dmprec
The number of decimals in the precision of the DM in the filename.,
.br
1 Int value between 2 and 4.
.br
Default: `2'
.IP -mask
File containing masking information to use,
.br
1 String value
.IP -ignorechan
Comma separated string (no spaces!) of channels to ignore (or file containing such string).  Ranges are specified by min:max[:step],
.br
1 String value
.IP infile
Input data file name.  If the data is not in a known raw format, it should be a single channel of single-precision floating point data.  In this case a '.inf' file with the same root filename must also exist (Note that this means that the input data file must have a suffix that starts with a period).
.\" cligPart OPTIONS end

.\" cligPart DESCRIPTION
.SH DESCRIPTION
This manual page was generated automagically by clig, the
Command Line Interface Generator. Actually the programmer
using clig was supposed to edit this part of the manual
page after
generating it with clig, but obviously (s)he didn't.

Sadly enough clig does not yet have the power to pick a good
program description out of blue air ;-(
.\" cligPart DESCRIPTION end
