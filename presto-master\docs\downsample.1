.\" clig manual page template
.\" (C) 1995-2001 <PERSON> (<EMAIL>)
.\"
.\" This file was generated by
.\" clig -- command line interface generator
.\"
.\"
.\" Clig will always edit the lines between pairs of `cligPart ...',
.\" but will not complain, if a pair is missing. So, if you want to
.\" make up a certain part of the manual page by hand rather than have
.\" it edited by clig, remove the respective pair of cligPart-lines.
.\"
.\" cligPart TITLE
.TH "downsample" 1 "12Mar10" "Clig-manuals" "Programmer's Manual"
.\" cligPart TITLE end

.\" cligPart NAME
.SH NAME
downsample \- Downsamples a time series.
.\" cligPart NAME end

.\" cligPart SYNOPSIS
.SH SYNOPSIS
.B downsample
-factor factor
[-o outfile]
file
.\" cligPart SYNOPSIS end

.\" cligPart OPTIONS
.SH OPTIONS
.IP -factor
The factor to downsample the data,
.br
1 Int value between 1 and 100.
.IP -o
Name of the output time series file (with suffix),
.br
1 String value
.IP file
Input '.dat' or '.sdat' file name.
.\" cligPart OPTIONS end

.\" cligPart DESCRIPTION
.SH DESCRIPTION
This manual page was generated automagically by clig, the
Command Line Interface Generator. Actually the programmer
using clig was supposed to edit this part of the manual
page after
generating it with clig, but obviously (s)he didn't.

Sadly enough clig does not yet have the power to pick a good
program description out of blue air ;-(
.\" cligPart DESCRIPTION end
